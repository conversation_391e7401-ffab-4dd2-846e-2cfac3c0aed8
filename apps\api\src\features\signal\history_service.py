"""Service for managing historical signal data and effectiveness calculations."""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Tuple

import numpy as np
from sqlalchemy import and_, desc, func
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...models.signal_history import SignalEffectiveness, SignalHistory
from ...models.signal_history_schemas import (
    SignalEffectivenessResponse,
    SignalHistoryCreate,
    SignalHistoryFilter,
    SignalHistoryResponse,
    SignalHistoryUpdate,
    SignalPerformanceMetrics,
)

logger = logging.getLogger(__name__)


class SignalHistoryService:
    """Service for managing historical signal data and effectiveness calculations."""

    def __init__(self, db: Session):
        """Initialize the service with database session."""
        self.db = db

    def create_signal_record(self, signal_data: SignalHistoryCreate) -> SignalHistoryResponse:
        """Create a new signal history record."""
        try:
            db_signal = SignalHistory(
                symbol=signal_data.symbol,
                signal_type=signal_data.signal_type,
                signal_strength=signal_data.signal_strength,
                trigger_price=signal_data.trigger_price,
                trigger_date=signal_data.trigger_date,
                strategy_name=signal_data.strategy_name,
                strategy_params=signal_data.strategy_params,
                volume=signal_data.volume,
                market_cap=signal_data.market_cap,
                is_successful='PENDING'
            )
            
            self.db.add(db_signal)
            self.db.commit()
            self.db.refresh(db_signal)
            
            logger.info(f"Created signal record for {signal_data.symbol}: {signal_data.signal_type}")
            return SignalHistoryResponse.model_validate(db_signal)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating signal record: {str(e)}")
            raise RuntimeError(f"Failed to create signal record: {str(e)}")

    def update_signal_outcome(self, signal_id: int, update_data: SignalHistoryUpdate) -> SignalHistoryResponse:
        """Update a signal record with outcome data."""
        try:
            db_signal = self.db.query(SignalHistory).filter(SignalHistory.id == signal_id).first()
            if not db_signal:
                raise ValueError(f"Signal record with id {signal_id} not found")
            
            # Update fields if provided
            if update_data.exit_price is not None:
                db_signal.exit_price = update_data.exit_price
            if update_data.exit_date is not None:
                db_signal.exit_date = update_data.exit_date
            if update_data.return_percentage is not None:
                db_signal.return_percentage = update_data.return_percentage
            if update_data.holding_period_days is not None:
                db_signal.holding_period_days = update_data.holding_period_days
            if update_data.is_successful is not None:
                db_signal.is_successful = update_data.is_successful
            
            # Calculate return percentage if exit price is provided
            if update_data.exit_price is not None and db_signal.trigger_price:
                if db_signal.signal_type == 'BUY':
                    db_signal.return_percentage = ((update_data.exit_price - db_signal.trigger_price) / db_signal.trigger_price) * 100
                elif db_signal.signal_type == 'SELL':
                    db_signal.return_percentage = ((db_signal.trigger_price - update_data.exit_price) / db_signal.trigger_price) * 100
                
                # Determine success based on return
                if db_signal.return_percentage > 0:
                    db_signal.is_successful = 'SUCCESS'
                else:
                    db_signal.is_successful = 'LOSS'
            
            # Calculate holding period if exit date is provided
            if update_data.exit_date is not None and db_signal.trigger_date:
                db_signal.holding_period_days = (update_data.exit_date - db_signal.trigger_date).days
            
            self.db.commit()
            self.db.refresh(db_signal)
            
            logger.info(f"Updated signal record {signal_id} with outcome data")
            return SignalHistoryResponse.model_validate(db_signal)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating signal record: {str(e)}")
            raise RuntimeError(f"Failed to update signal record: {str(e)}")

    def get_signal_history(self, filters: SignalHistoryFilter) -> Tuple[List[SignalHistoryResponse], int]:
        """Get filtered signal history with pagination."""
        try:
            query = self.db.query(SignalHistory)
            
            # Apply filters
            if filters.symbol:
                query = query.filter(SignalHistory.symbol == filters.symbol)
            if filters.signal_type:
                query = query.filter(SignalHistory.signal_type == filters.signal_type)
            if filters.strategy_name:
                query = query.filter(SignalHistory.strategy_name == filters.strategy_name)
            if filters.start_date:
                query = query.filter(SignalHistory.trigger_date >= filters.start_date)
            if filters.end_date:
                query = query.filter(SignalHistory.trigger_date <= filters.end_date)
            if filters.is_successful:
                query = query.filter(SignalHistory.is_successful == filters.is_successful)
            
            # Get total count before pagination
            total_count = query.count()
            
            # Apply pagination and ordering
            signals = query.order_by(desc(SignalHistory.trigger_date)).offset(filters.offset).limit(filters.limit).all()
            
            signal_responses = [SignalHistoryResponse.model_validate(signal) for signal in signals]
            
            logger.info(f"Retrieved {len(signal_responses)} signal records (total: {total_count})")
            return signal_responses, total_count
            
        except Exception as e:
            logger.error(f"Error retrieving signal history: {str(e)}")
            raise RuntimeError(f"Failed to retrieve signal history: {str(e)}")

    def calculate_effectiveness_metrics(self, symbol: str, signal_type: str, period_days: int = 90) -> SignalEffectivenessResponse:
        """Calculate effectiveness metrics for a symbol and signal type over a period."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days)
            
            # Get completed signals in the period
            signals = self.db.query(SignalHistory).filter(
                and_(
                    SignalHistory.symbol == symbol,
                    SignalHistory.signal_type == signal_type,
                    SignalHistory.trigger_date >= start_date,
                    SignalHistory.trigger_date <= end_date,
                    SignalHistory.is_successful.in_(['SUCCESS', 'LOSS'])
                )
            ).all()
            
            if not signals:
                # Return default metrics if no signals found
                return self._create_default_effectiveness_metrics(symbol, signal_type, start_date, end_date)
            
            # Calculate basic metrics
            total_signals = len(signals)
            successful_signals = len([s for s in signals if s.is_successful == 'SUCCESS'])
            success_rate = successful_signals / total_signals if total_signals > 0 else 0.0
            
            # Calculate return metrics
            returns = [s.return_percentage for s in signals if s.return_percentage is not None]
            if returns:
                avg_return = np.mean(returns)
                max_return = np.max(returns)
                min_return = np.min(returns)
                total_return = np.sum(returns)
                volatility = np.std(returns)
            else:
                avg_return = max_return = min_return = total_return = volatility = 0.0
            
            # Calculate holding period metrics
            holding_periods = [s.holding_period_days for s in signals if s.holding_period_days is not None]
            avg_holding_period = np.mean(holding_periods) if holding_periods else 0.0
            
            # Calculate maximum drawdown
            max_drawdown = self._calculate_max_drawdown(returns) if returns else 0.0
            
            # Create or update effectiveness record
            effectiveness = SignalEffectiveness(
                symbol=symbol,
                signal_type=signal_type,
                strategy_name="magic_nine_macd",
                period_start=start_date,
                period_end=end_date,
                total_signals=total_signals,
                successful_signals=successful_signals,
                success_rate=success_rate,
                avg_return=avg_return,
                max_return=max_return,
                min_return=min_return,
                total_return=total_return,
                max_drawdown=max_drawdown,
                avg_holding_period=avg_holding_period,
                volatility=volatility
            )
            
            # Check if record exists and update or create
            existing = self.db.query(SignalEffectiveness).filter(
                and_(
                    SignalEffectiveness.symbol == symbol,
                    SignalEffectiveness.signal_type == signal_type,
                    SignalEffectiveness.strategy_name == "magic_nine_macd"
                )
            ).first()
            
            if existing:
                for key, value in effectiveness.__dict__.items():
                    if not key.startswith('_') and key not in ['id', 'created_at']:
                        setattr(existing, key, value)
                self.db.commit()
                self.db.refresh(existing)
                result = existing
            else:
                self.db.add(effectiveness)
                self.db.commit()
                self.db.refresh(effectiveness)
                result = effectiveness
            
            logger.info(f"Calculated effectiveness metrics for {symbol} {signal_type}: {success_rate:.2%} success rate")
            return SignalEffectivenessResponse.model_validate(result)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error calculating effectiveness metrics: {str(e)}")
            raise RuntimeError(f"Failed to calculate effectiveness metrics: {str(e)}")

    def get_performance_metrics(self, symbol: str, timeframe: str = "30d") -> SignalPerformanceMetrics:
        """Get comprehensive performance metrics for a symbol."""
        try:
            end_date = datetime.now()
            period_days = self._parse_timeframe(timeframe)
            start_date = end_date - timedelta(days=period_days)
            
            # Get all completed signals in the period
            signals = self.db.query(SignalHistory).filter(
                and_(
                    SignalHistory.symbol == symbol,
                    SignalHistory.trigger_date >= start_date,
                    SignalHistory.trigger_date <= end_date,
                    SignalHistory.is_successful.in_(['SUCCESS', 'LOSS'])
                )
            ).order_by(SignalHistory.trigger_date).all()
            
            if not signals:
                return self._create_default_performance_metrics(symbol)
            
            # Basic counts
            total_signals = len(signals)
            buy_signals = len([s for s in signals if s.signal_type == 'BUY'])
            sell_signals = len([s for s in signals if s.signal_type == 'SELL'])
            
            # Success rates
            successful_signals = [s for s in signals if s.is_successful == 'SUCCESS']
            overall_success_rate = len(successful_signals) / total_signals if total_signals > 0 else 0.0
            
            buy_successful = len([s for s in successful_signals if s.signal_type == 'BUY'])
            sell_successful = len([s for s in successful_signals if s.signal_type == 'SELL'])
            
            buy_success_rate = buy_successful / buy_signals if buy_signals > 0 else 0.0
            sell_success_rate = sell_successful / sell_signals if sell_signals > 0 else 0.0
            
            # Return metrics
            returns = [s.return_percentage for s in signals if s.return_percentage is not None]
            total_return = sum(returns) if returns else 0.0
            avg_return_per_signal = total_return / len(returns) if returns else 0.0
            
            # Consecutive wins/losses
            max_consecutive_wins, max_consecutive_losses = self._calculate_consecutive_streaks(signals)
            
            # Holding period
            holding_periods = [s.holding_period_days for s in signals if s.holding_period_days is not None]
            avg_holding_period = np.mean(holding_periods) if holding_periods else 0.0
            
            # Sharpe ratio (simplified)
            sharpe_ratio = None
            if returns and len(returns) > 1:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                if std_return > 0:
                    sharpe_ratio = mean_return / std_return
            
            # Win/loss ratio
            losses = len([s for s in signals if s.is_successful == 'LOSS'])
            win_loss_ratio = len(successful_signals) / losses if losses > 0 else float('inf')
            
            return SignalPerformanceMetrics(
                symbol=symbol,
                total_signals=total_signals,
                buy_signals=buy_signals,
                sell_signals=sell_signals,
                overall_success_rate=overall_success_rate,
                buy_success_rate=buy_success_rate,
                sell_success_rate=sell_success_rate,
                total_return=total_return,
                avg_return_per_signal=avg_return_per_signal,
                max_consecutive_wins=max_consecutive_wins,
                max_consecutive_losses=max_consecutive_losses,
                avg_holding_period=avg_holding_period,
                sharpe_ratio=sharpe_ratio,
                win_loss_ratio=win_loss_ratio
            )
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {str(e)}")
            raise RuntimeError(f"Failed to calculate performance metrics: {str(e)}")

    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """Calculate maximum drawdown from a series of returns."""
        if not returns:
            return 0.0
        
        cumulative = np.cumprod([1 + r/100 for r in returns])
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return abs(np.min(drawdown)) * 100

    def _calculate_consecutive_streaks(self, signals: List[SignalHistory]) -> Tuple[int, int]:
        """Calculate maximum consecutive wins and losses."""
        if not signals:
            return 0, 0
        
        max_wins = max_losses = current_wins = current_losses = 0
        
        for signal in signals:
            if signal.is_successful == 'SUCCESS':
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            elif signal.is_successful == 'LOSS':
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
        
        return max_wins, max_losses

    def _create_default_effectiveness_metrics(self, symbol: str, signal_type: str, start_date: datetime, end_date: datetime) -> SignalEffectivenessResponse:
        """Create default effectiveness metrics when no data is available."""
        return SignalEffectivenessResponse(
            id=0,
            symbol=symbol,
            signal_type=signal_type,
            strategy_name="magic_nine_macd",
            period_start=start_date,
            period_end=end_date,
            total_signals=0,
            successful_signals=0,
            success_rate=0.0,
            avg_return=0.0,
            max_return=0.0,
            min_return=0.0,
            total_return=0.0,
            max_drawdown=0.0,
            avg_holding_period=0.0,
            volatility=0.0,
            benchmark_return=None,
            alpha=None,
            last_calculated=datetime.now()
        )

    def _create_default_performance_metrics(self, symbol: str) -> SignalPerformanceMetrics:
        """Create default performance metrics when no data is available."""
        return SignalPerformanceMetrics(
            symbol=symbol,
            total_signals=0,
            buy_signals=0,
            sell_signals=0,
            overall_success_rate=0.0,
            buy_success_rate=0.0,
            sell_success_rate=0.0,
            total_return=0.0,
            avg_return_per_signal=0.0,
            max_consecutive_wins=0,
            max_consecutive_losses=0,
            avg_holding_period=0.0,
            sharpe_ratio=None,
            win_loss_ratio=0.0
        )

    def get_available_strategies(self) -> List[str]:
        """Get list of available trading strategies from signal history."""
        try:
            strategies = self.db.query(SignalHistory.strategy_name).distinct().all()
            strategy_list = [strategy[0] for strategy in strategies if strategy[0]]
            
            # Add default strategies if none found
            if not strategy_list:
                strategy_list = ["magic_nine_macd", "rsi_divergence", "bollinger_bands"]
            
            logger.info(f"Retrieved {len(strategy_list)} available strategies")
            return sorted(strategy_list)
            
        except Exception as e:
            logger.error(f"Error fetching available strategies: {str(e)}")
            raise RuntimeError(f"Failed to fetch available strategies: {str(e)}")

    def compare_strategies(self, symbol: str, strategies: List[str], timeframe: str = "30d") -> List[dict]:
        """Compare performance of different strategies for a symbol."""
        try:
            end_date = datetime.now()
            period_days = self._parse_timeframe(timeframe)
            start_date = end_date - timedelta(days=period_days)
            
            comparison_results = []
            
            for strategy in strategies:
                # Get signals for this strategy
                signals = self.db.query(SignalHistory).filter(
                    and_(
                        SignalHistory.symbol == symbol,
                        SignalHistory.strategy_name == strategy,
                        SignalHistory.trigger_date >= start_date,
                        SignalHistory.trigger_date <= end_date,
                        SignalHistory.is_successful.in_(['SUCCESS', 'LOSS'])
                    )
                ).all()
                
                if not signals:
                    comparison_results.append({
                        "strategy_name": strategy,
                        "total_signals": 0,
                        "success_rate": 0.0,
                        "total_return": 0.0,
                        "avg_return": 0.0,
                        "max_drawdown": 0.0,
                        "sharpe_ratio": None,
                        "avg_holding_period": 0.0
                    })
                    continue
                
                # Calculate metrics for this strategy
                total_signals = len(signals)
                successful_signals = len([s for s in signals if s.is_successful == 'SUCCESS'])
                success_rate = successful_signals / total_signals if total_signals > 0 else 0.0
                
                returns = [s.return_percentage for s in signals if s.return_percentage is not None]
                total_return = sum(returns) if returns else 0.0
                avg_return = np.mean(returns) if returns else 0.0
                max_drawdown = self._calculate_max_drawdown(returns) if returns else 0.0
                
                # Calculate Sharpe ratio
                sharpe_ratio = None
                if returns and len(returns) > 1:
                    std_return = np.std(returns)
                    if std_return > 0:
                        sharpe_ratio = avg_return / std_return
                
                # Calculate average holding period
                holding_periods = [s.holding_period_days for s in signals if s.holding_period_days is not None]
                avg_holding_period = np.mean(holding_periods) if holding_periods else 0.0
                
                comparison_results.append({
                    "strategy_name": strategy,
                    "total_signals": total_signals,
                    "success_rate": success_rate,
                    "total_return": total_return,
                    "avg_return": avg_return,
                    "max_drawdown": max_drawdown,
                    "sharpe_ratio": sharpe_ratio,
                    "avg_holding_period": avg_holding_period
                })
            
            logger.info(f"Compared {len(strategies)} strategies for {symbol}")
            return comparison_results
            
        except Exception as e:
            logger.error(f"Error comparing strategies: {str(e)}")
            raise RuntimeError(f"Failed to compare strategies: {str(e)}")

    def _parse_timeframe(self, timeframe: str) -> int:
        """Parse timeframe string to number of days."""
        timeframe_map = {
            "1d": 1,
            "7d": 7,
            "30d": 30,
            "90d": 90,
            "1y": 365
        }
        return timeframe_map.get(timeframe, 30)