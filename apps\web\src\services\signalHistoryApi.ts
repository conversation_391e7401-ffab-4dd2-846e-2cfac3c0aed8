import { SignalData, SignalEffectiveness, SignalFilters } from '../stores/signalHistoryStore';

const API_BASE_URL = '/api';

export interface SignalHistoryResponse {
  signals: SignalData[];
  total_count: number;
  page: number;
  page_size: number;
}

export interface SignalHistoryRequest {
  symbol: string;
  start_date?: string;
  end_date?: string;
  signal_types?: string;
  min_confidence?: number;
  min_effectiveness?: number;
  strategies?: string;
  page?: number;
  page_size?: number;
}

class SignalHistoryApiService {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.detail || 
        errorData.message || 
        `HTTP ${response.status}: ${response.statusText}`
      );
    }
    
    return response.json();
  }
  
  async getSignalHistory(params: SignalHistoryRequest): Promise<SignalHistoryResponse> {
    const queryParams = new URLSearchParams();
    
    // Add all parameters to query string
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    
    return this.request<SignalHistoryResponse>(
      `/signal-history?${queryParams.toString()}`
    );
  }
  
  async getSignalEffectiveness(
    symbol: string, 
    filters?: Partial<SignalFilters>
  ): Promise<SignalEffectiveness> {
    const queryParams = new URLSearchParams({ symbol });
    
    if (filters) {
      if (filters.dateRange?.start) {
        queryParams.append('start_date', filters.dateRange.start);
      }
      if (filters.dateRange?.end) {
        queryParams.append('end_date', filters.dateRange.end);
      }
      if (filters.signalTypes?.length) {
        queryParams.append('signal_types', filters.signalTypes.join(','));
      }
      if (filters.strategies?.length) {
        queryParams.append('strategies', filters.strategies.join(','));
      }
      if (filters.minConfidence !== undefined) {
        queryParams.append('min_confidence', filters.minConfidence.toString());
      }
    }
    
    return this.request<SignalEffectiveness>(
      `/signal-history/effectiveness?${queryParams.toString()}`
    );
  }
  
  async getSignalById(signalId: string): Promise<SignalData> {
    return this.request<SignalData>(`/signal-history/${signalId}`);
  }
  
  async getAvailableStrategies(): Promise<string[]> {
    const response = await this.request<{ strategies: string[] }>('/signal-history/strategies');
    return response.strategies;
  }
  
  async getSignalPerformanceMetrics(
    symbol: string,
    timeframe: '1d' | '7d' | '30d' | '90d' | '1y' = '30d'
  ): Promise<{
    total_signals: number;
    win_rate: number;
    average_return: number;
    max_drawdown: number;
    sharpe_ratio: number;
    profit_factor: number;
  }> {
    const queryParams = new URLSearchParams({
      symbol,
      timeframe
    });
    
    return this.request(`/signal-history/metrics?${queryParams.toString()}`);
  }
  
  async compareSignalStrategies(
    symbol: string,
    strategies: string[],
    timeframe: string = '30d'
  ): Promise<{
    strategy_name: string;
    total_signals: number;
    win_rate: number;
    average_return: number;
    sharpe_ratio: number;
  }[]> {
    const queryParams = new URLSearchParams({
      symbol,
      strategies: strategies.join(','),
      timeframe
    });
    
    return this.request(`/signal-history/compare?${queryParams.toString()}`);
  }
}

// Create singleton instance
export const signalHistoryApi = new SignalHistoryApiService();

// Export individual methods for easier testing
export const {
  getSignalHistory,
  getSignalEffectiveness,
  getSignalById,
  getAvailableStrategies,
  getSignalPerformanceMetrics,
  compareSignalStrategies,
} = signalHistoryApi;

// Helper function to convert filters to API request format
export const filtersToApiRequest = (
  symbol: string,
  filters: SignalFilters,
  pagination?: { page: number; pageSize: number }
): SignalHistoryRequest => {
  return {
    symbol,
    start_date: filters.dateRange.start,
    end_date: filters.dateRange.end,
    signal_types: filters.signalTypes.join(','),
    min_confidence: filters.minConfidence,
    min_effectiveness: filters.minEffectiveness,
    strategies: filters.strategies.length > 0 ? filters.strategies.join(',') : undefined,
    page: pagination?.page,
    page_size: pagination?.pageSize,
  };
};

// Error handling utilities
export class SignalHistoryApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'SignalHistoryApiError';
  }
}

// Cache management for performance
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttlMs: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
  }
  
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data as T;
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  delete(key: string): void {
    this.cache.delete(key);
  }
}

export const apiCache = new ApiCache();

// Cached API methods
export const getCachedSignalHistory = async (
  params: SignalHistoryRequest
): Promise<SignalHistoryResponse> => {
  const cacheKey = `signal-history-${JSON.stringify(params)}`;
  const cached = apiCache.get<SignalHistoryResponse>(cacheKey);
  
  if (cached) {
    return cached;
  }
  
  const result = await signalHistoryApi.getSignalHistory(params);
  apiCache.set(cacheKey, result, 2 * 60 * 1000); // 2 minutes cache
  
  return result;
};

export const getCachedSignalEffectiveness = async (
  symbol: string,
  filters?: Partial<SignalFilters>
): Promise<SignalEffectiveness> => {
  const cacheKey = `signal-effectiveness-${symbol}-${JSON.stringify(filters)}`;
  const cached = apiCache.get<SignalEffectiveness>(cacheKey);
  
  if (cached) {
    return cached;
  }
  
  const result = await signalHistoryApi.getSignalEffectiveness(symbol, filters);
  apiCache.set(cacheKey, result, 5 * 60 * 1000); // 5 minutes cache
  
  return result;
};