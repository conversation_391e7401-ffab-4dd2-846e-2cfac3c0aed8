"""Tests for Magic Nine Turns calculator."""

from datetime import datetime, timedelta

import pytest

from src.features.strategy.calculators.magic_nine_turns import MagicNineTurnsCalculator
from src.models.stock_data import DailyPrice


class TestMagicNineTurnsCalculator:
    """Test cases for Magic Nine Turns calculator."""

    def setup_method(self):
        """Set up test fixtures."""
        self.calculator = MagicNineTurnsCalculator()

    @pytest.fixture
    def basic_price_data(self):
        """Basic price data for testing."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create 20 days of price data with known patterns
        close_prices = [100, 101, 102, 103, 104,  # Rising trend
                       105, 106, 107, 108, 109,  # Continued rise
                       110, 111, 112, 113, 114,  # More rise
                       115, 116, 117, 118, 119]  # Peak formation

        for i, close in enumerate(close_prices):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=close + 0.5,
                low=close - 1.0,
                close=close,
                volume=1000000
            ))

        return prices

    @pytest.fixture
    def insufficient_data(self):
        """Insufficient data for testing error handling."""
        base_date = datetime(2025, 1, 1)
        prices = []

        for i in range(3):  # Only 3 days, need at least 5
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=100.0,
                high=101.0,
                low=99.0,
                close=100.0 + i,
                volume=1000000
            ))

        return prices

    @pytest.fixture
    def nine_count_sequence(self):
        """Price data that creates a complete 9-count sequence."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create base prices that don't trigger counting
        base_closes = [100, 100, 100, 100, 100]

        # Add sequence that will create 9-count UP
        # Each close is higher than 4 periods ago
        sequence_closes = [101, 102, 103, 104, 105, 106, 107, 108, 109]

        all_closes = base_closes + sequence_closes

        for i, close in enumerate(all_closes):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=close + 0.5,
                low=close - 1.0,
                close=close,
                volume=1000000
            ))

        return prices

    @pytest.fixture
    def mixed_direction_data(self):
        """Price data with changing directions."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Pattern: up for 3, down for 2, up for 4
        close_prices = [100, 100, 100, 100, 100,  # Base
                       101, 102, 103,              # UP 3
                       99, 98,                     # DOWN 2
                       104, 105, 106, 107]        # UP 4

        for i, close in enumerate(close_prices):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=close + 0.5,
                low=close - 1.0,
                close=close,
                volume=1000000
            ))

        return prices

    def test_insufficient_data_raises_error(self, insufficient_data):
        """Test that insufficient data raises ValueError."""
        with pytest.raises(ValueError, match="Need at least 5 data points"):
            self.calculator.calculate(insufficient_data)

    def test_basic_calculation(self, basic_price_data):
        """Test basic Magic Nine Turns calculation."""
        result = self.calculator.calculate(basic_price_data)

        assert result is not None
        assert len(result.sequence) == len(basic_price_data)
        assert result.current_count >= 0
        assert result.current_count <= 9
        assert result.direction in ['UP', 'DOWN', 'NONE']

        # First 4 values should be None (need lookback period)
        for i in range(4):
            assert result.sequence[i] is None

    def test_nine_count_completion(self, nine_count_sequence):
        """Test that 9-count sequence is properly detected."""
        result = self.calculator.calculate(nine_count_sequence)

        assert result.is_complete is True
        assert result.current_count == 9
        assert result.direction == 'UP'

        # Check that sequence reaches 9
        assert 9 in result.sequence

    def test_direction_changes(self, mixed_direction_data):
        """Test that direction changes reset the count."""
        result = self.calculator.calculate(mixed_direction_data)

        # Should have direction changes in the sequence
        sequence_values = [v for v in result.sequence if v is not None]

        # Should have some count resets (values going back to 1)
        assert 1 in sequence_values[1:]  # Should have reset at least once after first count

    def test_equal_closes_handling(self):
        """Test handling of equal closing prices."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create data with equal closes
        close_prices = [100, 100, 100, 100, 100, 100, 100, 100]

        for i, close in enumerate(close_prices):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close,
                high=close,
                low=close,
                close=close,
                volume=1000000
            ))

        result = self.calculator.calculate(prices)

        # With all equal closes, should not build any count
        assert result.current_count == 0
        assert result.direction == 'NONE'
        assert result.is_complete is False

    def test_custom_lookback_period(self):
        """Test calculator with custom lookback period."""
        custom_calculator = MagicNineTurnsCalculator(lookback_period=2)

        base_date = datetime(2025, 1, 1)
        prices = []

        # Create simple ascending sequence
        for i in range(10):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=100.0 + i,
                high=101.0 + i,
                low=99.0 + i,
                close=100.0 + i,
                volume=1000000
            ))

        result = custom_calculator.calculate(prices)

        # With lookback=2, should start counting from index 2
        assert result.sequence[0] is None
        assert result.sequence[1] is None
        assert result.sequence[2] is not None

    def test_max_count_limit(self):
        """Test that count doesn't exceed maximum of 9."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create very long ascending sequence (15 periods)
        for i in range(20):
            date = base_date + timedelta(days=i)
            close = 100.0 + i
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=close + 0.5,
                low=close - 1.0,
                close=close,
                volume=1000000
            ))

        result = self.calculator.calculate(prices)

        # Count should not exceed 9
        assert result.current_count <= 9
        assert max(v for v in result.sequence if v is not None) <= 9

    def test_sequence_length_matches_input(self, basic_price_data):
        """Test that output sequence length matches input data length."""
        result = self.calculator.calculate(basic_price_data)

        assert len(result.sequence) == len(basic_price_data)

    def test_down_trend_counting(self):
        """Test counting in downward trend."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create base then descending sequence
        close_prices = [120, 120, 120, 120, 120,  # Base
                       119, 118, 117, 116, 115,  # Descending
                       114, 113, 112, 111, 110]  # Continue down

        for i, close in enumerate(close_prices):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close + 0.5,
                high=close + 1.0,
                low=close - 0.5,
                close=close,
                volume=1000000
            ))

        result = self.calculator.calculate(prices)

        assert result.direction == 'DOWN'
        assert result.current_count > 0
