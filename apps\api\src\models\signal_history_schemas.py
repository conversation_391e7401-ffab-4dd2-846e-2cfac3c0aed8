"""Pydantic schemas for signal history API endpoints."""

from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, Field


class SignalHistoryBase(BaseModel):
    """Base schema for signal history."""
    
    symbol: str = Field(..., description="Stock symbol", max_length=20)
    signal_type: Literal['BUY', 'SELL', 'HOLD'] = Field(..., description="Type of trading signal")
    signal_strength: Optional[float] = Field(None, description="Signal confidence (0.0 to 1.0)", ge=0.0, le=1.0)
    trigger_price: float = Field(..., description="Price when signal was triggered", gt=0)
    trigger_date: datetime = Field(..., description="Date and time when signal was triggered")
    strategy_name: str = Field(default="magic_nine_macd", description="Strategy that generated the signal")
    strategy_params: Optional[str] = Field(None, description="JSON string of strategy parameters")
    volume: Optional[float] = Field(None, description="Trading volume at signal time")
    market_cap: Optional[float] = Field(None, description="Market capitalization at signal time")


class SignalHistoryCreate(SignalHistoryBase):
    """Schema for creating a new signal history record."""
    pass


class SignalHistoryUpdate(BaseModel):
    """Schema for updating signal history with effectiveness data."""
    
    exit_price: Optional[float] = Field(None, description="Exit price for the signal", gt=0)
    exit_date: Optional[datetime] = Field(None, description="Exit date for the signal")
    return_percentage: Optional[float] = Field(None, description="Return percentage from the signal")
    holding_period_days: Optional[int] = Field(None, description="Number of days the position was held", ge=0)
    is_successful: Optional[Literal['SUCCESS', 'LOSS', 'PENDING']] = Field(None, description="Signal outcome")


class SignalHistoryResponse(SignalHistoryBase):
    """Schema for signal history API responses."""
    
    id: int = Field(..., description="Unique identifier")
    exit_price: Optional[float] = Field(None, description="Exit price for the signal")
    exit_date: Optional[datetime] = Field(None, description="Exit date for the signal")
    return_percentage: Optional[float] = Field(None, description="Return percentage from the signal")
    holding_period_days: Optional[int] = Field(None, description="Number of days the position was held")
    is_successful: Optional[Literal['SUCCESS', 'LOSS', 'PENDING']] = Field(None, description="Signal outcome")
    created_at: datetime = Field(..., description="Record creation timestamp")
    updated_at: datetime = Field(..., description="Record last update timestamp")

    class Config:
        from_attributes = True


class SignalEffectivenessBase(BaseModel):
    """Base schema for signal effectiveness metrics."""
    
    symbol: str = Field(..., description="Stock symbol", max_length=20)
    signal_type: Literal['BUY', 'SELL'] = Field(..., description="Type of trading signal")
    strategy_name: str = Field(default="magic_nine_macd", description="Strategy name")
    period_start: datetime = Field(..., description="Start of analysis period")
    period_end: datetime = Field(..., description="End of analysis period")


class SignalEffectivenessResponse(SignalEffectivenessBase):
    """Schema for signal effectiveness API responses."""
    
    id: int = Field(..., description="Unique identifier")
    total_signals: int = Field(..., description="Total number of signals in period", ge=0)
    successful_signals: int = Field(..., description="Number of successful signals", ge=0)
    success_rate: float = Field(..., description="Success rate (0.0 to 1.0)", ge=0.0, le=1.0)
    avg_return: float = Field(..., description="Average return percentage")
    max_return: float = Field(..., description="Maximum return percentage")
    min_return: float = Field(..., description="Minimum return percentage")
    total_return: float = Field(..., description="Total cumulative return")
    max_drawdown: float = Field(..., description="Maximum drawdown percentage")
    avg_holding_period: float = Field(..., description="Average holding period in days", ge=0)
    volatility: float = Field(..., description="Return volatility", ge=0)
    benchmark_return: Optional[float] = Field(None, description="Benchmark return for comparison")
    alpha: Optional[float] = Field(None, description="Alpha (excess return vs benchmark)")
    last_calculated: datetime = Field(..., description="When metrics were last calculated")

    class Config:
        from_attributes = True


class SignalHistoryFilter(BaseModel):
    """Schema for filtering signal history queries."""
    
    symbol: Optional[str] = Field(None, description="Filter by stock symbol")
    signal_type: Optional[Literal['BUY', 'SELL', 'HOLD']] = Field(None, description="Filter by signal type")
    strategy_name: Optional[str] = Field(None, description="Filter by strategy name")
    start_date: Optional[datetime] = Field(None, description="Filter signals after this date")
    end_date: Optional[datetime] = Field(None, description="Filter signals before this date")
    min_success_rate: Optional[float] = Field(None, description="Minimum success rate threshold", ge=0.0, le=1.0)
    is_successful: Optional[Literal['SUCCESS', 'LOSS', 'PENDING']] = Field(None, description="Filter by outcome")
    limit: int = Field(default=100, description="Maximum number of results", ge=1, le=1000)
    offset: int = Field(default=0, description="Number of results to skip", ge=0)


class SignalHistoryListResponse(BaseModel):
    """Schema for paginated signal history list responses."""
    
    signals: List[SignalHistoryResponse] = Field(..., description="List of signal history records")
    total_count: int = Field(..., description="Total number of matching records", ge=0)
    has_more: bool = Field(..., description="Whether there are more results available")


class SignalEffectivenessListResponse(BaseModel):
    """Schema for signal effectiveness list responses."""
    
    effectiveness_metrics: List[SignalEffectivenessResponse] = Field(..., description="List of effectiveness metrics")
    total_count: int = Field(..., description="Total number of metrics", ge=0)


class SignalPerformanceMetrics(BaseModel):
    """Schema for comprehensive signal performance metrics."""
    
    symbol: str = Field(..., description="Stock symbol")
    total_signals: int = Field(..., description="Total signals generated", ge=0)
    buy_signals: int = Field(..., description="Number of buy signals", ge=0)
    sell_signals: int = Field(..., description="Number of sell signals", ge=0)
    overall_success_rate: float = Field(..., description="Overall success rate", ge=0.0, le=1.0)
    buy_success_rate: float = Field(..., description="Buy signal success rate", ge=0.0, le=1.0)
    sell_success_rate: float = Field(..., description="Sell signal success rate", ge=0.0, le=1.0)
    total_return: float = Field(..., description="Total portfolio return")
    avg_return_per_signal: float = Field(..., description="Average return per signal")
    max_consecutive_wins: int = Field(..., description="Maximum consecutive successful signals", ge=0)
    max_consecutive_losses: int = Field(..., description="Maximum consecutive failed signals", ge=0)
    avg_holding_period: float = Field(..., description="Average holding period in days", ge=0)
    sharpe_ratio: Optional[float] = Field(None, description="Sharpe ratio of signal returns")
    win_loss_ratio: float = Field(..., description="Ratio of winning to losing signals", ge=0)