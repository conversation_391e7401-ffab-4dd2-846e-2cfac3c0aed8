import { render, screen } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { I18nextProvider } from 'react-i18next'
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import { LanguageSelector } from '../LanguageSelector'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Create a test i18n instance
const createTestI18n = (lng = 'en') => {
  const testI18n = i18n.createInstance()
  testI18n.use(initReactI18next).init({
    lng,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    resources: {
        en: {
          common: {
            'language.selector': 'Language selector',
            language: 'Language',
            english: 'English',
            chinese: 'Chinese',
          },
        },
        'zh-CN': {
          common: {
            'language.selector': '语言选择器',
            language: '语言',
            english: '英文',
            chinese: '中文',
          },
        },
      },
  })
  return testI18n
}

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const testI18n = createTestI18n()
  return <I18nextProvider i18n={testI18n}>{children}</I18nextProvider>
}

describe('LanguageSelector', () => {
  beforeEach(() => {
    mockLocalStorage.getItem.mockClear()
    mockLocalStorage.setItem.mockClear()
    mockLocalStorage.removeItem.mockClear()
    mockLocalStorage.clear.mockClear()
  })

  it('renders language selector with default English language', () => {
    render(
      <TestWrapper>
        <LanguageSelector />
      </TestWrapper>
    )

    expect(screen.getByRole('button')).toBeInTheDocument()
    expect(screen.getByText('English')).toBeInTheDocument()
  })

  it('renders with Chinese language when initialized with zh-CN', () => {
    const testI18n = createTestI18n('zh-CN')
    render(
      <I18nextProvider i18n={testI18n}>
        <LanguageSelector />
      </I18nextProvider>
    )

    expect(screen.getByText('中文')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <LanguageSelector />
      </TestWrapper>
    )

    const trigger = screen.getByRole('button')
    expect(trigger).toHaveAttribute('aria-label', 'Language selector')
  })

  it('can receive focus', () => {
    render(
      <TestWrapper>
        <LanguageSelector />
      </TestWrapper>
    )

    const trigger = screen.getByRole('button')
    
    trigger.focus()
    
    expect(trigger).toHaveFocus()
  })

  it('falls back to English when current language is not in available languages', () => {
    const testI18n = createTestI18n('fr') // French not in available languages
    render(
      <I18nextProvider i18n={testI18n}>
        <LanguageSelector />
      </I18nextProvider>
    )

    expect(screen.getByText('English')).toBeInTheDocument()
  })

  it('handles missing translation keys gracefully', () => {
    const testI18n = createTestI18n('en')
    // Test with missing translation key
    testI18n.options.resources!.en.common = {}
    
    render(
      <I18nextProvider i18n={testI18n}>
        <LanguageSelector />
      </I18nextProvider>
    )

    const trigger = screen.getByRole('button')
    expect(trigger).toBeInTheDocument()
  })
})