### 3. Tech Stack

#### Technology Stack Table

| Category               | Technology              | Version | Purpose                            | Rationale                                        |
| :--------------------- | :---------------------- | :------ | :--------------------------------- | :----------------------------------------------- |
| **Frontend Language** | TypeScript              | 5.4+    | Primary frontend language          | Ensures type safety and maintainability.         |
| **Frontend Framework** | React                   | 18+     | UI development library             | Large ecosystem, excellent for data-heavy apps.  |
| **UI Component Lib** | Radix UI                | Latest  | Unstyled, accessible components    | Provides accessibility primitives without styling. |
| **State Management** | Zustand                 | Latest  | Simple state management            | Minimal boilerplate, easy to use for MVP scope.  |
| **Backend Language** | Python                  | 3.11+   | Primary backend language           | Required for `akshare` and `Tushare` libraries.  |
| **Backend Framework** | FastAPI                 | Latest  | Web framework for APIs             | High performance, auto-generates API docs.       |
| **API Style** | REST                    | N/A     | Frontend-backend communication     | Simple, stateless, and well-understood pattern.  |
| **Database** | SQLite                  | 3+      | MVP database                       | File-based, zero-config for simplicity in MVP.   |
| **Frontend Testing** | Vitest & RTL            | Latest  | Unit/Integration testing           | Modern, fast test runner for Vite environments.  |
| **Backend Testing** | Pytest                  | Latest  | Unit/Integration testing           | De-facto standard for testing in Python.         |
| **E2E Testing** | Playwright              | Latest  | End-to-end browser testing         | Robust, modern, and excellent cross-browser support. |
| **Build Tool/Bundler** | Vite                    | Latest  | Frontend dev server & bundler      | Extremely fast performance and developer experience. |
| **CI/CD** | GitHub Actions          | N/A     | Automation and deployment          | Tightly integrated with GitHub, excellent free tier. |
| **CSS Framework** | Tailwind CSS            | Latest  | Utility-first styling              | Accelerates styling and works perfectly with Radix. |

***
