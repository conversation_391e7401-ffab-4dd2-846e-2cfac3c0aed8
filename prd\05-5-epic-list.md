### 5. Epic List

* **Epic 1: Foundation & Core Signal Generation**
  * **Goal:** Establish the core project infrastructure and implement the backend strategy engine to generate a trading signal for a single stock, accessible via an API endpoint.
* **Epic 2: Analysis Interface & Visualization**
  * **Goal:** Develop the user-facing stock analysis interface, allowing users to input a stock code and visualize the generated trading signal on an interactive chart.
* **Epic 3: Automated Stock Screening**
  * **Goal:** Implement the automated stock screening tool to scan the market daily and present a list of potential trading opportunities to the user.

***
