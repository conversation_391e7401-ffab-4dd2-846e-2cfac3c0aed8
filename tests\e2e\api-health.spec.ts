import { test, expect } from '@playwright/test'

test.describe('API Health', () => {
  test('should have backend API running', async ({ request }) => {
    // Test the health endpoint
    const response = await request.get('http://localhost:8000/health')
    expect(response.ok()).toBeTruthy()
    
    const data = await response.json()
    expect(data.status).toBe('healthy')
    expect(data.service).toBe('trading-agent-api')
  })

  test('should have root endpoint working', async ({ request }) => {
    const response = await request.get('http://localhost:8000/')
    expect(response.ok()).toBeTruthy()
    
    const data = await response.json()
    expect(data.message).toBe('Trading Agent API is running')
    expect(data.version).toBe('1.0.0')
  })

  test('should have signal endpoint responding', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/signal/000001.SZ')
    expect(response.ok()).toBeTruthy()
    
    const data = await response.json()
    expect(data.symbol).toBe('000001.SZ')
    expect(data).toHaveProperty('signal')
    expect(data).toHaveProperty('chartData')
  })

  test('should have screener endpoint responding', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/screener/results')
    expect(response.ok()).toBeTruthy()
    
    const data = await response.json()
    expect(Array.isArray(data)).toBeTruthy()
  })
})