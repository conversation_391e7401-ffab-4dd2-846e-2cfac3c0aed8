import { create } from 'zustand'
import type { ScreenerItem } from '@trading-agent/shared-types'

interface ScreenerState {
  results: ScreenerItem[]
  loading: boolean
  error: string | null
  lastUpdated: Date | null
  setResults: (results: ScreenerItem[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
}

export const useScreenerStore = create<ScreenerState>((set) => ({
  results: [],
  loading: false,
  error: null,
  lastUpdated: null,
  setResults: (results) => set({ 
    results, 
    error: null, 
    lastUpdated: new Date() 
  }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error, loading: false }),
  clearError: () => set({ error: null }),
}))