import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { apiClient, ApiError } from '../api'
import type { SignalResult, ScreenerItem } from '@trading-agent/shared-types'

// Mock fetch globally
globalThis.fetch = vi.fn()

describe('ApiClient', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getSignalForStock', () => {
    it('successfully fetches signal data', async () => {
      const mockSignalResult: SignalResult = {
        symbol: '000001.SZ',
        lastScanDate: '2025-01-01',
        signal: 'SELL_CANDIDATE',
        chartData: {
          dailyPrices: [
            { date: '2025-01-01', open: 100, high: 110, low: 95, close: 105, volume: 1000 }
          ],
          magicNineSequence: [1, 2, 3],
          macdLine: [0.1, 0.2, 0.3],
          signalLine: [0.05, 0.15, 0.25],
          divergencePoints: [{ date: '2025-01-01', type: 'TOP' }]
        }
      }

      const mockResponse = {
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue(mockSignalResult)
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await apiClient.getSignalForStock('000001.SZ')

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/signal/000001.SZ',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
      expect(result).toEqual(mockSignalResult)
    })

    it('handles 404 error for invalid stock code', async () => {
      const errorResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: vi.fn().mockResolvedValue({
          detail: 'Stock code 000001.SZ not found'
        })
      }

      vi.mocked(fetch).mockResolvedValue(errorResponse as any)

      await expect(apiClient.getSignalForStock('000001.SZ')).rejects.toThrow(
        new ApiError('Stock code 000001.SZ not found', 404)
      )
    })

    it('handles network errors', async () => {
      vi.mocked(fetch).mockRejectedValue(new TypeError('Failed to fetch'))

      await expect(apiClient.getSignalForStock('000001.SZ')).rejects.toThrow(
        new ApiError('Network error. Please check your connection.', 0)
      )
    })

    it('handles malformed JSON response', async () => {
      const errorResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: vi.fn().mockRejectedValue(new Error('Invalid JSON'))
      }

      vi.mocked(fetch).mockResolvedValue(errorResponse as any)

      await expect(apiClient.getSignalForStock('000001.SZ')).rejects.toThrow(
        new ApiError('API request failed: 500 Internal Server Error', 500)
      )
    })

    it('handles unexpected errors', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Something went wrong'))

      await expect(apiClient.getSignalForStock('000001.SZ')).rejects.toThrow(
        new ApiError('An unexpected error occurred.', 0)
      )
    })
  })

  describe('getScreenerResults', () => {
    it('successfully fetches screener results', async () => {
      const mockScreenerResults: ScreenerItem[] = [
        { symbol: '000001.SZ', companyName: 'Company A' },
        { symbol: '000002.SZ', companyName: 'Company B' }
      ]

      const mockResponse = {
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue(mockScreenerResults)
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await apiClient.getScreenerResults()

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/screener/results',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
      expect(result).toEqual(mockScreenerResults)
    })

    it('handles empty screener results', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue([])
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await apiClient.getScreenerResults()
      expect(result).toEqual([])
    })
  })

  describe('error handling', () => {
    it('preserves error details from API response', async () => {
      const errorResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: vi.fn().mockResolvedValue({
          detail: 'Invalid stock code format'
        })
      }

      vi.mocked(fetch).mockResolvedValue(errorResponse as any)

      await expect(apiClient.getSignalForStock('INVALID')).rejects.toThrow(
        new ApiError('Invalid stock code format', 400)
      )
    })

    it('falls back to status text when no detail is provided', async () => {
      const errorResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: vi.fn().mockResolvedValue({})
      }

      vi.mocked(fetch).mockResolvedValue(errorResponse as any)

      await expect(apiClient.getSignalForStock('000001.SZ')).rejects.toThrow(
        new ApiError('API request failed: 500 Internal Server Error', 500)
      )
    })
  })

  describe('ApiError class', () => {
    it('creates error with message and status code', () => {
      const error = new ApiError('Test error', 404)
      
      expect(error.message).toBe('Test error')
      expect(error.status).toBe(404)
      expect(error.name).toBe('ApiError')
      expect(error instanceof Error).toBe(true)
    })
  })
})