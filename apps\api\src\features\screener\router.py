"""
Screener results router.
"""
from datetime import datetime, timedelta
from typing import Any, cast

from fastapi import APIRouter, HTTPException

from ...models.api_responses import ScreenerItem
from .repository import ScreenerRepository
from .scheduler import get_scheduler
from .screening_service import ScreeningService

router = APIRouter()

# Simple in-memory cache for API responses
_results_cache: dict[str, Any] = {
    "data": None,
    "timestamp": None,
    "ttl_hours": 4
}


@router.get("/screener/results", response_model=list[ScreenerItem])
async def get_screener_results() -> list[dict[str, Any]]:
    """
    Get the latest list of stocks flagged by the screener.

    Returns:
        List of ScreenerItem objects with symbol and companyName
    """
    try:
        # Check cache first
        if (_results_cache["data"] is not None and
            _results_cache["timestamp"] is not None):
            cache_age = datetime.now() - _results_cache["timestamp"]
            if cache_age < timedelta(hours=_results_cache["ttl_hours"]):
                return cast(list[dict[str, Any]], _results_cache["data"])

        # Fetch fresh data from repository
        with ScreenerRepository() as repo:
            results = repo.get_latest_screening_results()

            # Update cache
            _results_cache["data"] = results
            _results_cache["timestamp"] = datetime.now()

            return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve screening results: {str(e)}") from e


@router.post("/screener/run")
async def run_immediate_screening() -> dict[str, Any]:
    """
    Trigger an immediate screening run.

    Returns:
        Screening results and statistics
    """
    try:
        scheduler = get_scheduler()
        result = await scheduler.run_immediate_screening()

        # Invalidate cache after new screening run
        _results_cache["data"] = None
        _results_cache["timestamp"] = None

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Screening execution failed: {str(e)}") from e


@router.get("/screener/status")
async def get_screener_status() -> dict[str, Any]:
    """
    Get screener service status and statistics.

    Returns:
        Status information including scheduler state, job history, and cache stats
    """
    try:
        scheduler = get_scheduler()
        screening_service = ScreeningService()

        scheduler_status = scheduler.get_scheduler_status()
        cache_stats = screening_service.get_cache_stats()
        screening_stats = screening_service.get_screening_stats()

        return {
            'service_status': 'healthy',
            'scheduler': scheduler_status,
            'cache': cache_stats,
            'screening_statistics': screening_stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}") from e


@router.get("/screener/health")
async def health_check() -> dict[str, str]:
    """
    Health check endpoint for monitoring.

    Returns:
        Health status
    """
    try:
        scheduler = get_scheduler()

        # Basic health checks
        scheduler_running = scheduler.is_running

        # Could add more health checks here:
        # - Database connectivity
        # - Last successful screening within expected timeframe
        # - Cache functionality

        if scheduler_running:
            return {
                "status": "healthy",
                "message": "Screener service is operational",
                "scheduler": "running"
            }
        else:
            return {
                "status": "degraded",
                "message": "Scheduler is not running",
                "scheduler": "stopped"
            }

    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Health check failed: {str(e)}"
        ) from e


@router.get("/screener/jobs/history")
async def get_job_history(limit: int = 10) -> list[dict[str, Any]]:
    """
    Get job execution history.

    Args:
        limit: Maximum number of history entries to return (default: 10)

    Returns:
        List of job execution records
    """
    try:
        scheduler = get_scheduler()
        history = scheduler.get_job_history(limit=limit)
        return history
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job history: {str(e)}") from e


@router.post("/screener/schedule/daily")
async def schedule_daily_screening(hour: int = 15, minute: int = 30) -> dict[str, str]:
    """
    Schedule daily screening at specified time.

    Args:
        hour: Hour to run (0-23, default: 15)
        minute: Minute to run (0-59, default: 30)

    Returns:
        Confirmation message
    """
    try:
        scheduler = get_scheduler()

        # Validate time parameters
        if not (0 <= hour <= 23):
            raise HTTPException(status_code=400, detail="Hour must be between 0 and 23")
        if not (0 <= minute <= 59):
            raise HTTPException(status_code=400, detail="Minute must be between 0 and 59")

        job = scheduler.schedule_daily_screening(hour=hour, minute=minute)

        # Start scheduler if not running
        if not scheduler.is_running:
            scheduler.start_scheduler()

        return {
            "status": "success",
            "message": f"Daily screening scheduled at {hour:02d}:{minute:02d}",
            "job_id": job.id,
            "next_run": job.next_run_time.isoformat() if job.next_run_time else "Not scheduled"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to schedule daily screening: {str(e)}") from e


@router.delete("/screener/schedule/{job_id}")
async def remove_scheduled_job(job_id: str) -> dict[str, str]:
    """
    Remove a scheduled screening job.

    Args:
        job_id: Job identifier to remove

    Returns:
        Confirmation message
    """
    try:
        scheduler = get_scheduler()
        success = scheduler.remove_job(job_id)

        if success:
            return {
                "status": "success",
                "message": f"Job {job_id} removed successfully"
            }
        else:
            raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to remove job: {str(e)}") from e
