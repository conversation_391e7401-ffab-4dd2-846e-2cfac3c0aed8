"""
Main FastAPI application entry point.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .core.database import create_tables
from .features.data.router import router as data_router
from .features.screener.router import router as screener_router
from .features.signal.router import router as signal_router
from .features.signal.history_router import router as signal_history_router

app = FastAPI(
    title="Trading Agent API",
    description="API for trading signal analysis and stock screening",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Initialize database tables
create_tables()

# Initialize scheduler (but don't start it immediately for development)
# In production, you would call initialize_default_schedule() here

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(signal_router, prefix="/api")
app.include_router(signal_history_router, prefix="/api")
app.include_router(screener_router, prefix="/api")
app.include_router(data_router, prefix="/api")


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"message": "Trading Agent API is running", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {"status": "healthy", "service": "trading-agent-api"}
