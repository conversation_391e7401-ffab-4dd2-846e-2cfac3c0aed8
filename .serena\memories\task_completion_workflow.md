# Task Completion Workflow

## Required Steps When Completing Any Task

### 1. Code Quality Validation
```bash
npm run lint                   # Lint all packages (frontend + backend)
npm run type-check            # TypeScript + Python type checking
```

### 2. Testing Validation
```bash
npm test                       # Run unit tests for all packages
npm run test:e2e              # Run E2E tests (if UI changes)
```

### 3. Build Verification
```bash
npm run build                 # Ensure all packages build successfully
```

## Specific Validations by Component

### Frontend Changes (apps/web/)
```bash
cd apps/web
npm run test                   # Vitest unit tests
npm run type-check            # TypeScript validation
npm run lint                  # ESLint validation
npm run build                 # Vite build verification
```

### Backend Changes (apps/api/)
```bash
cd apps/api
uv run python -m pytest tests/ -v          # Python unit tests
uv run python -m mypy src/                  # MyPy type checking
uv run python -m ruff check src/ tests/     # Ruff linting
```

### Shared Types Changes (packages/shared-types/)
```bash
cd packages/shared-types
npm run build                 # TypeScript compilation
```

## Pre-Commit Checklist

### ✅ Required Checks
- [ ] All linting passes (`npm run lint`)
- [ ] All type checking passes (`npm run type-check`)
- [ ] All unit tests pass (`npm test`)
- [ ] Build succeeds (`npm run build`)
- [ ] No console errors in development
- [ ] API endpoints respond correctly (if backend changes)
- [ ] Frontend loads without errors (if frontend changes)

### ✅ Optional but Recommended
- [ ] E2E tests pass for affected workflows (`npm run test:e2e`)
- [ ] Manual testing of changed functionality
- [ ] Code review of changes
- [ ] Documentation updated if needed

## Error Resolution

### Common Issues
1. **TypeScript Errors**: Check import paths and type definitions
2. **Linting Errors**: Follow ESLint/Ruff suggestions
3. **Test Failures**: Ensure mocks are updated for changes
4. **Build Failures**: Check for missing dependencies or path issues

### Windows-Specific Notes
- Use `uv run python` prefix for all Python commands
- Check that paths use proper Windows format in configuration
- Ensure development servers can bind to specified ports

## Integration Testing
- E2E tests automatically start both frontend and backend servers
- Tests run against localhost:5173 (frontend) and localhost:8000 (backend)
- API proxy routes `/api/*` requests to backend during development

## Performance Checks
- Frontend build size should be reasonable (check dist/ folder)
- Backend response times should be acceptable (<500ms for typical requests)
- No memory leaks in long-running development servers