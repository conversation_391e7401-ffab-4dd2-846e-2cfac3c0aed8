# Essential Development Commands

## Project Setup
```bash
npm install                    # Install all workspace dependencies
npm run install-deps          # Install both npm and Python dependencies
```

## Development Servers
```bash
npm run dev                    # Start both frontend (5173) and backend (8000) in parallel
cd apps/web && npm run dev     # Frontend only on localhost:5173
cd apps/api && npm run dev     # Backend only on localhost:8000
```

## Building
```bash
npm run build                  # Build all packages using Turborepo
cd apps/web && npm run build   # Frontend build only (outputs to dist/)
```

## Testing
```bash
npm test                       # Run all tests (unit + integration)
npm run test:e2e              # Run Playwright E2E tests
npm run test:e2e:ui           # Run E2E tests with UI
cd apps/web && npm run test    # Frontend unit tests with Vitest
cd apps/api && npm run test    # Backend tests with Pytest
```

## Code Quality & Validation
```bash
npm run lint                   # Lint all packages
npm run type-check            # TypeScript type checking across all packages
cd apps/api && npm run lint    # Python linting with Ruff
cd apps/api && npm run type-check  # Python type checking with MyPy
```

## Cleanup
```bash
npm run clean                  # Clean all build artifacts
cd apps/api && npm run clean   # Clean Python cache files
```

## Windows-Specific Commands
Since this is a Windows environment, use these system commands:
- `dir` instead of `ls`
- `cd` for navigation
- `type` instead of `cat` for viewing files
- `findstr` instead of `grep` for text search
- `where` instead of `which` for finding executables

## Special Notes
- Use `uv run python` for running Python scripts (as per CLAUDE.md)
- Turborepo manages parallel execution and caching
- E2E tests auto-start both servers on configured ports