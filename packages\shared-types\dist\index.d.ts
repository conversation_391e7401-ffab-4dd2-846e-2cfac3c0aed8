export interface DailyPrice {
    date: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
}
export interface StockData {
    symbol: string;
    dailyPrices: DailyPrice[];
}
export type Signal = 'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL';
export interface SignalResult {
    symbol: string;
    lastScanDate: string;
    signal: Signal;
    chartData: {
        dailyPrices: DailyPrice[];
        magicNineSequence: (number | null)[];
        macdLine: (number | null)[];
        signalLine: (number | null)[];
        divergencePoints: {
            date: string;
            type: 'TOP';
        }[];
    };
}
export interface ScreenerItem {
    symbol: string;
    companyName: string;
}
export interface ApiError {
    detail: string;
    status_code: number;
}
export interface ApiResponse<T> {
    data?: T;
    error?: ApiError;
}
export type SignalType = 'BUY' | 'SELL';
export type SignalSuccessStatus = 'SUCCESS' | 'LOSS' | 'PENDING';
export interface SignalHistoryRecord {
    id: number;
    symbol: string;
    signalType: SignalType;
    signalStrength: number;
    triggerPrice: number;
    triggerDate: string;
    exitPrice?: number;
    exitDate?: string;
    returnPercentage?: number;
    holdingPeriodDays?: number;
    strategyName: string;
    strategyParams?: Record<string, any>;
    volume?: number;
    marketCap?: number;
    isSuccessful: SignalSuccessStatus;
    createdAt: string;
}
export interface SignalEffectiveness {
    id: number;
    symbol: string;
    signalType: SignalType;
    strategyName: string;
    periodStart: string;
    periodEnd: string;
    totalSignals: number;
    successfulSignals: number;
    successRate: number;
    avgReturn: number;
    maxReturn: number;
    minReturn: number;
    totalReturn: number;
    maxDrawdown: number;
    avgHoldingPeriod: number;
    volatility: number;
    benchmarkReturn?: number;
    alpha?: number;
    lastCalculated: string;
}
export interface SignalPerformanceMetrics {
    symbol: string;
    totalSignals: number;
    buySignals: number;
    sellSignals: number;
    overallSuccessRate: number;
    buySuccessRate: number;
    sellSuccessRate: number;
    totalReturn: number;
    avgReturnPerSignal: number;
    maxConsecutiveWins: number;
    maxConsecutiveLosses: number;
    avgHoldingPeriod: number;
    sharpeRatio?: number;
    winLossRatio: number;
}
export interface SignalHistoryFilter {
    symbol?: string;
    signalType?: SignalType;
    strategyName?: string;
    startDate?: string;
    endDate?: string;
    isSuccessful?: SignalSuccessStatus;
    offset?: number;
    limit?: number;
}
export interface SignalHistoryListResponse {
    signals: SignalHistoryRecord[];
    totalCount: number;
    offset: number;
    limit: number;
}
export interface SignalEffectivenessListResponse {
    effectivenessMetrics: SignalEffectiveness[];
}
//# sourceMappingURL=index.d.ts.map