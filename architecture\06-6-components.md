### 6. Components

#### Component List

* **Backend Components:**
  * **Web Server (FastAPI):** Handles HTTP requests and routing.
  * **Signal Service:** Core business logic orchestrator.
  * **Data Ingestion Service:** Fetches and parses data from `akshare`.
  * **Strategy Engine:** Pure calculation module for the indicators.
* **Frontend Components:**
  * **API Client Service:** Manages all communication with the backend.
  * **State Management Store (Zustand):** Holds global UI state.
  * **View Components (React):** Renders the main application pages.
  * **UI Component Library (Radix + Tailwind):** Provides reusable UI building blocks.

#### Component Diagram

```mermaid
graph TD
    subgraph Frontend (Browser)
        View[View Components] --> APIClient[API Client Service];
        View --> Store[State Management Store];
        APIClient --> Store;
    end
    subgraph Backend (Render)
        WebServer[Web Server] --> SignalSvc[Signal Service];
        SignalSvc --> DataSvc[Data Ingestion Service];
        SignalSvc --> Engine[Strategy Engine];
    end
    APIClient -->|REST API| WebServer;
    DataSvc -->|fetches| Akshare[External: akshare];
```

***
