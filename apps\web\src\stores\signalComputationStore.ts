import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type {
  ComputedSignal,
  EffectivenessMetrics,
  SignalComputationRequest,
  SignalComputationResponse,
  StrategyComparison,
  StrategyComparisonResponse,
  SignalFilterOptions,
  SignalDisplayOptions,
} from '../types';
import { apiClient } from '../lib/api';

interface SignalComputationState {
  // Signal data
  signals: ComputedSignal[];
  filteredSignals: ComputedSignal[];
  effectivenessMetrics: EffectivenessMetrics | null;
  strategyComparisons: StrategyComparison[];
  
  // UI state
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  
  // Filter and display options
  filterOptions: SignalFilterOptions;
  displayOptions: SignalDisplayOptions;
  
  // Cache management
  cache: Map<string, { data: any; timestamp: number; ttl: number; lastAccessed?: number }>;
  
  // Actions
  computeSignals: (request: SignalComputationRequest) => Promise<void>;
  compareStrategies: (symbol: string, strategies: string[]) => Promise<void>;
  updateFilterOptions: (options: Partial<SignalFilterOptions>) => void;
  updateDisplayOptions: (options: Partial<SignalDisplayOptions>) => void;
  clearSignals: () => void;
  clearError: () => void;
  
  // Cache operations
  getCachedData: (key: string) => any | null;
  setCachedData: (key: string, data: any, ttl?: number) => void;
  removeCacheEntry: (key: string) => void;
  clearCache: () => void;
  clearExpiredCache: () => void;
  getCacheStats: () => { totalEntries: number; validEntries: number; expiredEntries: number; cacheSize: number; oldestEntry: number | null; newestEntry: number | null };
  
  // Internal methods
  applyFilters: () => void;
}

const DEFAULT_FILTER_OPTIONS: SignalFilterOptions = {
  signalTypes: ['buy', 'sell', 'hold'],
  minConfidence: 0,
  maxConfidence: 1,
  strategies: [],
  dateRange: {
    start: null,
    end: null,
  },
};

const DEFAULT_DISPLAY_OPTIONS: SignalDisplayOptions = {
  showTooltips: true,
  showEffectiveness: true,
  groupByStrategy: false,
  sortBy: 'timestamp',
  sortOrder: 'desc',
};

const DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export const useSignalComputationStore = create<SignalComputationState>()(devtools(
    (set, get) => ({
      // Initial state
      signals: [],
      filteredSignals: [],
      effectivenessMetrics: null,
      strategyComparisons: [],
      loading: false,
      error: null,
      lastUpdated: null,
      filterOptions: DEFAULT_FILTER_OPTIONS,
      displayOptions: DEFAULT_DISPLAY_OPTIONS,
      cache: new Map(),

      // Actions
      computeSignals: async (request: SignalComputationRequest) => {
        const cacheKey = `signals_${request.symbol}_${JSON.stringify(request.strategies)}_${request.start_date}_${request.end_date}`;
        
        // Validate inputs
        if (!request.symbol || request.symbol.trim().length === 0) {
          set({ error: 'Stock symbol is required', loading: false });
          return;
        }
        
        if (!request.strategies || request.strategies.length === 0) {
          set({ error: 'At least one strategy must be selected', loading: false });
          return;
        }
        
        if (!request.start_date || !request.end_date || new Date(request.start_date) >= new Date(request.end_date)) {
          set({ error: 'Valid date range is required', loading: false });
          return;
        }
        
        // Check cache first
        const cachedData = get().getCachedData(cacheKey);
        if (cachedData) {
          set({
            signals: cachedData.signals,
            effectivenessMetrics: cachedData.effectivenessMetrics,
            lastUpdated: new Date(cachedData.timestamp),
          });
          get().applyFilters();
          return;
        }

        set({ loading: true, error: null });
        
        try {
          const response = await apiClient.post<SignalComputationResponse>('/api/signals/compute', {
            ...request,
            stock_code: request.symbol.toUpperCase().trim()
          });
          
          if (response.success && response.data) {
            const { signals, effectivenessMetrics } = response.data;
            
            if (!Array.isArray(signals)) {
              throw new Error('Invalid response format from server');
            }
            
            // Validate signal data
            const validSignals = signals.filter(signal => 
              signal && 
              typeof signal.signal_type === 'string' &&
              typeof signal.confidence === 'number' &&
              typeof signal.price === 'number' &&
              signal.timestamp
            );
            
            if (validSignals.length !== signals.length) {
              console.warn(`Filtered out ${signals.length - validSignals.length} invalid signals`);
            }
            
            set({
              signals: validSignals,
              effectivenessMetrics,
              loading: false,
              lastUpdated: new Date(),
            });
            
            // Cache the results
            get().setCachedData(cacheKey, {
              signals: validSignals,
              effectivenessMetrics,
              timestamp: Date.now(),
            });
            
            get().applyFilters();
          } else {
            throw new Error(response.error || 'Failed to compute signals');
          }
        } catch (error) {
          console.error('Error computing signals:', error);
          
          let errorMessage = 'Failed to compute signals';
          
          if (error instanceof Error) {
            if (error.message.includes('404')) {
              errorMessage = `Stock symbol '${request.symbol}' not found`;
            } else if (error.message.includes('400')) {
              errorMessage = 'Invalid request parameters';
            } else if (error.message.includes('500')) {
              errorMessage = 'Server error occurred. Please try again later.';
            } else if (error.message.includes('network') || error.message.includes('fetch')) {
              errorMessage = 'Network error. Please check your connection.';
            } else {
              errorMessage = error.message;
            }
          }
          
          set({
            loading: false,
            error: errorMessage,
          });
        }
      },

      compareStrategies: async (symbol: string, strategies: string[]) => {
        const cacheKey = `comparison_${symbol}_${strategies.join('_')}`;
        
        // Validate inputs
        if (!symbol || symbol.trim().length === 0) {
          set({ error: 'Stock symbol is required for strategy comparison', loading: false });
          return;
        }
        
        if (!strategies || strategies.length < 2) {
          set({ error: 'At least two strategies are required for comparison', loading: false });
          return;
        }
        
        // Check cache first
        const cachedData = get().getCachedData(cacheKey);
        if (cachedData) {
          set({ strategyComparisons: cachedData });
          return;
        }

        set({ loading: true, error: null });
        
        try {
          const response = await apiClient.post<StrategyComparisonResponse>('/api/signals/compare-strategies', {
            stock_code: symbol.toUpperCase().trim(),
            strategies,
          });
          
          if (response.success && response.data) {
            const { comparisons } = response.data;
            
            if (!Array.isArray(comparisons)) {
              throw new Error('Invalid response format from server');
            }
            
            // Validate comparison data
            const validComparisons = comparisons.filter(comparison => 
              comparison && 
              typeof comparison.strategy === 'string' &&
              typeof comparison.effectiveness === 'object' &&
              typeof comparison.signal_count === 'number'
            );
            
            if (validComparisons.length !== comparisons.length) {
              console.warn(`Filtered out ${comparisons.length - validComparisons.length} invalid strategy comparisons`);
            }
            
            set({
              strategyComparisons: validComparisons,
              loading: false,
            });
            
            // Cache the results
            get().setCachedData(cacheKey, validComparisons);
          } else {
            throw new Error(response.error || 'Failed to compare strategies');
          }
        } catch (error) {
          console.error('Error comparing strategies:', error);
          
          let errorMessage = 'Failed to compare strategies';
          
          if (error instanceof Error) {
            if (error.message.includes('404')) {
              errorMessage = `Stock symbol '${symbol}' not found for strategy comparison`;
            } else if (error.message.includes('400')) {
              errorMessage = 'Invalid parameters for strategy comparison';
            } else if (error.message.includes('500')) {
              errorMessage = 'Server error during strategy comparison. Please try again later.';
            } else if (error.message.includes('network') || error.message.includes('fetch')) {
              errorMessage = 'Network error during strategy comparison. Please check your connection.';
            } else {
              errorMessage = error.message;
            }
          }
          
          set({
            loading: false,
            error: errorMessage,
          });
        }
      },

      updateFilterOptions: (options: Partial<SignalFilterOptions>) => {
        set((state) => ({
          filterOptions: { ...state.filterOptions, ...options },
        }));
        get().applyFilters();
      },

      updateDisplayOptions: (options: Partial<SignalDisplayOptions>) => {
        set((state) => ({
          displayOptions: { ...state.displayOptions, ...options },
        }));
      },

      clearSignals: () => {
        set({
          signals: [],
          filteredSignals: [],
          effectivenessMetrics: null,
          strategyComparisons: [],
          lastUpdated: null,
        });
      },

      clearError: () => {
        set({ error: null });
      },

      // Cache management with optimization
      getCachedData: (key: string) => {
        const cached = get().cache.get(key);
        
        if (!cached) {
          return null;
        }
        
        // Check if cache is expired
        if (Date.now() - cached.timestamp > cached.ttl) {
          // Remove expired entry
          get().removeCacheEntry(key);
          return null;
        }
        
        // Update access time for LRU
        cached.lastAccessed = Date.now();
        
        return cached.data;
      },

      setCachedData: (key: string, data: any, ttl: number = DEFAULT_CACHE_TTL) => {
        const state = get();
        const cache = new Map(state.cache);
        
        // Implement cache size limit (max 50 entries)
        if (cache.size >= 50) {
          // Remove oldest accessed entries
          const entries = Array.from(cache.entries());
          const sortedEntries = entries.sort((a, b) => {
            const aAccessed = a[1].lastAccessed || a[1].timestamp;
            const bAccessed = b[1].lastAccessed || b[1].timestamp;
            return aAccessed - bAccessed;
          });
          
          // Remove oldest 10 entries
          for (let i = 0; i < 10; i++) {
            cache.delete(sortedEntries[i][0]);
          }
        }
        
        // Add new cache entry
        cache.set(key, {
          data,
          timestamp: Date.now(),
          ttl,
          lastAccessed: Date.now()
        });
        
        set({ cache });
      },

      removeCacheEntry: (key: string) => {
        set(state => {
          const newCache = new Map(state.cache);
          newCache.delete(key);
          return { cache: newCache };
        });
      },

      clearCache: () => {
        set({ cache: new Map() });
      },

      clearExpiredCache: () => {
        const state = get();
        const cache = new Map(state.cache);
        const now = Date.now();
        
        for (const [key, entry] of cache.entries()) {
          if (now - entry.timestamp > entry.ttl) {
            cache.delete(key);
          }
        }
        
        set({ cache });
      },

      getCacheStats: () => {
        const cache = get().cache;
        const now = Date.now();
        const entries = Array.from(cache.values());
        
        return {
          totalEntries: entries.length,
          validEntries: entries.filter(entry => now - entry.timestamp < entry.ttl).length,
          expiredEntries: entries.filter(entry => now - entry.timestamp >= entry.ttl).length,
          cacheSize: JSON.stringify(Array.from(cache.entries())).length,
          oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : null,
          newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : null
        };
      },

      // Internal methods
      applyFilters: () => {
        const { signals, filterOptions } = get();
        
        let filtered = [...signals];
        
        // Filter by signal types
        if (filterOptions.signalTypes.length > 0) {
          filtered = filtered.filter(signal => 
            filterOptions.signalTypes.includes(signal.signal_type)
          );
        }
        
        // Filter by confidence range
        filtered = filtered.filter(signal => 
          signal.confidence >= filterOptions.minConfidence &&
          signal.confidence <= filterOptions.maxConfidence
        );
        
        // Filter by strategies
        if (filterOptions.strategies.length > 0) {
          filtered = filtered.filter(signal => 
            filterOptions.strategies.includes(signal.strategy)
          );
        }
        
        // Filter by date range
        if (filterOptions.dateRange.start) {
          filtered = filtered.filter(signal => 
            new Date(signal.timestamp) >= filterOptions.dateRange.start!
          );
        }
        
        if (filterOptions.dateRange.end) {
          filtered = filtered.filter(signal => 
            new Date(signal.timestamp) <= filterOptions.dateRange.end!
          );
        }
        
        set({ filteredSignals: filtered });
      },
    }),
    {
      name: 'signal-computation-store',
    }
  ));

// Selectors for better performance
export const useSignals = () => useSignalComputationStore(state => state.filteredSignals);
export const useEffectivenessMetrics = () => useSignalComputationStore(state => state.effectivenessMetrics);
export const useStrategyComparisons = () => useSignalComputationStore(state => state.strategyComparisons);
export const useSignalLoading = () => useSignalComputationStore(state => state.loading);
export const useSignalError = () => useSignalComputationStore(state => state.error);
export const useFilterOptions = () => useSignalComputationStore(state => state.filterOptions);
export const useDisplayOptions = () => useSignalComputationStore(state => state.displayOptions);