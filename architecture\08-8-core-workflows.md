### 8. Core Workflows

#### Workflow 1: Single Stock Analysis

```mermaid
sequenceDiagram
    participant User
    participant Frontend SPA
    participant Backend API
    participant Akshare
    User->>+Frontend SPA: Enters stock code & clicks "Get Signal"
    Frontend SPA->>+Backend API: GET /api/signal/{stock_code}
    Backend API->>+Akshare: Get historical data()
    Akshare-->>-Backend API: Return stock data
    Backend API-->>-Frontend SPA: 200 OK with SignalResult JSON
    Frontend SPA-->>-User: Display signal and chart
```

#### Workflow 2: Displaying Screener Results

```mermaid
sequenceDiagram
    participant User
    participant Frontend SPA
    participant Backend API
    participant SQLite DB
    User->>+Frontend SPA: Navigates to Screener page
    Frontend SPA->>+Backend API: GET /api/screener/results
    Backend API->>+SQLite DB: Get latest screener results()
    SQLite DB-->>-Backend API: Return cached results
    Backend API-->>-Frontend SPA: 200 OK with list of stocks
    Frontend SPA-->>-User: Display list of flagged stocks
```

***
