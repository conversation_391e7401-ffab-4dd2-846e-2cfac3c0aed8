"""Performance tests for ScreeningService."""

import time
from datetime import datetime
from unittest.mock import Mock, patch

import pytest

from src.features.screener.screening_service import ScreeningService
from src.models.strategy import ChartData, SignalResult


class TestScreeningPerformance:
    """Performance test cases for ScreeningService."""

    @pytest.fixture
    def performance_service(self) -> ScreeningService:
        """Create ScreeningService instance for performance testing."""
        return ScreeningService(max_concurrent=10, rate_limit_delay=0.001)

    @pytest.fixture
    def mock_csi_300_stocks(self) -> list[str]:
        """Mock CSI 300 stock list for performance testing."""
        return [f"{str(i).zfill(6)}.{'SZ' if i % 2 == 0 else 'SH'}" for i in range(1, 301)]

    @pytest.fixture
    def mock_signal_result_fast(self) -> SignalResult:
        """Fast mock SignalResult for performance testing."""
        return SignalResult(
            symbol="000001.SZ",
            last_scan_date=datetime.now().isoformat(),
            signal="HOLD",
            chart_data=ChartData(
                daily_prices=[],
                magic_nine_sequence=[],
                macd_line=[],
                signal_line=[],
                divergence_points=[]
            )
        )

    @pytest.mark.asyncio
    async def test_concurrent_processing_performance(self, performance_service: ScreeningService, mock_signal_result_fast: SignalResult) -> None:
        """Test concurrent processing performance with multiple stocks."""
        # Test with 50 stocks to simulate reasonable load
        test_stocks = [f"00000{i:02d}.SZ" for i in range(1, 51)]

        with patch.object(performance_service.signal_service, 'get_signal_for_stock', return_value=mock_signal_result_fast), \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 0
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            start_time = time.time()
            result = await performance_service.run_screening(test_stocks)
            end_time = time.time()

            processing_time = end_time - start_time

            # Performance assertions
            assert result['total_stocks_processed'] == 50
            assert processing_time < 10  # Should complete within 10 seconds
            assert result['processing_time_seconds'] < 10

            # Calculate throughput
            throughput = result['total_stocks_processed'] / processing_time
            assert throughput > 5  # At least 5 stocks per second


    @pytest.mark.asyncio
    async def test_cache_performance_improvement(self, performance_service: ScreeningService, mock_signal_result_fast: SignalResult) -> None:
        """Test that caching improves performance on repeated requests."""
        test_stocks = ["000001.SZ", "000002.SZ", "000003.SZ"]

        with patch.object(performance_service.signal_service, 'get_signal_for_stock', return_value=mock_signal_result_fast) as mock_signal, \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 0
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            # First run - populate cache
            start_time = time.time()
            await performance_service.run_screening(test_stocks)
            first_run_time = time.time() - start_time

            # Reset mock to count second run calls
            mock_signal.reset_mock()

            # Second run - should use cache
            start_time = time.time()
            await performance_service.run_screening(test_stocks)
            second_run_time = time.time() - start_time
            second_run_calls = mock_signal.call_count

            # Cache should reduce API calls to 0
            assert second_run_calls == 0, "Second run should use cache, not make API calls"

            # Second run should be faster (allowing for some variance)
            assert second_run_time < first_run_time, "Cached run should be faster than initial run"

            # Verify cache stats
            cache_stats = performance_service.get_cache_stats()
            assert cache_stats['valid_entries'] == 3
            assert cache_stats['total_entries'] == 3


    @pytest.mark.asyncio
    async def test_rate_limiting_enforcement(self, performance_service: ScreeningService) -> None:
        """Test that rate limiting is properly enforced."""
        # Use higher rate limit delay for this test
        rate_limited_service = ScreeningService(max_concurrent=2, rate_limit_delay=0.1)

        test_stocks = ["000001.SZ", "000002.SZ"]

        with patch.object(rate_limited_service.signal_service, 'get_signal_for_stock') as mock_signal, \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock fast signal processing
            mock_signal.return_value = SignalResult(
                symbol="test",
                last_scan_date=datetime.now().isoformat(),
                signal="HOLD",
                chart_data=ChartData(
                    daily_prices=[],
                    magic_nine_sequence=[],
                    macd_line=[],
                    signal_line=[],
                    divergence_points=[]
                )
            )

            # Mock repository
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 0
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            start_time = time.time()
            await rate_limited_service.run_screening(test_stocks)
            end_time = time.time()

            processing_time = end_time - start_time

            # With 0.1s rate limit delay and 2 stocks, should take at least 0.2s
            # (accounting for concurrent processing)
            assert processing_time >= 0.1, f"Rate limiting not enforced: took only {processing_time:.3f}s"

    def test_cache_stats_and_management(self, performance_service: ScreeningService) -> None:
        """Test cache statistics and management functions."""
        # Initially empty cache
        stats = performance_service.get_cache_stats()
        assert stats['total_entries'] == 0
        assert stats['valid_entries'] == 0
        assert stats['expired_entries'] == 0

        # Add some cache entries manually for testing
        from datetime import datetime, timedelta

        from src.features.screener.screening_service import CacheEntry

        # Add valid entry
        performance_service._cache['000001.SZ'] = CacheEntry(
            signal_result=None,
            timestamp=datetime.now(),
            is_sell_candidate=False
        )

        # Add expired entry
        performance_service._cache['000002.SZ'] = CacheEntry(
            signal_result=None,
            timestamp=datetime.now() - timedelta(hours=5),  # Expired (TTL is 4 hours)
            is_sell_candidate=False
        )

        # Check stats
        stats = performance_service.get_cache_stats()
        assert stats['total_entries'] == 2
        assert stats['valid_entries'] == 1
        assert stats['expired_entries'] == 1

        # Clear cache
        performance_service.clear_cache()
        stats = performance_service.get_cache_stats()
        assert stats['total_entries'] == 0

    @pytest.mark.asyncio
    async def test_error_caching_prevents_repeated_failures(self, performance_service: ScreeningService) -> None:
        """Test that error caching prevents repeated processing of failing stocks."""
        test_stocks = ["INVALID.XX"]

        with patch.object(performance_service.signal_service, 'get_signal_for_stock', side_effect=ValueError("Invalid stock")) as mock_signal, \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 0
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            # First run - should call API and cache error
            await performance_service.run_screening(test_stocks)
            first_run_calls = mock_signal.call_count
            assert first_run_calls == 1

            # Reset mock
            mock_signal.reset_mock()

            # Second run - should use cached error result
            await performance_service.run_screening(test_stocks)
            second_run_calls = mock_signal.call_count
            assert second_run_calls == 0, "Second run should use cached error result"

            # Verify cache contains error result
            cache_stats = performance_service.get_cache_stats()
            assert cache_stats['valid_entries'] == 1
