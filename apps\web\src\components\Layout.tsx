import { ReactNode } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'
import { LanguageSelector } from './LanguageSelector'

interface LayoutProps {
  children: ReactNode
}

export function Layout({ children }: LayoutProps) {
  const location = useLocation()
  const { t } = useTranslation('common')

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between" role="navigation" aria-label="Main navigation">
            <h1 className="text-2xl font-bold">Trading Agent</h1>
            <div className="flex items-center space-x-4">
              <ul className="flex space-x-4">
              <li>
                <Link
                  to="/analysis"
                  className={cn(
                    "px-3 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                    isActive('/analysis') || isActive('/')
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted"
                  )}
                  aria-current={isActive('/analysis') || isActive('/') ? 'page' : undefined}
                >
                  {t('navigation.analysis')}
                </Link>
              </li>

              <li>
                <Link
                  to="/screener"
                  className={cn(
                    "px-3 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                    isActive('/screener')
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:bg-muted"
                  )}
                  aria-current={isActive('/screener') ? 'page' : undefined}
                >
                  {t('navigation.screener')}
                </Link>
              </li>
              </ul>
              <LanguageSelector />
            </div>
          </nav>
        </div>
      </header>
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  )
}