"""Integration tests for data router endpoints."""

from unittest.mock import patch

import pandas as pd
import pytest
from fastapi.testclient import TestClient

from src.main import app
from src.models.stock_data import DailyPrice, StockData


class TestDataRouter:
    """Test cases for data router endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def mock_stock_data(self):
        """Create mock StockData for testing."""
        daily_prices = [
            DailyPrice(
                date='2024-01-02',
                open=9.39,
                high=9.42,
                low=9.21,
                close=9.21,
                volume=1158366
            ),
            DailyPrice(
                date='2024-01-03',
                open=9.19,
                high=9.22,
                low=9.15,
                close=9.20,
                volume=733610
            )
        ]
        return StockData(symbol='000001', daily_prices=daily_prices)

    @pytest.fixture
    def mock_akshare_data(self):
        """Create mock akshare DataFrame."""
        data = {
            'col0': ['2024-01-02', '2024-01-03'],  # Date
            'col1': ['000001', '000001'],  # Symbol
            'col2': [9.39, 9.19],  # Open
            'col3': [9.21, 9.20],  # Close
            'col4': [9.42, 9.22],  # High
            'col5': [9.21, 9.15],  # Low
            'col6': [1158366, 733610],  # Volume
            'col7': [1075742252.45, 673673600],  # Amount
            'col8': [2.24, 0.76],  # Amplitude
            'col9': [-1.92, -0.11],  # Change pct
            'col10': [-0.18, -0.01],  # Change amount
            'col11': [0.60, 0.38]  # Turnover
        }
        return pd.DataFrame(data)

    def test_get_stock_data_success(self, client, mock_akshare_data):
        """Test successful stock data retrieval."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            response = client.get(
                "/api/data/000001",
                params={"start_date": "2024-01-01", "end_date": "2024-01-05"}
            )

            assert response.status_code == 200
            data = response.json()

            assert data["symbol"] == "000001"
            assert "daily_prices" in data
            assert len(data["daily_prices"]) == 2

            # Verify first record structure
            first_price = data["daily_prices"][0]
            assert first_price["date"] == "2024-01-02"
            assert first_price["open"] == 9.39
            assert first_price["close"] == 9.21
            assert first_price["high"] == 9.42
            assert first_price["low"] == 9.21
            assert first_price["volume"] == 1158366

    def test_get_stock_data_no_dates(self, client, mock_akshare_data):
        """Test stock data retrieval without date parameters."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            response = client.get("/api/data/000001")

            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == "000001"
            assert len(data["daily_prices"]) == 2

    def test_get_stock_data_invalid_stock_code_format(self, client):
        """Test invalid stock code format."""
        # Test non-6-digit code
        response = client.get("/api/data/12345")
        assert response.status_code == 404
        assert "Invalid stock code format" in response.json()["detail"]

        # Test non-numeric code
        response = client.get("/api/data/ABCDEF")
        assert response.status_code == 404
        assert "Invalid stock code format" in response.json()["detail"]

        # Test empty code
        response = client.get("/api/data/")
        assert response.status_code == 404  # FastAPI route not found

    def test_get_stock_data_stock_not_found(self, client):
        """Test stock code not found scenario."""
        empty_df = pd.DataFrame()

        with patch('akshare.stock_zh_a_hist', return_value=empty_df):
            response = client.get("/api/data/999999")

            assert response.status_code == 404
            assert "Stock code not found or data unavailable" in response.json()["detail"]

    def test_get_stock_data_service_unavailable(self, client):
        """Test akshare service unavailable scenario."""
        with patch('akshare.stock_zh_a_hist', side_effect=Exception("Network error")):
            response = client.get("/api/data/000001")

            assert response.status_code == 500
            assert "Data service unavailable" in response.json()["detail"]

    def test_get_stock_data_invalid_date_format(self, client):
        """Test invalid date format in query parameters."""
        response = client.get(
            "/api/data/000001",
            params={"start_date": "2024/01/01", "end_date": "2024-01-05"}
        )

        # FastAPI should return 422 for invalid query parameter format
        assert response.status_code == 422

    def test_health_check_stock_data_available(self, client, mock_akshare_data):
        """Test health check endpoint with available stock."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            response = client.get("/api/data/000001/health")

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "available"
            assert data["stock_code"] == "000001"
            assert data["data_available"] is True

    def test_health_check_stock_data_unavailable(self, client):
        """Test health check endpoint with unavailable stock."""
        empty_df = pd.DataFrame()

        with patch('akshare.stock_zh_a_hist', return_value=empty_df):
            response = client.get("/api/data/999999/health")

            assert response.status_code == 404
            data = response.json()
            assert data["status"] == "unavailable"
            assert data["stock_code"] == "999999"
            assert data["data_available"] is False
            assert "error" in data

    @pytest.mark.parametrize("stock_code,expected_status", [
        ("000001", 200),  # Valid stock code
        ("600000", 200),  # Another valid stock code
        ("12345", 404),   # Invalid format (5 digits)
        ("1234567", 404), # Invalid format (7 digits)
        ("ABCDEF", 404),  # Invalid format (letters)
    ])
    def test_stock_code_validation(self, client, mock_akshare_data, stock_code, expected_status):
        """Test stock code validation with various inputs."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            response = client.get(f"/api/data/{stock_code}")
            assert response.status_code == expected_status

    def test_date_range_validation(self, client, mock_akshare_data):
        """Test date range parameter validation."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            # Valid date range
            response = client.get(
                "/api/data/000001",
                params={"start_date": "2024-01-01", "end_date": "2024-01-05"}
            )
            assert response.status_code == 200

            # Only start date
            response = client.get(
                "/api/data/000001",
                params={"start_date": "2024-01-01"}
            )
            assert response.status_code == 200

            # Only end date
            response = client.get(
                "/api/data/000001",
                params={"end_date": "2024-01-05"}
            )
            assert response.status_code == 200

    def test_response_content_type(self, client, mock_akshare_data):
        """Test response content type is JSON."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            response = client.get("/api/data/000001")

            assert response.status_code == 200
            assert response.headers["content-type"] == "application/json"

    def test_cors_middleware_configured(self, client, mock_akshare_data):
        """Test that CORS middleware allows the request (doesn't block)."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            response = client.get(
                "/api/data/000001",
                headers={"Origin": "http://localhost:5173"}
            )

            assert response.status_code == 200
            # If CORS was blocking, we'd get a 4xx error instead of 200
