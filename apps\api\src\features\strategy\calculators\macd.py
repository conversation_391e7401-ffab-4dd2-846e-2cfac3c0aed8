"""MACD (Moving Average Convergence Divergence) technical indicator calculator with top divergence detection."""

import logging
from typing import Optional

from ....models.stock_data import DailyPrice
from ....models.strategy import DivergencePoint, MACDResult

logger = logging.getLogger(__name__)


class MACDCalculator:
    """Calculator for MACD indicator with top divergence detection."""

    def __init__(
        self,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
        divergence_lookback: int = 20
    ):
        """
        Initialize MACD calculator.

        Args:
            fast_period: Fast EMA period (default: 12)
            slow_period: Slow EMA period (default: 26)
            signal_period: Signal line EMA period (default: 9)
            divergence_lookback: Lookback period for divergence detection (default: 20)
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.divergence_lookback = divergence_lookback

        # Validation
        if fast_period >= slow_period:
            raise ValueError("Fast period must be less than slow period")
        if divergence_lookback < 5:
            raise ValueError("Divergence lookback must be at least 5 periods")

    def calculate(self, daily_prices: list[DailyPrice]) -> MACDResult:
        """
        Calculate MACD indicator and detect top divergences.

        Args:
            daily_prices: List of daily price data, must be sorted by date

        Returns:
            MACDResult with MACD values and divergence points

        Raises:
            ValueError: If insufficient data points
        """
        min_required = max(self.slow_period, self.divergence_lookback) + self.signal_period
        if len(daily_prices) < min_required:
            raise ValueError(
                f"Need at least {min_required} data points for MACD calculation, "
                f"got {len(daily_prices)}"
            )

        logger.debug(f"Calculating MACD for {len(daily_prices)} data points")

        # Extract close prices
        closes = [price.close for price in daily_prices]

        # Calculate EMAs
        fast_ema = self._calculate_ema(closes, self.fast_period)
        slow_ema = self._calculate_ema(closes, self.slow_period)

        # Calculate MACD line
        macd_line = self._calculate_macd_line(fast_ema, slow_ema)

        # Calculate signal line
        signal_line = self._calculate_signal_line(macd_line)

        # Calculate histogram
        histogram = self._calculate_histogram(macd_line, signal_line)

        # Detect top divergences
        divergence_points = self._detect_top_divergences(daily_prices, macd_line)

        result = MACDResult(
            macd_line=macd_line,
            signal_line=signal_line,
            histogram=histogram,
            divergence_points=divergence_points
        )

        logger.debug(f"MACD calculation completed, found {len(divergence_points)} divergence points")
        return result

    def _calculate_ema(self, values: list[float], period: int) -> list[Optional[float]]:
        """
        Calculate Exponential Moving Average.

        Args:
            values: List of values to calculate EMA for
            period: EMA period

        Returns:
            List of EMA values (None for insufficient data)
        """
        if len(values) < period:
            return [None] * len(values)

        multiplier = 2.0 / (period + 1)
        ema_values: list[Optional[float]] = [None] * len(values)

        # Use SMA for the first EMA value
        sma_sum = sum(values[:period])
        ema_values[period - 1] = sma_sum / period

        # Calculate EMA for remaining values
        for i in range(period, len(values)):
            prev_ema = ema_values[i - 1]
            if prev_ema is not None:
                ema_values[i] = (values[i] * multiplier) + (prev_ema * (1 - multiplier))

        return ema_values

    def _calculate_macd_line(
        self,
        fast_ema: list[Optional[float]],
        slow_ema: list[Optional[float]]
    ) -> list[Optional[float]]:
        """
        Calculate MACD line (fast EMA - slow EMA).

        Args:
            fast_ema: Fast EMA values
            slow_ema: Slow EMA values

        Returns:
            MACD line values
        """
        macd_line: list[Optional[float]] = []
        for fast, slow in zip(fast_ema, slow_ema):
            if fast is not None and slow is not None:
                macd_line.append(fast - slow)
            else:
                macd_line.append(None)
        return macd_line

    def _calculate_signal_line(self, macd_line: list[Optional[float]]) -> list[Optional[float]]:
        """
        Calculate signal line (EMA of MACD line).

        Args:
            macd_line: MACD line values

        Returns:
            Signal line values
        """
        # Filter out None values for EMA calculation
        macd_values = [val for val in macd_line if val is not None]

        if len(macd_values) < self.signal_period:
            return [None] * len(macd_line)

        # Calculate EMA of MACD values
        signal_ema = self._calculate_ema(macd_values, self.signal_period)

        # Map back to original indices
        signal_line: list[Optional[float]] = [None] * len(macd_line)
        macd_index = 0

        for i, macd_val in enumerate(macd_line):
            if macd_val is not None:
                signal_line[i] = signal_ema[macd_index]
                macd_index += 1

        return signal_line

    def _calculate_histogram(
        self,
        macd_line: list[Optional[float]],
        signal_line: list[Optional[float]]
    ) -> list[Optional[float]]:
        """
        Calculate MACD histogram (MACD line - signal line).

        Args:
            macd_line: MACD line values
            signal_line: Signal line values

        Returns:
            Histogram values
        """
        histogram: list[Optional[float]] = []
        for macd, signal in zip(macd_line, signal_line):
            if macd is not None and signal is not None:
                histogram.append(macd - signal)
            else:
                histogram.append(None)
        return histogram

    def _detect_top_divergences(
        self,
        daily_prices: list[DailyPrice],
        macd_line: list[Optional[float]]
    ) -> list[DivergencePoint]:
        """
        Detect top divergences between price and MACD.

        Top divergence occurs when:
        - Price makes higher highs
        - MACD makes lower highs

        Args:
            daily_prices: Daily price data
            macd_line: MACD line values

        Returns:
            List of divergence points
        """
        divergence_points: list[DivergencePoint] = []

        # Need sufficient data for divergence detection
        if len(daily_prices) < self.divergence_lookback * 2:
            return divergence_points

        # Find peaks in both price and MACD
        price_peaks = self._find_peaks([price.high for price in daily_prices])
        macd_peaks = self._find_peaks([val if val is not None else 0.0 for val in macd_line])

        # Look for divergences in recent periods
        recent_start = len(daily_prices) - self.divergence_lookback

        for i in range(recent_start, len(daily_prices)):
            if i in price_peaks and i in macd_peaks:
                # Check for divergence pattern
                if self._is_top_divergence(daily_prices, macd_line, i, price_peaks, macd_peaks):
                    divergence_points.append(DivergencePoint(
                        date=daily_prices[i].date,
                        type='TOP'
                    ))

        return divergence_points

    def _find_peaks(self, values: list[float], min_distance: int = 3) -> list[int]:
        """
        Find local peaks in a series of values.

        Args:
            values: Values to find peaks in
            min_distance: Minimum distance between peaks

        Returns:
            List of peak indices
        """
        peaks = []

        for i in range(min_distance, len(values) - min_distance):
            is_peak = True

            # Check if this point is higher than surrounding points
            for j in range(i - min_distance, i + min_distance + 1):
                if j != i and values[j] >= values[i]:
                    is_peak = False
                    break

            if is_peak:
                peaks.append(i)

        return peaks

    def _is_top_divergence(
        self,
        daily_prices: list[DailyPrice],
        macd_line: list[Optional[float]],
        current_index: int,
        price_peaks: list[int],
        macd_peaks: list[int]
    ) -> bool:
        """
        Check if there's a top divergence at the current index.

        Args:
            daily_prices: Daily price data
            macd_line: MACD line values
            current_index: Current index to check
            price_peaks: List of price peak indices
            macd_peaks: List of MACD peak indices

        Returns:
            True if top divergence detected
        """
        # Find the previous peak within lookback period
        lookback_start = max(0, current_index - self.divergence_lookback)

        # Find previous price peak
        prev_price_peak = None
        for peak_idx in reversed(price_peaks):
            if lookback_start <= peak_idx < current_index:
                prev_price_peak = peak_idx
                break

        # Find previous MACD peak
        prev_macd_peak = None
        for peak_idx in reversed(macd_peaks):
            if lookback_start <= peak_idx < current_index:
                prev_macd_peak = peak_idx
                break

        # Check for divergence pattern
        if prev_price_peak is not None and prev_macd_peak is not None:
            current_price = daily_prices[current_index].high
            prev_price = daily_prices[prev_price_peak].high

            current_macd = macd_line[current_index]
            prev_macd = macd_line[prev_macd_peak]

            if current_macd is not None and prev_macd is not None:
                # Top divergence: higher price high, lower MACD high
                return current_price > prev_price and current_macd < prev_macd

        return False
