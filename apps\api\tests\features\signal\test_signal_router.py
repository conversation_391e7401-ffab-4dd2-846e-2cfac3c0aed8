"""Integration tests for signal router."""

from unittest.mock import Mock, patch

from fastapi.testclient import TestClient

from src.main import app
from src.models.stock_data import DailyPrice
from src.models.strategy import ChartData, DivergencePoint, SignalResult


class TestSignalRouter:
    """Test cases for signal router endpoints."""

    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)

        # Create mock data with enough points for MACD (35+ required)
        self.mock_daily_prices = []
        for i in range(40):  # Create 40 data points
            date = f"2025-07-{str(i+1).zfill(2)}" if i < 31 else f"2025-08-{str(i-30).zfill(2)}"
            price = 100.0 + i * 0.5  # Gradually increasing prices
            self.mock_daily_prices.append(
                DailyPrice(
                    date=date,
                    open=price,
                    high=price + 2.0,
                    low=price - 2.0,
                    close=price + 1.0,
                    volume=1000 + i * 10
                )
            )

        self.mock_chart_data = ChartData(
            daily_prices=self.mock_daily_prices,
            magic_nine_sequence=[None] * 37 + [1, 2, 3],  # Match the 40 data points
            macd_line=[None] * 35 + [0.5, 0.6, 0.7, 0.8, 0.9],  # Start after 35 points
            signal_line=[None] * 35 + [0.4, 0.5, 0.6, 0.7, 0.8],
            divergence_points=[
                DivergencePoint(date="2025-08-09", type="TOP")
            ]
        )

        self.mock_signal_result = SignalResult(
            symbol="000001",
            last_scan_date="2025-08-17T10:00:00Z",
            signal="SELL_CANDIDATE",
            chart_data=self.mock_chart_data
        )

    @patch('src.features.signal.router.SignalService')
    def test_get_signal_success(self, mock_signal_service_class):
        """Test successful signal endpoint call."""
        # Setup mock
        mock_service = Mock()
        mock_service.get_signal_for_stock.return_value = self.mock_signal_result
        mock_signal_service_class.return_value = mock_service

        # Make request
        response = self.client.get("/api/signal/000001")

        # Verify response
        assert response.status_code == 200
        data = response.json()

        assert data["symbol"] == "000001"
        assert data["signal"] == "SELL_CANDIDATE"
        assert data["last_scan_date"] == "2025-08-17T10:00:00Z"
        assert "chart_data" in data
        assert len(data["chart_data"]["daily_prices"]) == 40
        assert len(data["chart_data"]["divergence_points"]) == 1

        # Verify service was called correctly
        mock_service.get_signal_for_stock.assert_called_once_with(
            stock_code="000001",
            start_date=None,
            end_date=None
        )

    @patch('src.features.signal.router.SignalService')
    def test_get_signal_with_date_params(self, mock_signal_service_class):
        """Test signal endpoint with date parameters."""
        # Setup mock
        mock_service = Mock()
        mock_service.get_signal_for_stock.return_value = self.mock_signal_result
        mock_signal_service_class.return_value = mock_service

        # Make request with date parameters
        response = self.client.get(
            "/api/signal/000001",
            params={"start_date": "2025-07-01", "end_date": "2025-08-17"}
        )

        # Verify response
        assert response.status_code == 200

        # Verify service was called with date parameters
        mock_service.get_signal_for_stock.assert_called_once_with(
            stock_code="000001",
            start_date="2025-07-01",
            end_date="2025-08-17"
        )

    def test_get_signal_empty_stock_code(self):
        """Test error handling for empty stock code."""
        response = self.client.get("/api/signal/")
        assert response.status_code == 404  # FastAPI returns 404 for missing path parameter

    def test_get_signal_whitespace_stock_code(self):
        """Test error handling for whitespace-only stock code."""
        response = self.client.get("/api/signal/   ")
        assert response.status_code == 400
        assert "Stock code is required" in response.json()["detail"]

    @patch('src.features.signal.router.SignalService')
    def test_get_signal_invalid_stock_code(self, mock_signal_service_class):
        """Test error handling for invalid stock code."""
        # Setup mock to raise ValueError
        mock_service = Mock()
        mock_service.get_signal_for_stock.side_effect = ValueError("Invalid stock code: INVALID")
        mock_signal_service_class.return_value = mock_service

        # Make request
        response = self.client.get("/api/signal/INVALID")

        # Verify error response
        assert response.status_code == 404
        assert "Invalid stock code: INVALID" in response.json()["detail"]

    @patch('src.features.signal.router.SignalService')
    def test_get_signal_service_unavailable(self, mock_signal_service_class):
        """Test error handling for service unavailability."""
        # Setup mock to raise RuntimeError
        mock_service = Mock()
        mock_service.get_signal_for_stock.side_effect = RuntimeError("akshare service unavailable")
        mock_signal_service_class.return_value = mock_service

        # Make request
        response = self.client.get("/api/signal/000001")

        # Verify error response
        assert response.status_code == 500
        assert "Signal analysis service temporarily unavailable" in response.json()["detail"]

    @patch('src.features.signal.router.SignalService')
    def test_get_signal_unexpected_error(self, mock_signal_service_class):
        """Test error handling for unexpected errors."""
        # Setup mock to raise unexpected exception
        mock_service = Mock()
        mock_service.get_signal_for_stock.side_effect = Exception("Unexpected error")
        mock_signal_service_class.return_value = mock_service

        # Make request
        response = self.client.get("/api/signal/000001")

        # Verify error response
        assert response.status_code == 500
        assert "Internal server error" in response.json()["detail"]

    @patch('src.features.signal.router.SignalService')
    def test_get_signal_response_model_validation(self, mock_signal_service_class):
        """Test that response matches SignalResult model structure."""
        # Setup mock
        mock_service = Mock()
        mock_service.get_signal_for_stock.return_value = self.mock_signal_result
        mock_signal_service_class.return_value = mock_service

        # Make request
        response = self.client.get("/api/signal/000001")

        # Verify response structure matches SignalResult model
        assert response.status_code == 200
        data = response.json()

        # Check required fields (note: FastAPI converts snake_case to camelCase in responses)
        required_fields = ["symbol", "last_scan_date", "signal", "chart_data"]
        for field in required_fields:
            assert field in data

        # Check chart data structure
        chart_data = data["chart_data"]
        chart_required_fields = ["daily_prices", "magic_nine_sequence", "macd_line", "signal_line", "divergence_points"]
        for field in chart_required_fields:
            assert field in chart_data

        # Check signal enum value
        assert data["signal"] in ["SELL_CANDIDATE", "HOLD", "NO_SIGNAL"]

    def test_signal_endpoint_in_openapi_schema(self):
        """Test that signal endpoint is properly documented in OpenAPI schema."""
        response = self.client.get("/docs")
        assert response.status_code == 200  # OpenAPI docs are accessible

        # Get OpenAPI schema
        openapi_response = self.client.get("/openapi.json")
        assert openapi_response.status_code == 200

        schema = openapi_response.json()
        paths = schema.get("paths", {})

        # Verify signal endpoint is documented
        signal_path = "/api/signal/{stock_code}"
        assert signal_path in paths
        assert "get" in paths[signal_path]

        # Verify response model is defined
        get_operation = paths[signal_path]["get"]
        assert "responses" in get_operation
        assert "200" in get_operation["responses"]
