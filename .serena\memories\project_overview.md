# Trading Agent Web Application - Project Overview

## Purpose
A comprehensive fullstack web application for trading signal analysis and stock screening using Chinese stock market data.

## Key Features
- **Single Stock Analysis**: Get trading signals for individual stocks
- **Stock Screener**: View latest flagged stocks meeting criteria
- **Real-time Charts**: Interactive charts with technical indicators

## Architecture Type
**Jamstack** application with:
- Frontend: React SPA
- Backend: Python FastAPI REST API
- Database: SQLite for data persistence
- Monorepo: Managed with npm workspaces and Turborepo

## Technology Stack
### Frontend
- **Framework**: React 18+ with TypeScript 5.4+
- **Build Tool**: Vite
- **UI Library**: Radix UI components
- **Styling**: Tailwind CSS (utility-first approach)
- **State Management**: Zustand
- **Routing**: React Router
- **Testing**: Vitest + React Testing Library

### Backend
- **Framework**: FastAPI
- **Language**: Python 3.11+
- **Database**: SQLite 3+ with SQLAlchemy ORM
- **Data Source**: akshare library for Chinese stock market data
- **Testing**: Pytest

### Shared
- **Monorepo**: npm workspaces + Turborepo
- **E2E Testing**: Playwright
- **Type Safety**: Shared TypeScript types package

## Key Requirements
- Node.js v18+
- Python 3.11+
- Git

## Development Environment
- Frontend runs on localhost:5173 (Vite dev server)
- Backend runs on localhost:8000 (uvicorn with auto-reload)
- API proxy configured: `/api/*` → `http://localhost:8000`