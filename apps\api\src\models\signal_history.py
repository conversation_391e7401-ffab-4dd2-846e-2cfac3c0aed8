"""SQLAlchemy models for historical signal data."""

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, Float, Index, Integer, String, Text
from sqlalchemy.sql import func

from ..core.database import Base


class SignalHistory(Base):
    """Database model for historical trading signals."""

    __tablename__ = "signal_history"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    signal_type = Column(String(20), nullable=False)  # 'BUY', 'SELL', 'HOLD'
    signal_strength = Column(Float, nullable=True)  # 0.0 to 1.0 confidence
    trigger_price = Column(Float, nullable=False)
    trigger_date = Column(DateTime, nullable=False, index=True)
    
    # Strategy context
    strategy_name = Column(String(50), nullable=False, default="magic_nine_macd")
    strategy_params = Column(Text, nullable=True)  # JSON string of parameters
    
    # Market context at signal time
    volume = Column(Float, nullable=True)
    market_cap = Column(Float, nullable=True)
    
    # Effectiveness tracking
    exit_price = Column(Float, nullable=True)
    exit_date = Column(DateTime, nullable=True)
    return_percentage = Column(Float, nullable=True)
    holding_period_days = Column(Integer, nullable=True)
    is_successful = Column(String(10), nullable=True)  # 'SUCCESS', 'LOSS', 'PENDING'
    
    # Metadata
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Create composite indexes for performance
    __table_args__ = (
        Index('idx_symbol_date', 'symbol', 'trigger_date'),
        Index('idx_signal_type_date', 'signal_type', 'trigger_date'),
        Index('idx_strategy_date', 'strategy_name', 'trigger_date'),
    )

    def __repr__(self) -> str:
        """String representation of SignalHistory."""
        return f"<SignalHistory(symbol={self.symbol}, signal_type={self.signal_type}, trigger_date={self.trigger_date})>"


class SignalEffectiveness(Base):
    """Database model for aggregated signal effectiveness metrics."""

    __tablename__ = "signal_effectiveness"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    signal_type = Column(String(20), nullable=False)  # 'BUY', 'SELL'
    strategy_name = Column(String(50), nullable=False, default="magic_nine_macd")
    
    # Time period for metrics
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Effectiveness metrics
    total_signals = Column(Integer, nullable=False, default=0)
    successful_signals = Column(Integer, nullable=False, default=0)
    success_rate = Column(Float, nullable=False, default=0.0)  # 0.0 to 1.0
    
    # Return metrics
    avg_return = Column(Float, nullable=False, default=0.0)
    max_return = Column(Float, nullable=False, default=0.0)
    min_return = Column(Float, nullable=False, default=0.0)
    total_return = Column(Float, nullable=False, default=0.0)
    
    # Risk metrics
    max_drawdown = Column(Float, nullable=False, default=0.0)
    avg_holding_period = Column(Float, nullable=False, default=0.0)  # days
    volatility = Column(Float, nullable=False, default=0.0)
    
    # Benchmark comparison
    benchmark_return = Column(Float, nullable=True)  # buy-and-hold return
    alpha = Column(Float, nullable=True)  # excess return vs benchmark
    
    # Metadata
    last_calculated = Column(DateTime, default=func.now(), nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Create composite indexes for performance
    __table_args__ = (
        Index('idx_symbol_strategy_period', 'symbol', 'strategy_name', 'period_start', 'period_end'),
        Index('idx_signal_type_period', 'signal_type', 'period_start', 'period_end'),
    )

    def __repr__(self) -> str:
        """String representation of SignalEffectiveness."""
        return f"<SignalEffectiveness(symbol={self.symbol}, signal_type={self.signal_type}, success_rate={self.success_rate})>"