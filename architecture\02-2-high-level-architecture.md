### 2. High Level Architecture

#### Technical Summary

The application will be architected as a modern **Jamstack** application. It will consist of a responsive Single-Page Application (SPA) frontend and a pragmatic, monolithic **Python backend API**. The backend's sole responsibility is to ingest data from external sources, execute the core trading strategy, and expose the results via a simple REST API. This lean, decoupled approach is designed for rapid development and validation of the MVP, with clear paths for future scaling.

#### Platform and Infrastructure Choice

* **Recommendation:** **Vercel** for the frontend and **Render** for the Python backend.
* **Rationale:** Vercel provides best-in-class performance and continuous deployment for modern web frontends. Render offers a simple, cost-effective way to deploy a Python web service with a persistent disk for the SQLite database. This combination is fast to set up, has generous free tiers, and avoids the significant operational overhead of a full cloud provider like AWS for an MVP.

#### Repository Structure

* **Structure:** A **Monorepo** managed with **npm workspaces**.
* **Rationale:** As decided in the PRD, this will keep the frontend and backend in a single repository, simplifying dependency management and making it easier to share code, such as data types.

#### High Level Architecture Diagram

```mermaid
graph TD
    User --> Browser[Browser: Frontend SPA on Vercel];
    Browser -->|REST API Call| Backend[Python Backend on Render];
    Backend -->|Fetches Data| Akshare[akshare Library];
    Backend -->|Reads/Writes| DB[(SQLite Database)];
    Akshare --> Backend;
    DB --> Backend;
```

#### Architectural Patterns

* **Jamstack Architecture:** The frontend will be a static or server-rendered application hosted on a global CDN (Vercel), interacting with the backend via APIs. **Rationale:** This pattern provides excellent performance, security, and scalability for the client application.
* **Component-Based UI:** The frontend will be built using reusable components (e.g., in React or Vue). **Rationale:** This ensures a maintainable, consistent, and scalable frontend codebase.
* **Repository Pattern (Backend):** A data access layer will abstract the database logic. **Rationale:** Although we are starting with SQLite, this pattern will make the planned future migration to PostgreSQL seamless with minimal code changes.
* **REST API:** A standard RESTful interface will be used for all frontend-backend communication. **Rationale:** It is a simple, well-understood, and stateless communication pattern that is perfectly sufficient for the application's needs.

***
