"""Pydantic models for API responses."""


from pydantic import BaseModel


class ScreenerItem(BaseModel):
    """
    Individual screener result item.

    Represents a stock that meets the screening criteria.
    """
    symbol: str
    companyName: str

    class Config:
        """Pydantic configuration."""
        json_schema_extra = {
            "example": {
                "symbol": "000001.SZ",
                "companyName": "Ping An Bank Co., Ltd."
            }
        }


class ScreenerResultsResponse(BaseModel):
    """
    Response model for GET /api/screener/results endpoint.

    Contains a list of stocks that meet the screening criteria from the most recent scan.
    """
    results: list[ScreenerItem]

    class Config:
        """Pydantic configuration."""
        json_schema_extra = {
            "example": {
                "results": [
                    {
                        "symbol": "000001.SZ",
                        "companyName": "Ping An Bank Co., Ltd."
                    },
                    {
                        "symbol": "000002.SZ",
                        "companyName": "China Vanke Co., Ltd."
                    }
                ]
            }
        }
