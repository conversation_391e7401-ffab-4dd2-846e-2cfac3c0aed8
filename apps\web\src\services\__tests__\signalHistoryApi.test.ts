import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  SignalHistoryApiError,
  apiCache,
  filtersToApiRequest
} from '../signalHistoryApi';
import type { SignalFilters } from '../../stores/signalHistoryStore';

// Mock fetch globally
const mockFetch = vi.fn();
(globalThis as any).fetch = mockFetch;

describe('signalHistoryApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    apiCache.clear();
  });

  describe('SignalHistoryApiError', () => {
    it('should create error with correct properties', () => {
      const error = new SignalHistoryApiError('Test error', 404, { detail: 'Not found' });

      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(404);
      expect(error.details).toEqual({ detail: 'Not found' });
      expect(error.name).toBe('SignalHistoryApiError');
      expect(error instanceof Error).toBe(true);
    });

    it('should create error without optional parameters', () => {
      const error = new SignalHistoryApiError('Simple error');

      expect(error.message).toBe('Simple error');
      expect(error.statusCode).toBeUndefined();
      expect(error.details).toBeUndefined();
    });
  });

  describe('apiCache', () => {
    it('should store and retrieve cached data', () => {
      const testData = { test: 'data' };
      const cacheKey = 'test-key';

      apiCache.set(cacheKey, testData, 5000);
      const retrieved = apiCache.get(cacheKey);

      expect(retrieved).toEqual(testData);
    });

    it('should return null for expired cache', () => {
      const testData = { test: 'data' };
      const cacheKey = 'test-key';

      // Set with very short TTL
      apiCache.set(cacheKey, testData, -1);
      const retrieved = apiCache.get(cacheKey);

      expect(retrieved).toBeNull();
    });

    it('should return null for non-existent cache', () => {
      const retrieved = apiCache.get('non-existent-key');
      expect(retrieved).toBeNull();
    });

    it('should clear all cache entries', () => {
      apiCache.set('key1', 'data1');
      apiCache.set('key2', 'data2');
      
      apiCache.clear();
      
      expect(apiCache.get('key1')).toBeNull();
      expect(apiCache.get('key2')).toBeNull();
    });

    it('should delete specific cache entry', () => {
      apiCache.set('key1', 'data1');
      apiCache.set('key2', 'data2');
      
      apiCache.delete('key1');
      
      expect(apiCache.get('key1')).toBeNull();
      expect(apiCache.get('key2')).toBe('data2');
    });
  });

  describe('filtersToApiRequest', () => {
    it('should convert filters to API request format', () => {
      const filters: SignalFilters = {
        dateRange: {
          start: '2024-01-01',
          end: '2024-12-31'
        },
        signalTypes: ['buy', 'sell'],
        strategies: ['momentum'],
        minConfidence: 0.8,
        minEffectiveness: 0.7
      };

      const result = filtersToApiRequest('AAPL', filters, { page: 1, pageSize: 50 });

      expect(result).toEqual({
        symbol: 'AAPL',
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        signal_types: 'buy,sell',
        min_confidence: 0.8,
        min_effectiveness: 0.7,
        strategies: 'momentum',
        page: 1,
        page_size: 50
      });
    });

    it('should handle empty filters', () => {
      const filters: SignalFilters = {
        dateRange: { start: '', end: '' },
        signalTypes: [],
        strategies: [],
        minConfidence: 0,
        minEffectiveness: 0
      };

      const result = filtersToApiRequest('AAPL', filters);

      expect(result).toEqual({
        symbol: 'AAPL',
        start_date: '',
        end_date: '',
        signal_types: '',
        min_confidence: 0,
        min_effectiveness: 0,
        strategies: undefined,
        page: undefined,
        page_size: undefined
      });
    });

    it('should handle partial filters', () => {
      const filters: SignalFilters = {
        dateRange: { start: '2024-01-01', end: '' },
        signalTypes: ['buy'],
        strategies: [],
        minConfidence: 0.5,
        minEffectiveness: 0
      };

      const result = filtersToApiRequest('AAPL', filters, { page: 2, pageSize: 25 });

      expect(result).toEqual({
        symbol: 'AAPL',
        start_date: '2024-01-01',
        end_date: '',
        signal_types: 'buy',
        min_confidence: 0.5,
        min_effectiveness: 0,
        strategies: undefined,
        page: 2,
        page_size: 25
      });
    });
  });
});