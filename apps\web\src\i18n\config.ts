import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enCommon from '../locales/en/common.json';
import enAnalysis from '../locales/en/analysis.json';
import enScreener from '../locales/en/screener.json';
import zhCommon from '../locales/zh-CN/common.json';
import zhAnalysis from '../locales/zh-CN/analysis.json';
import zhScreener from '../locales/zh-CN/screener.json';

const resources = {
  en: {
    common: enCommon,
    analysis: enAnalysis,
    screener: enScreener,
  },
  'zh-CN': {
    common: zhCommon,
    analysis: zhAnalysis,
    screener: zhScreener,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: import.meta.env.DEV,
    
    // Language detection options
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage'],
    },

    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Default namespace
    defaultNS: 'common',
    ns: ['common', 'analysis', 'screener'],
    
    // Enhanced fallback handling
    fallbackNS: 'common', // Fallback to common namespace if key not found in current namespace
    
    // Return key if translation is missing (instead of empty string)
    returnEmptyString: false,
    returnNull: false,
    
    // Fallback options for missing keys
    saveMissing: import.meta.env.DEV, // Log missing keys in development
    
    // Handle missing key interpolation
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      if (import.meta.env.DEV) {
        console.warn(`Missing translation key: ${ns}:${key} for language: ${lng}`);
      }
      // Return the key as fallback to make missing translations visible
      return fallbackValue || key;
    },
    
    // Postprocessing to handle missing translations gracefully
    postProcess: ['missingKeyHandler'],
  });

export default i18n;