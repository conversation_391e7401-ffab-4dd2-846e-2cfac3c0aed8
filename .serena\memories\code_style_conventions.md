# Code Style & Conventions

## Frontend (TypeScript/React)

### TypeScript Configuration
- **Target**: ES2020
- **JSX**: react-jsx (React 17+ transform)
- **Strict Mode**: Enabled with additional strict checks:
  - `noUnusedLocals: true`
  - `noUnusedParameters: true`
  - `noFallthroughCasesInSwitch: true`

### Import Patterns
- Use `@/` alias for absolute imports from src directory
- Specific aliases:
  - `@/components/*` → `./src/components/*`
  - `@/features/*` → `./src/features/*`
  - `@/lib/*` → `./src/lib/*`
  - `@/stores/*` → `./src/stores/*`
- Import shared types from `@trading-agent/shared-types`
- Prefer named imports over default imports for utilities

### File Organization
- **Feature-based organization** over technical grouping
- Components co-located with tests in `__tests__/` directories
- Path structure:
  - `src/components/` - Reusable UI components
  - `src/features/` - Domain-specific features (Analysis, Screener)
  - `src/lib/` - Shared utilities
  - `src/stores/` - Zustand state management

### ESLint Rules (from package.json)
- TypeScript ESLint with strict rules
- React hooks plugin for hooks compliance
- React refresh plugin for HMR compatibility
- Max warnings: 0 (must fix all warnings)

## Backend (Python)

### Python Version & Style
- **Python**: 3.11+ required
- **Line Length**: 88 characters (Ruff/Black standard)
- **Type Hints**: Required for all function definitions (`disallow_untyped_defs: true`)

### Ruff Configuration
- **Selected Rules**: E, W, F, I, N, UP, S, B, A, C4, T20
- **Ignored**: 
  - E501 (line too long - handled by line-length setting)
  - S101 (assert used - allowed in tests)

### MyPy Configuration
- **Strict Mode**: Multiple strict options enabled
- **Required**: 
  - Type hints for all functions
  - Complete function definitions
  - Typed decorators

### File Organization
- **Feature-based modules** in `src/features/`
- Tests in `tests/` directory mirroring source structure
- Feature modules should have routers, models, and business logic

## Shared Types

### Type Definitions
- All shared interfaces in `packages/shared-types/src/index.ts`
- Core types:
  - `DailyPrice` - OHLC+Volume data
  - `StockData` - Symbol + price history
  - `SignalResult` - Trading signal analysis result
  - `Signal` - Enum: `'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL'`
  - `ApiResponse<T>` - Standardized API response wrapper

## General Conventions

### Testing
- **Frontend**: Vitest with jsdom environment
- **Backend**: Pytest with async support
- **E2E**: Playwright with multi-browser support
- Tests should be co-located with source files

### Documentation
- Use TSDoc comments for TypeScript functions
- Python docstrings following standard conventions
- README files for significant components/features

### Git & Version Control
- Feature-based branch naming
- Conventional commit messages preferred
- Clean up build artifacts before commits