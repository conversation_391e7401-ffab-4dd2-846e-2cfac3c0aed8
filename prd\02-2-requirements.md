### 2. Requirements

#### Functional Requirements

1. **FR1**: The system shall ingest Chinese stock market data for analysis using the `akshare` library.
2. **FR2**: The system shall calculate the "Magic Nine Turns" technical indicator based on the ingested stock data.
3. **FR3**: The system shall calculate the MACD indicator and include an algorithm to detect top divergence patterns.
4. **FR4**: The system shall generate a unified trading signal by combining the results of the "Magic Nine Turns" and MACD divergence indicators.
5. **FR5**: A user interface shall be provided for users to input a single Chinese stock code.
6. **FR6**: The system shall display the unified trading signal and a visual chart with the relevant indicators overlaid for the requested stock.
7. **FR7**: The system shall perform an automated daily scan of a predefined list of stocks (e.g., CSI 300).
8. **FR8**: The system shall display a simple list of stocks that meet the criteria of the combined trading strategy from the automated scan.

#### Non-Functional Requirements

1. **NFR1**: The application shall be a web-based platform.
2. **NFR2**: The application's design shall be responsive, ensuring usability on both desktop and mobile browsers.
3. **NFR3**: The application shall use near real-time data for its analysis to ensure signals are timely.
4. **NFR4**: All charts presented to the user shall be interactive (e.g., allowing zoom, pan).
5. **NFR5**: All sensitive credentials, such as the Tushare API token, must be securely stored and managed on the backend.

***
