### Epic 2: Analysis Interface & Visualization

This epic brings the core backend logic to life by creating the primary user-facing feature. It focuses on building a clean and effective user interface where a trader can input a stock code and immediately see the unified signal and a detailed, interactive chart. The goal is to provide a seamless and intuitive experience for visualizing the results of our proprietary strategy.

#### Story 2.1: Basic UI Layout

**As a** User, **I want** a basic application layout with navigation between the "Analysis" and "Screener" views, **so that** I can easily switch between the main tools.
**Acceptance Criteria:**

1. A main application shell is created using the chosen frontend framework.
2. A simple header or navigation bar is present containing links for "Analysis" and "Screener".
3. Clicking the navigation links successfully switches between two distinct, initially empty, page components.
4. The overall layout is responsively structured for both desktop and mobile viewports.

#### Story 2.2: Stock Input and API Connection

**As a** User, **I want** to enter a stock code into an input field and trigger the analysis, **so that** I can get a trading signal for a specific stock.
**Acceptance Criteria:**

1. The "Analysis" view contains a text input field for a stock code and a submit button.
2. Clicking the submit button triggers a call to the `GET /api/signal/{stock_code}` endpoint created in Epic 1.
3. The frontend correctly receives and processes the JSON response from the API.
4. A visual loading indicator is displayed while the API call is in progress.
5. Clear error messages are displayed to the user if the API call fails or the stock code is invalid.

#### Story 2.3: Signal Display

**As a** User, **I want** to see the final trading signal displayed clearly and prominently, **so that** I can understand the analysis result at a glance.
**Acceptance Criteria:**

1. After a successful API response, the unified signal (e.g., "SELL\_CANDIDATE") is displayed in a prominent position on the "Analysis" view.
2. The display style (e.g., color) changes based on the nature of the signal to provide an immediate visual cue.
3. Any additional summary details from the API response are displayed in a clean, readable format.

#### Story 2.4: Interactive Chart Visualization

**As a** User, **I want** to see an interactive chart of the stock's price and indicators, **so that** I can visually verify and explore the data behind the signal.
**Acceptance Criteria:**

1. A third-party charting library is successfully integrated into the "Analysis" view.
2. The chart renders the stock's historical price data received from the API.
3. The "Magic Nine Turns" sequence and the MACD indicator with divergence markers are correctly overlaid on the price chart.
4. The chart is interactive, supporting, at a minimum, zooming and panning.
5. The chart is responsive and renders correctly on both desktop and mobile screens.

***
