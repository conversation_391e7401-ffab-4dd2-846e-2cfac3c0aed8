import { LoadingSpinner } from './LoadingSpinner'
import { cn } from '@/lib/utils'

interface LoadingStateProps {
  message?: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingState({ 
  message = 'Loading...', 
  size = 'md', 
  className 
}: LoadingStateProps) {
  return (
    <div 
      className={cn('flex items-center space-x-2', className)}
      role="status"
      aria-live="polite"
      aria-label={message}
    >
      <LoadingSpinner size={size} showRole={false} />
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  )
}