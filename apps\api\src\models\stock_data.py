"""Pydantic models for stock data."""

from datetime import datetime

from pydantic import BaseModel, Field, field_validator


class DailyPrice(BaseModel):
    """Daily price data for a stock."""

    date: str = Field(..., description="Date in YYYY-MM-DD format")
    open: float = Field(..., description="Opening price", gt=0)
    high: float = Field(..., description="Highest price", gt=0)
    low: float = Field(..., description="Lowest price", gt=0)
    close: float = Field(..., description="Closing price", gt=0)
    volume: int = Field(..., description="Trading volume", ge=0)

    @field_validator('date')
    def validate_date_format(cls, v):
        """Validate date is in YYYY-MM-DD format."""
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('Date must be in YYYY-MM-DD format')


class StockData(BaseModel):
    """Stock data containing symbol and daily prices."""

    symbol: str = Field(..., description="Stock symbol/code", min_length=1)
    daily_prices: list[DailyPrice] = Field(..., description="List of daily price data")

    @field_validator('daily_prices')
    def validate_daily_prices_not_empty(cls, v):
        """Validate that daily_prices is not empty."""
        if not v:
            raise ValueError('daily_prices cannot be empty')
        return v
