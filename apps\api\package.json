{"name": "@trading-agent/api", "version": "1.0.0", "description": "Python FastAPI backend for trading agent application", "scripts": {"dev": "python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000", "start": "python -m uvicorn src.main:app --host 0.0.0.0 --port 8000", "test": "python -m pytest tests/ -v", "lint": "python -m ruff check src/ tests/", "type-check": "python -m mypy src/", "clean": "find . -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true"}, "dependencies": {}, "devDependencies": {}}