"""Tests for Strategy Engine."""

from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytest

from src.features.strategy.engine import StrategyEngine
from src.models.stock_data import DailyPrice, StockData
from src.models.strategy import DivergencePoint, MACDResult, MagicNineTurnsResult


class TestStrategyEngine:
    """Test cases for Strategy Engine."""

    def setup_method(self):
        """Set up test fixtures."""
        self.engine = StrategyEngine()

    @pytest.fixture
    def valid_stock_data(self):
        """Valid stock data for testing."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create 40 days of price data (exceeds minimum requirement)
        for i in range(40):
            date = base_date + timedelta(days=i)
            close = 100 + (i * 0.5)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=close + 1.0,
                low=close - 1.0,
                close=close,
                volume=1000000 + (i * 1000)
            ))

        return StockData(
            symbol="000001.SZ",
            daily_prices=prices
        )

    @pytest.fixture
    def insufficient_data_stock(self):
        """Stock data with insufficient data points."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create only 20 days of data (below minimum)
        for i in range(20):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=100.0,
                high=101.0,
                low=99.0,
                close=100.0 + i,
                volume=1000000
            ))

        return StockData(
            symbol="TEST.SZ",
            daily_prices=prices
        )

    @pytest.fixture
    def empty_stock_data(self):
        """Empty stock data for error testing."""
        # Note: StockData model validates that daily_prices cannot be empty
        # So we create minimal data but test engine validation
        return StockData(
            symbol="EMPTY.SZ",
            daily_prices=[DailyPrice(
                date="2025-01-01",
                open=100.0,
                high=101.0,
                low=99.0,
                close=100.0,
                volume=1000000
            )]
        )

    @pytest.fixture
    def unsorted_stock_data(self):
        """Stock data with unsorted dates but sufficient data points."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create 35 days of data but make last few out of order
        for i in range(35):
            date = base_date + timedelta(days=i)
            close = 100.0 + i
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=close + 0.5,
                low=close - 1.0,
                close=close,
                volume=1000000
            ))

        # Swap last two entries to make unsorted
        prices[-1], prices[-2] = prices[-2], prices[-1]

        return StockData(
            symbol="UNSORTED.SZ",
            daily_prices=prices
        )

    def test_calculate_signal_success(self, valid_stock_data):
        """Test successful signal calculation."""
        with patch('src.features.strategy.calculators.magic_nine_turns.MagicNineTurnsCalculator') as mock_magic, \
             patch('src.features.strategy.calculators.macd.MACDCalculator') as mock_macd:

            # Mock calculator results
            mock_magic_result = MagicNineTurnsResult(
                sequence=[None] * 40,
                current_count=5,
                is_complete=False,
                direction='UP'
            )

            mock_macd_result = MACDResult(
                macd_line=[None] * 40,
                signal_line=[None] * 40,
                histogram=[None] * 40,
                divergence_points=[]
            )

            mock_magic_instance = Mock()
            mock_magic_instance.calculate.return_value = mock_magic_result
            mock_magic.return_value = mock_magic_instance

            mock_macd_instance = Mock()
            mock_macd_instance.calculate.return_value = mock_macd_result
            mock_macd.return_value = mock_macd_instance

            # Execute test
            result = self.engine.calculate_signal(valid_stock_data)

            # Verify result structure
            assert result.symbol == "000001.SZ"
            assert result.signal in ['SELL_CANDIDATE', 'HOLD', 'NO_SIGNAL']
            assert result.last_scan_date is not None
            assert result.chart_data is not None

            # Verify chart data structure
            assert len(result.chart_data.daily_prices) == len(valid_stock_data.daily_prices)
            assert len(result.chart_data.magic_nine_sequence) == len(valid_stock_data.daily_prices)
            assert len(result.chart_data.macd_line) == len(valid_stock_data.daily_prices)
            assert len(result.chart_data.signal_line) == len(valid_stock_data.daily_prices)

            # Verify calculators were called
            mock_magic_instance.calculate.assert_called_once()
            mock_macd_instance.calculate.assert_called_once()

    def test_insufficient_data_raises_error(self, insufficient_data_stock):
        """Test that insufficient data raises ValueError."""
        with pytest.raises(ValueError, match="Insufficient data points"):
            self.engine.calculate_signal(insufficient_data_stock)

    def test_empty_data_raises_error(self, empty_stock_data):
        """Test that insufficient data raises ValueError."""
        with pytest.raises(ValueError, match="Insufficient data points"):
            self.engine.calculate_signal(empty_stock_data)

    def test_unsorted_data_raises_error(self, unsorted_stock_data):
        """Test that unsorted data raises ValueError."""
        with pytest.raises(ValueError, match="Daily prices must be sorted by date"):
            self.engine.calculate_signal(unsorted_stock_data)

    def test_determine_unified_signal_sell_candidate(self):
        """Test unified signal determination for SELL_CANDIDATE."""
        # Both conditions met
        magic_nine_result = MagicNineTurnsResult(
            sequence=[],
            current_count=9,
            is_complete=True,
            direction='UP'
        )

        macd_result = MACDResult(
            macd_line=[],
            signal_line=[],
            histogram=[],
            divergence_points=[DivergencePoint(date="2025-01-01", type='TOP')]
        )

        signal = self.engine._determine_unified_signal(magic_nine_result, macd_result)
        assert signal == 'SELL_CANDIDATE'

    def test_determine_unified_signal_hold_magic_nine_only(self):
        """Test unified signal determination for HOLD (Magic Nine only)."""
        # Only Magic Nine condition met
        magic_nine_result = MagicNineTurnsResult(
            sequence=[],
            current_count=9,
            is_complete=True,
            direction='UP'
        )

        macd_result = MACDResult(
            macd_line=[],
            signal_line=[],
            histogram=[],
            divergence_points=[]  # No divergence
        )

        signal = self.engine._determine_unified_signal(magic_nine_result, macd_result)
        assert signal == 'HOLD'

    def test_determine_unified_signal_hold_macd_only(self):
        """Test unified signal determination for HOLD (MACD only)."""
        # Only MACD condition met
        magic_nine_result = MagicNineTurnsResult(
            sequence=[],
            current_count=5,
            is_complete=False,  # Not complete
            direction='UP'
        )

        macd_result = MACDResult(
            macd_line=[],
            signal_line=[],
            histogram=[],
            divergence_points=[DivergencePoint(date="2025-01-01", type='TOP')]
        )

        signal = self.engine._determine_unified_signal(magic_nine_result, macd_result)
        assert signal == 'HOLD'

    def test_determine_unified_signal_no_signal(self):
        """Test unified signal determination for NO_SIGNAL."""
        # Neither condition met
        magic_nine_result = MagicNineTurnsResult(
            sequence=[],
            current_count=5,
            is_complete=False,
            direction='UP'
        )

        macd_result = MACDResult(
            macd_line=[],
            signal_line=[],
            histogram=[],
            divergence_points=[]
        )

        signal = self.engine._determine_unified_signal(magic_nine_result, macd_result)
        assert signal == 'NO_SIGNAL'

    def test_determine_unified_signal_magic_nine_down_direction(self):
        """Test that Magic Nine DOWN direction doesn't trigger sell signal."""
        # Magic Nine complete but DOWN direction
        magic_nine_result = MagicNineTurnsResult(
            sequence=[],
            current_count=9,
            is_complete=True,
            direction='DOWN'  # Down direction
        )

        macd_result = MACDResult(
            macd_line=[],
            signal_line=[],
            histogram=[],
            divergence_points=[DivergencePoint(date="2025-01-01", type='TOP')]
        )

        signal = self.engine._determine_unified_signal(magic_nine_result, macd_result)
        assert signal == 'HOLD'  # Only MACD condition met

    def test_validate_input_data_success(self, valid_stock_data):
        """Test successful input data validation."""
        # Should not raise any exception
        self.engine._validate_input_data(valid_stock_data)

    def test_calculation_error_propagation(self, valid_stock_data):
        """Test that calculation errors are properly propagated."""
        with patch('src.features.strategy.calculators.magic_nine_turns.MagicNineTurnsCalculator') as mock_magic:
            mock_magic_instance = Mock()
            mock_magic_instance.calculate.side_effect = Exception("Calculation failed")
            mock_magic.return_value = mock_magic_instance

            with pytest.raises(Exception, match="Calculation failed"):
                self.engine.calculate_signal(valid_stock_data)

    def test_min_data_points_configuration(self):
        """Test that minimum data points can be configured."""
        custom_engine = StrategyEngine()
        custom_engine.min_data_points = 50

        base_date = datetime(2025, 1, 1)
        prices = []

        # Create 40 days (less than new minimum)
        for i in range(40):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=100.0,
                high=101.0,
                low=99.0,
                close=100.0 + i,
                volume=1000000
            ))

        stock_data = StockData(symbol="TEST.SZ", daily_prices=prices)

        with pytest.raises(ValueError, match="Need at least 50"):
            custom_engine.calculate_signal(stock_data)

    def test_iso_date_format_in_result(self, valid_stock_data):
        """Test that last_scan_date is in ISO format."""
        with patch('src.features.strategy.calculators.magic_nine_turns.MagicNineTurnsCalculator') as mock_magic, \
             patch('src.features.strategy.calculators.macd.MACDCalculator') as mock_macd:

            # Mock calculator results
            mock_magic_result = MagicNineTurnsResult(
                sequence=[None] * 40,
                current_count=0,
                is_complete=False,
                direction='NONE'
            )

            mock_macd_result = MACDResult(
                macd_line=[None] * 40,
                signal_line=[None] * 40,
                histogram=[None] * 40,
                divergence_points=[]
            )

            mock_magic_instance = Mock()
            mock_magic_instance.calculate.return_value = mock_magic_result
            mock_magic.return_value = mock_magic_instance

            mock_macd_instance = Mock()
            mock_macd_instance.calculate.return_value = mock_macd_result
            mock_macd.return_value = mock_macd_instance

            result = self.engine.calculate_signal(valid_stock_data)

            # Verify ISO format (should contain 'T' for datetime separator)
            assert 'T' in result.last_scan_date

            # Should be parseable as ISO datetime
            datetime.fromisoformat(result.last_scan_date.replace('Z', '+00:00') if result.last_scan_date.endswith('Z') else result.last_scan_date)
