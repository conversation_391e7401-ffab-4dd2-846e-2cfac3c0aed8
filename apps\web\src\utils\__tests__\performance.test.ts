import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  DataProcessor,
  PerformanceMonitor,
  ArrayUtils
} from '../performance';

describe('performance utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('DataProcessor', () => {
    describe('chunk', () => {
      it('should split array into chunks of specified size', () => {
        const data = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        const result = DataProcessor.chunk(data, 3);
        
        expect(result).toEqual([[1, 2, 3], [4, 5, 6], [7, 8, 9]]);
      });

      it('should handle arrays that do not divide evenly', () => {
        const data = [1, 2, 3, 4, 5];
        const result = DataProcessor.chunk(data, 2);
        
        expect(result).toEqual([[1, 2], [3, 4], [5]]);
      });

      it('should handle empty arrays', () => {
        const result = DataProcessor.chunk([], 3);
        expect(result).toEqual([]);
      });

      it('should handle chunk size larger than array', () => {
        const data = [1, 2];
        const result = DataProcessor.chunk(data, 5);
        
        expect(result).toEqual([[1, 2]]);
      });
    });

    describe('processInBatches', () => {
      it('should process data in batches', async () => {
        const data = [1, 2, 3, 4, 5, 6];
        const processor = vi.fn().mockImplementation((batch: number[]) => 
          batch.map(x => x * 2)
        );
        
        const result = await DataProcessor.processInBatches(data, processor, 2);
        
        expect(result).toEqual([2, 4, 6, 8, 10, 12]);
        expect(processor).toHaveBeenCalledTimes(3);
        expect(processor).toHaveBeenNthCalledWith(1, [1, 2]);
        expect(processor).toHaveBeenNthCalledWith(2, [3, 4]);
        expect(processor).toHaveBeenNthCalledWith(3, [5, 6]);
      });

      it('should handle async processors', async () => {
        const data = [1, 2, 3];
        const processor = vi.fn().mockImplementation(async (batch: number[]) => {
          await new Promise(resolve => setTimeout(resolve, 10));
          return batch.map(x => x * 3);
        });
        
        const result = await DataProcessor.processInBatches(data, processor, 2);
        
        expect(result).toEqual([3, 6, 9]);
        expect(processor).toHaveBeenCalledTimes(2);
      });
    });

    describe('memoize', () => {
      it('should cache function results', () => {
        const fn = vi.fn((x: number) => x * 2);
        const memoized = DataProcessor.memoize(fn);
        
        const result1 = memoized(5);
        const result2 = memoized(5);
        const result3 = memoized(10);
        
        expect(result1).toBe(10);
        expect(result2).toBe(10);
        expect(result3).toBe(20);
        expect(fn).toHaveBeenCalledTimes(2); // Only called for unique inputs
      });

      it('should handle complex arguments', () => {
        const fn = vi.fn((obj: { a: number; b: string }) => `${obj.a}-${obj.b}`);
        const memoized = DataProcessor.memoize(fn);
        
        const result1 = memoized({ a: 1, b: 'test' });
        const result2 = memoized({ a: 1, b: 'test' });
        const result3 = memoized({ a: 2, b: 'test' });
        
        expect(result1).toBe('1-test');
        expect(result2).toBe('1-test');
        expect(result3).toBe('2-test');
        expect(fn).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('PerformanceMonitor', () => {
    beforeEach(() => {
      vi.spyOn(performance, 'now').mockReturnValue(1000);
      vi.spyOn(console, 'log').mockImplementation(() => {});
    });

    describe('track', () => {
      it('should measure function execution time', () => {
        const fn = vi.fn(() => 'result');
        vi.spyOn(performance, 'now')
          .mockReturnValueOnce(1000)
          .mockReturnValueOnce(1050);
        
        const result = PerformanceMonitor.track('test-operation', fn);
        
        expect(result).toBe('result');
        expect(fn).toHaveBeenCalledTimes(1);
        expect(console.log).toHaveBeenCalledWith(
          'Performance [test-operation]: 50.00ms'
        );
      });

      it('should handle function errors', () => {
        const error = new Error('Test error');
        const fn = vi.fn(() => { throw error; });
        
        expect(() => PerformanceMonitor.track('test-error', fn)).toThrow(error);
        expect(fn).toHaveBeenCalledTimes(1);
      });
    });

    describe('trackAsync', () => {
      it('should measure async function execution time', async () => {
        const fn = vi.fn(async () => {
          await new Promise(resolve => setTimeout(resolve, 10));
          return 'async-result';
        });
        
        vi.spyOn(performance, 'now')
          .mockReturnValueOnce(1000)
          .mockReturnValueOnce(1075);
        
        const result = await PerformanceMonitor.trackAsync('async-test', fn);
        
        expect(result).toBe('async-result');
        expect(fn).toHaveBeenCalledTimes(1);
        expect(console.log).toHaveBeenCalledWith(
          'Performance [async-test]: 75.00ms'
        );
      });
    });

    describe('timer methods', () => {
      it('should start and end timers correctly', () => {
        vi.spyOn(performance, 'now')
          .mockReturnValueOnce(1000)
          .mockReturnValueOnce(1100);
        
        PerformanceMonitor.startTimer('timer-test');
        PerformanceMonitor.endTimer('timer-test');
        
        expect(console.log).toHaveBeenCalledWith(
          'Performance [timer-test]: 100.00ms'
        );
      });

      it('should handle ending non-existent timer', () => {
        PerformanceMonitor.endTimer('non-existent');
        
        expect(console.log).toHaveBeenCalledWith(
          'Performance [non-existent]: Timer not found'
        );
      });
    });
  });

  describe('ArrayUtils', () => {
    describe('binarySearch', () => {
      it('should find element in sorted array', () => {
        const arr = [1, 3, 5, 7, 9, 11];
        
        expect(ArrayUtils.binarySearch(arr, 5)).toBe(2);
        expect(ArrayUtils.binarySearch(arr, 1)).toBe(0);
        expect(ArrayUtils.binarySearch(arr, 11)).toBe(5);
      });

      it('should return -1 for non-existent element', () => {
        const arr = [1, 3, 5, 7, 9];
        
        expect(ArrayUtils.binarySearch(arr, 4)).toBe(-1);
        expect(ArrayUtils.binarySearch(arr, 0)).toBe(-1);
        expect(ArrayUtils.binarySearch(arr, 10)).toBe(-1);
      });

      it('should handle empty array', () => {
        expect(ArrayUtils.binarySearch([], 5)).toBe(-1);
      });
    });

    describe('partition', () => {
      it('should partition array based on predicate', () => {
        const arr = [1, 2, 3, 4, 5, 6];
        const [evens, odds] = ArrayUtils.partition(arr, x => x % 2 === 0);
        
        expect(evens).toEqual([2, 4, 6]);
        expect(odds).toEqual([1, 3, 5]);
      });

      it('should handle empty array', () => {
        const [truthy, falsy] = ArrayUtils.partition([], () => true);
        
        expect(truthy).toEqual([]);
        expect(falsy).toEqual([]);
      });
    });

    describe('groupBy', () => {
      it('should group array elements by key function', () => {
        const arr = ['apple', 'banana', 'apricot', 'blueberry'];
        const grouped = ArrayUtils.groupBy(arr, str => str[0]);
        
        expect(grouped).toEqual({
          a: ['apple', 'apricot'],
          b: ['banana', 'blueberry']
        });
      });

      it('should handle empty array', () => {
        const grouped = ArrayUtils.groupBy([], x => x);
        expect(grouped).toEqual({});
      });
    });

    describe('unique', () => {
      it('should return unique elements', () => {
        const arr = [1, 2, 2, 3, 3, 3, 4];
        const unique = ArrayUtils.unique(arr);
        
        expect(unique).toEqual([1, 2, 3, 4]);
      });

      it('should handle empty array', () => {
        const unique = ArrayUtils.unique([]);
        expect(unique).toEqual([]);
      });
    });

    describe('intersection', () => {
      it('should find intersection of two arrays', () => {
        const arr1 = [1, 2, 3, 4];
        const arr2 = [3, 4, 5, 6];
        const intersection = ArrayUtils.intersection(arr1, arr2);
        
        expect(intersection).toEqual([3, 4]);
      });

      it('should handle no intersection', () => {
        const arr1 = [1, 2];
        const arr2 = [3, 4];
        const intersection = ArrayUtils.intersection(arr1, arr2);
        
        expect(intersection).toEqual([]);
      });
    });

    describe('difference', () => {
      it('should find difference between two arrays', () => {
        const arr1 = [1, 2, 3, 4];
        const arr2 = [3, 4, 5, 6];
        const difference = ArrayUtils.difference(arr1, arr2);
        
        expect(difference).toEqual([1, 2]);
      });

      it('should handle no difference', () => {
        const arr1 = [1, 2];
        const arr2 = [1, 2, 3];
        const difference = ArrayUtils.difference(arr1, arr2);
        
        expect(difference).toEqual([]);
      });
    });
  });
});