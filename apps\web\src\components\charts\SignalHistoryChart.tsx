import React, { useMemo, useCallback, useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Dot,
  ReferenceDot,
} from 'recharts';
import { SignalHistoryRecord } from '@trading-agent/shared-types';
import { withPerformanceTracking } from '@/lib/performance';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ChevronUp, ChevronDown, TrendingUp, TrendingDown } from 'lucide-react';

interface SignalHistoryChartProps {
  priceData: Array<{
    timestamp: string;
    price: number;
    volume?: number;
  }>;
  signalHistory: SignalHistoryRecord[];
  height?: number;
  showVolume?: boolean;
  className?: string;
  showSignals?: boolean;
  onSignalClick?: (signal: SignalHistoryRecord) => void;
}

interface ChartDataPoint {
  date: string;
  price: number;
  signal?: SignalHistoryRecord;
  signalType?: SignalType;
  isSuccessful?: string;
}

const SignalHistoryChart: React.FC<SignalHistoryChartProps> = ({
  priceData,
  signalHistory,
  height = 400,
  showVolume = false,
  className = '',
  showSignals = true,
  onSignalClick,
}) => {
  const [selectedSignal, setSelectedSignal] = useState<SignalHistoryRecord | null>(null);
  const chartData = useMemo(() => {
    const endMeasurement = PerformanceMonitor.startMeasurement('chart-data-processing');
    
    try {
      // Create a map of dates to signals for quick lookup
      const signalMap = new Map<string, SignalHistoryRecord>();
      signalHistory.forEach(signal => {
        const date = new Date(signal.triggerDate).toISOString().split('T')[0];
        signalMap.set(date, signal);
      });

      // Combine price data with signals using optimized processing
      const result = priceData.map(price => {
        const dateKey = new Date(price.date).toISOString().split('T')[0];
        const signal = signalMap.get(dateKey);
        
        return {
          date: price.date,
          price: price.close,
          signal,
          signalType: signal?.signalType,
          isSuccessful: signal?.isSuccessful,
        };
      });
      
      return result;
    } finally {
      endMeasurement();
    }
  }, [priceData, signalHistory]);

  // Memoized function to get signal color based on effectiveness
  const getSignalColor = useCallback((signal: SignalHistoryRecord): string => {
    if (!signal.isSuccessful) {
      return signal.isSuccessful === false ? '#ef4444' : '#6b7280'; // Red for failed, gray for pending
    }
    return '#10b981'; // Green for successful
  }, []);

  // Arrow marker component for signals
  const SignalArrow = useCallback(({ cx, cy, signal }: { cx: number; cy: number; signal: SignalHistoryRecord }) => {
    const isBuy = signal.signalType === 'BUY';
    const color = getSignalColor(signal);
    const ArrowIcon = isBuy ? TrendingUp : TrendingDown;
    const size = selectedSignal?.id === signal.id ? 24 : 18;
    
    return (
      <g
        onClick={() => {
          setSelectedSignal(signal);
          onSignalClick?.(signal);
        }}
        style={{ cursor: 'pointer' }}
        className="signal-arrow"
      >
        {/* Background circle */}
        <circle
          cx={cx}
          cy={cy}
          r={size / 2 + 2}
          fill="white"
          stroke={color}
          strokeWidth={2}
          opacity={0.9}
        />
        {/* Arrow icon */}
        <foreignObject
          x={cx - size / 2}
          y={cy - size / 2}
          width={size}
          height={size}
        >
          <ArrowIcon
            size={size - 4}
            color={color}
            className={`transform ${isBuy ? 'rotate-0' : 'rotate-180'}`}
          />
        </foreignObject>
        {/* Pulse animation for selected signal */}
        {selectedSignal?.id === signal.id && (
          <circle
            cx={cx}
            cy={cy}
            r={size / 2 + 6}
            fill="none"
            stroke={color}
            strokeWidth={1}
            opacity={0.6}
            className="animate-ping"
          />
        )}
      </g>
    );
  }, [getSignalColor, selectedSignal, onSignalClick]);

  // Custom dot component for signals with enhanced arrow markers
  const CustomDot = useCallback((props: any) => {
    const { cx, cy, payload } = props;
    if (!payload?.signal || !showSignals) return null;
    
    const signal = payload.signal as SignalHistoryRecord;
    
    return (
      <SignalArrow cx={cx} cy={cy} signal={signal} />
    );
  }, [SignalArrow, showSignals]);

  // Enhanced CustomTooltip component with detailed signal information
  const CustomTooltip = useCallback(({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    const data = payload[0].payload;
    const hasSignal = data.signal;

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-xl max-w-sm">
        <div className="flex items-center justify-between mb-2">
          <p className="text-sm font-semibold text-gray-900">
            {formatDate(label)}
          </p>
          <p className="text-lg font-bold text-gray-900">
            {formatCurrency(data.price)}
          </p>
        </div>
        
        {hasSignal && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center gap-2 mb-2">
              {data.signal.signalType === 'BUY' ? (
                <TrendingUp size={16} className="text-green-600" />
              ) : (
                <TrendingDown size={16} className="text-red-600" />
              )}
              <p className="text-sm font-semibold text-gray-900">
                {data.signal.signalType} Signal
              </p>
            </div>
            
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600">Trigger Price:</span>
                <span className="font-medium">{formatCurrency(data.signal.triggerPrice)}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${
                  data.signal.isSuccessful === true ? 'text-green-600' :
                  data.signal.isSuccessful === false ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {data.signal.isSuccessful === true ? 'Success' :
                   data.signal.isSuccessful === false ? 'Loss' : 'Pending'}
                </span>
              </div>
              
              {data.signal.confidence && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Confidence:</span>
                  <span className="font-medium">{(data.signal.confidence * 100).toFixed(1)}%</span>
                </div>
              )}
              
              {data.signal.strategyName && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Strategy:</span>
                  <span className="font-medium">{data.signal.strategyName}</span>
                </div>
              )}
              
              {data.signal.actualReturn && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Return:</span>
                  <span className={`font-medium ${
                    data.signal.actualReturn > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {(data.signal.actualReturn * 100).toFixed(2)}%
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }, []);

  // Memoize formatting functions
  const formatXAxisTick = useCallback(
    DataProcessor.memoize((tickItem: string) => {
      return new Date(tickItem).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    }),
    []
  );

  const formatYAxisTick = useCallback(
    DataProcessor.memoize((value: number) => {
      return `$${value.toFixed(0)}`;
    }),
    []
  );

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {symbol} - Signal History
        </h3>
        <div className="flex items-center gap-4 mt-2 text-sm">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-full bg-green-500" />
            <span>Buy Signals</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-full bg-red-500" />
            <span>Sell Signals</span>
          </div>
          {showEffectivenessColors && (
            <>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-green-700" />
                <span>Successful</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-yellow-500" />
                <span>Loss</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-gray-400" />
                <span>Pending</span>
              </div>
            </>
          )}
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
          <XAxis
            dataKey="date"
            tickFormatter={formatXAxisTick}
            stroke="#64748b"
            fontSize={12}
          />
          <YAxis
            tickFormatter={formatYAxisTick}
            stroke="#64748b"
            fontSize={12}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey="price"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={<CustomDot />}
            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
});

// Apply performance tracking wrapper
export default withPerformanceTracking(SignalHistoryChart, 'SignalHistoryChart');