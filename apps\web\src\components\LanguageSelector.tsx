import { useTranslation } from 'react-i18next'
import * as DropdownMenu from '@radix-ui/react-dropdown-menu'
import { ChevronDown, Globe, Check } from 'lucide-react'
import { cn } from '@/lib/utils'

const languages = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'zh-CN', name: 'Chinese', nativeName: '中文' },
]

export function LanguageSelector() {
  const { i18n, t } = useTranslation('common')

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode)
  }

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0]

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger
        className={cn(
          'inline-flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium',
          'bg-white border border-gray-200 rounded-md shadow-sm',
          'hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          'data-[state=open]:bg-gray-50'
        )}
        aria-label={t('language.selector')}
      >
        <Globe className="w-4 h-4" />
        <span>{currentLanguage.nativeName}</span>
        <ChevronDown className="w-4 h-4" />
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className={cn(
            'min-w-[160px] overflow-hidden bg-white rounded-md shadow-lg border border-gray-200 p-1',
            'data-[state=open]:animate-in data-[state=closed]:animate-out',
            'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
            'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
            'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2',
            'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2'
          )}
          sideOffset={5}
        >
          {languages.map((language) => (
            <DropdownMenu.Item
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={cn(
                'relative flex items-center gap-2 px-3 py-2 text-sm rounded-sm cursor-pointer',
                'hover:bg-gray-100 focus:bg-gray-100 focus:outline-none',
                language.code === i18n.language && 'bg-blue-50 text-blue-900'
              )}
            >
              <span className="font-medium">{language.nativeName}</span>
              <span className="ml-2 text-gray-500">({language.name})</span>
              {language.code === i18n.language && (
                <Check className="w-4 h-4 ml-auto" />
              )}
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  )
}

export default LanguageSelector