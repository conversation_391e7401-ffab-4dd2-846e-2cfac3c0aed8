import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  signalHistory<PERSON>pi, 
  getCachedSignalHistory, 
  getCachedSignalEffectiveness,
  filtersToApiRequest,
  SignalHistoryApiError 
} from '../services/signalHistoryApi';

export interface SignalData {
  id: string;
  symbol: string;
  signal_type: 'buy' | 'sell';
  timestamp: string;
  price: number;
  confidence: number;
  strategy_name: string;
  effectiveness_score?: number;
  profit_loss?: number;
  exit_price?: number;
  exit_timestamp?: string;
  notes?: string;
}

export interface SignalEffectiveness {
  symbol: string;
  total_signals: number;
  successful_signals: number;
  success_rate: number;
  average_profit: number;
  total_profit: number;
  best_strategy: string;
  worst_strategy: string;
  time_period: {
    start: string;
    end: string;
  };
}

export interface SignalFilters {
  dateRange: {
    start: string;
    end: string;
  };
  signalTypes: ('buy' | 'sell')[];
  minConfidence: number;
  minEffectiveness: number;
  strategies: string[];
}

export interface SignalHistoryState {
  signals: SignalData[];
  effectiveness: SignalEffectiveness | null;
  isLoading: boolean;
  error: string | null;
  filters: SignalFilters;
  selectedSignal: SignalData | null;
  showSignals: boolean;
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    hasMore: boolean;
  };
  availableStrategies: string[];
  performanceMetrics: any;
  strategyComparison: any[];
  fetchSignals: (symbol: string, filters?: Partial<SignalFilters>, page?: number) => Promise<void>;
  fetchEffectiveness: (symbol: string, filters?: Partial<SignalFilters>) => Promise<void>;
  setFilters: (newFilters: Partial<SignalFilters>) => void;
  resetFilters: () => void;
  selectSignal: (signal: SignalData | null) => void;
  toggleSignalVisibility: () => void;
  clearError: () => void;
  fetchStrategies: () => Promise<void>;
  fetchPerformanceMetrics: (symbol: string, timeframe?: string) => Promise<void>;
  compareStrategies: (symbol: string, strategies: string[], timeframe?: string) => Promise<void>;
  loadMore: () => Promise<void>;
  reset: () => void;
}

export interface SignalHistoryStore {
  // State
  signals: SignalData[];
  effectiveness: SignalEffectiveness | null;
  filters: SignalFilters;
  selectedSignal: SignalData | null;
  isLoading: boolean;
  error: string | null;
  showSignals: boolean;
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    hasMore: boolean;
  };
  availableStrategies: string[];
  performanceMetrics: {
    total_signals: number;
    win_rate: number;
    average_return: number;
    max_drawdown: number;
    sharpe_ratio: number;
    profit_factor: number;
  } | null;
  strategyComparison: {
    strategy_name: string;
    total_signals: number;
    win_rate: number;
    average_return: number;
    sharpe_ratio: number;
  }[];
  
  // Actions
  fetchSignals: (symbol: string, filters?: Partial<SignalFilters>, page?: number) => Promise<void>;
  fetchEffectiveness: (symbol: string, filters?: Partial<SignalFilters>) => Promise<void>;
  fetchStrategies: () => Promise<void>;
  fetchPerformanceMetrics: (symbol: string, timeframe?: string) => Promise<void>;
  compareStrategies: (symbol: string, strategies: string[], timeframe?: string) => Promise<void>;
  setFilters: (filters: Partial<SignalFilters>) => void;
  resetFilters: () => void;
  selectSignal: (signal: SignalData | null) => void;
  toggleSignalVisibility: () => void;
  clearError: () => void;
  reset: () => void;
  loadMore: () => Promise<void>;
}

const defaultFilters: SignalFilters = {
  dateRange: {
    start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year ago
    end: new Date().toISOString().split('T')[0], // today
  },
  signalTypes: ['buy', 'sell'],
  minConfidence: 0,
  minEffectiveness: 0,
  strategies: [],
};

const initialState = {
  signals: [],
  effectiveness: null,
  isLoading: false,
  error: null,
  filters: defaultFilters,
  selectedSignal: null,
  showSignals: true,
  pagination: {
    page: 1,
    pageSize: 50,
    totalCount: 0,
    hasMore: false,
  },
  availableStrategies: [],
  performanceMetrics: null,
  strategyComparison: [],
};

export const useSignalHistoryStore = create<SignalHistoryState>()(devtools(
  (set, get) => ({
    ...initialState,
    
    fetchSignals: async (symbol: string, filters?: Partial<SignalFilters>, page = 1) => {
      set({ isLoading: true, error: null });
      
      try {
        const currentFilters = { ...get().filters, ...filters };
        const mergedFilters = { ...currentFilters, ...filters };
        
        const apiRequest = filtersToApiRequest(symbol, mergedFilters, {
          page,
          pageSize: get().pagination.pageSize,
        });
        
        const response = await getCachedSignalHistory(apiRequest);
        
        const newSignals = page === 1 ? response.signals : [...get().signals, ...response.signals];
        
        set({ 
          signals: newSignals,
          pagination: {
            page: response.page,
            pageSize: response.page_size,
            totalCount: response.total_count,
            hasMore: response.signals.length === response.page_size,
          },
          isLoading: false 
        });
      } catch (error) {
        const errorMessage = error instanceof SignalHistoryApiError 
          ? error.message 
          : error instanceof Error 
          ? error.message 
          : 'Failed to fetch signals';
        
        set({ 
          error: errorMessage,
          isLoading: false,
          signals: []
        });
      }
    },
    
    fetchEffectiveness: async (symbol: string, filters?: Partial<SignalFilters>) => {
      set({ isLoading: true, error: null });
      try {
        const currentFilters = get().filters;
        const mergedFilters = { ...currentFilters, ...filters };
        
        const effectiveness = await getCachedSignalEffectiveness(symbol, mergedFilters);
        
        set({ effectiveness, isLoading: false });
      } catch (error) {
        const errorMessage = error instanceof SignalHistoryApiError 
          ? error.message 
          : error instanceof Error 
          ? error.message 
          : 'Failed to fetch effectiveness data';
        
        set({ 
          error: errorMessage,
          isLoading: false 
        });
      }
    },
    
    setFilters: (newFilters: Partial<SignalFilters>) => {
      set((state: SignalHistoryState) => ({
        filters: { ...state.filters, ...newFilters }
      }));
    },
    
    resetFilters: () => {
      set({ filters: defaultFilters });
    },
    
    selectSignal: (signal: SignalData | null) => {
      set({ selectedSignal: signal });
    },
    
    toggleSignalVisibility: () => {
      set((state: SignalHistoryState) => ({ showSignals: !state.showSignals }));
    },
    
    clearError: () => {
      set({ error: null });
    },
    
    fetchStrategies: async () => {
      try {
        const strategies = await signalHistoryApi.getAvailableStrategies();
        set({ availableStrategies: strategies });
      } catch (error) {
        console.error('Failed to fetch strategies:', error);
      }
    },

    fetchPerformanceMetrics: async (symbol: string, timeframe = '30d') => {
      try {
        const metrics = await signalHistoryApi.getSignalPerformanceMetrics(symbol, timeframe as any);
        set({ performanceMetrics: metrics });
      } catch (error) {
        console.error('Failed to fetch performance metrics:', error);
      }
    },

    compareStrategies: async (symbol: string, strategies: string[], timeframe = '30d') => {
      try {
        const comparison = await signalHistoryApi.compareSignalStrategies(symbol, strategies, timeframe);
        set({ strategyComparison: comparison });
      } catch (error) {
        console.error('Failed to compare strategies:', error);
      }
    },

    loadMore: async () => {
      const { pagination, signals, fetchSignals } = get();
      if (pagination.hasMore && signals.length > 0) {
        const firstSignal = signals[0];
        await fetchSignals(firstSignal.symbol, undefined, pagination.page + 1);
      }
    },

    reset: () => {
      set(initialState);
    },
  }),
  {
    name: 'signal-history-store',
  }
));

// Selectors for computed values
export const useFilteredSignals = () => {
  const { signals, filters } = useSignalHistoryStore();
  
  return signals.filter((signal: SignalData) => {
    const signalDate = new Date(signal.timestamp);
    const startDate = new Date(filters.dateRange.start);
    const endDate = new Date(filters.dateRange.end);
    
    return (
      signalDate >= startDate &&
      signalDate <= endDate &&
      filters.signalTypes.includes(signal.signal_type) &&
      signal.confidence >= filters.minConfidence &&
      (signal.effectiveness_score ?? 0) >= filters.minEffectiveness &&
      (filters.strategies.length === 0 || filters.strategies.includes(signal.strategy_name))
    );
  });
};

export const useSignalStats = () => {
  const signals = useFilteredSignals();
  
  const buySignals = signals.filter((s: SignalData) => s.signal_type === 'buy');
  const sellSignals = signals.filter((s: SignalData) => s.signal_type === 'sell');
  const successfulSignals = signals.filter((s: SignalData) => (s.effectiveness_score ?? 0) > 0.5);
  
  return {
    totalSignals: signals.length,
    buySignals: buySignals.length,
    sellSignals: sellSignals.length,
    successfulSignals: successfulSignals.length,
    successRate: signals.length > 0 ? (successfulSignals.length / signals.length) * 100 : 0,
    averageConfidence: signals.length > 0 
      ? signals.reduce((sum: number, s: SignalData) => sum + s.confidence, 0) / signals.length 
      : 0,
  };
};