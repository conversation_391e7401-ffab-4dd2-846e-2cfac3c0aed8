"""Tests for screener API endpoints."""

from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from ..router import _results_cache, router


@pytest.fixture
def app() -> FastAPI:
    """Create test FastAPI app with screener router."""
    test_app = FastAPI()
    test_app.include_router(router, prefix="/api")
    return test_app


@pytest.fixture
def client(app: FastAPI) -> TestClient:
    """Create test client."""
    return TestClient(app)


class TestScreenerResultsEndpoint:
    """Tests for GET /api/screener/results endpoint."""

    def setup_method(self) -> None:
        """Clear cache before each test."""
        _results_cache["data"] = None
        _results_cache["timestamp"] = None

    @patch('src.features.screener.router.ScreenerRepository')
    def test_get_screener_results_success(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test successful retrieval of screening results."""
        # Setup mock
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        mock_repo.get_latest_screening_results.return_value = [
            {"symbol": "000001.SZ", "companyName": "Ping An Bank"},
            {"symbol": "000002.SZ", "companyName": "Vanke A"}
        ]
        mock_repo_class.return_value = mock_repo

        # Make request
        response = client.get("/api/screener/results")

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["symbol"] == "000001.SZ"
        assert data[0]["companyName"] == "Ping An Bank"
        assert data[1]["symbol"] == "000002.SZ"
        assert data[1]["companyName"] == "Vanke A"

        # Verify repository was called
        mock_repo.get_latest_screening_results.assert_called_once()

    @patch('src.features.screener.router.ScreenerRepository')
    def test_get_screener_results_empty(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test endpoint with empty screening results."""
        # Setup mock
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        mock_repo.get_latest_screening_results.return_value = []
        mock_repo_class.return_value = mock_repo

        # Make request
        response = client.get("/api/screener/results")

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 0
        assert data == []

    @patch('src.features.screener.router.ScreenerRepository')
    def test_get_screener_results_database_error(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test endpoint with database connection failure."""
        # Setup mock to raise exception
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        mock_repo.get_latest_screening_results.side_effect = Exception("Database connection failed")
        mock_repo_class.return_value = mock_repo

        # Make request
        response = client.get("/api/screener/results")

        # Verify error response
        assert response.status_code == 500
        data = response.json()
        assert "Failed to retrieve screening results" in data["detail"]
        assert "Database connection failed" in data["detail"]

    @patch('src.features.screener.router.ScreenerRepository')
    def test_get_screener_results_single_result(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test endpoint with single screening result."""
        # Setup mock
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        mock_repo.get_latest_screening_results.return_value = [
            {"symbol": "000001.SZ", "companyName": "Ping An Bank"}
        ]
        mock_repo_class.return_value = mock_repo

        # Make request
        response = client.get("/api/screener/results")

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["symbol"] == "000001.SZ"
        assert data[0]["companyName"] == "Ping An Bank"

    @patch('src.features.screener.router.ScreenerRepository')
    def test_get_screener_results_response_format(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test that response format matches ScreenerItem interface."""
        # Setup mock
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        mock_repo.get_latest_screening_results.return_value = [
            {"symbol": "000001.SZ", "companyName": "Ping An Bank"},
            {"symbol": "000002.SZ", "companyName": "Vanke A"}
        ]
        mock_repo_class.return_value = mock_repo

        # Make request
        response = client.get("/api/screener/results")

        # Verify response format
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

        for item in data:
            assert isinstance(item, dict)
            assert "symbol" in item
            assert "companyName" in item
            assert isinstance(item["symbol"], str)
            assert isinstance(item["companyName"], str)

    def test_screener_results_endpoint_accessibility(self, client: TestClient) -> None:
        """Test that endpoint is accessible and properly routed."""
        # This test will fail if there's no database, but that's expected
        # We're just testing that the route exists and doesn't return 404
        response = client.get("/api/screener/results")

        # Should not return 404 (route not found)
        assert response.status_code != 404
        # Will likely return 500 due to database connection, which is fine for routing test
        assert response.status_code in [200, 500]


class TestScreenerResultsCaching:
    """Tests for caching functionality in screener results endpoint."""

    def setup_method(self) -> None:
        """Clear cache before each test."""
        _results_cache["data"] = None
        _results_cache["timestamp"] = None

    @patch('src.features.screener.router.ScreenerRepository')
    def test_cache_hit_returns_cached_data(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test that cached data is returned when cache is valid."""
        # Setup cache with valid data
        cached_data = [{"symbol": "000001.SZ", "companyName": "Test Company"}]
        _results_cache["data"] = cached_data
        _results_cache["timestamp"] = datetime.now()

        # Make request
        response = client.get("/api/screener/results")

        # Verify cached data is returned
        assert response.status_code == 200
        assert response.json() == cached_data

        # Verify repository was not called
        mock_repo_class.assert_not_called()

    @patch('src.features.screener.router.ScreenerRepository')
    def test_cache_miss_fetches_fresh_data(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test that fresh data is fetched when cache is empty or expired."""
        # Setup mock
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        fresh_data = [{"symbol": "000002.SZ", "companyName": "Fresh Company"}]
        mock_repo.get_latest_screening_results.return_value = fresh_data
        mock_repo_class.return_value = mock_repo

        # Make request with empty cache
        response = client.get("/api/screener/results")

        # Verify fresh data is returned
        assert response.status_code == 200
        assert response.json() == fresh_data

        # Verify repository was called
        mock_repo.get_latest_screening_results.assert_called_once()

        # Verify cache was updated
        assert _results_cache["data"] == fresh_data
        assert _results_cache["timestamp"] is not None

    @patch('src.features.screener.router.ScreenerRepository')
    def test_expired_cache_fetches_fresh_data(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test that fresh data is fetched when cache is expired."""
        # Setup expired cache
        old_data = [{"symbol": "000001.SZ", "companyName": "Old Company"}]
        _results_cache["data"] = old_data
        _results_cache["timestamp"] = datetime.now() - timedelta(hours=5)  # Expired (TTL is 4 hours)

        # Setup mock
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        fresh_data = [{"symbol": "000002.SZ", "companyName": "Fresh Company"}]
        mock_repo.get_latest_screening_results.return_value = fresh_data
        mock_repo_class.return_value = mock_repo

        # Make request
        response = client.get("/api/screener/results")

        # Verify fresh data is returned (not old cached data)
        assert response.status_code == 200
        assert response.json() == fresh_data
        assert response.json() != old_data

        # Verify repository was called
        mock_repo.get_latest_screening_results.assert_called_once()

    @patch('src.features.screener.router.get_scheduler')
    def test_cache_invalidation_on_screening_run(self, mock_get_scheduler: MagicMock, client: TestClient) -> None:
        """Test that cache is invalidated when new screening run is triggered."""
        # Setup cache with data
        cached_data = [{"symbol": "000001.SZ", "companyName": "Cached Company"}]
        _results_cache["data"] = cached_data
        _results_cache["timestamp"] = datetime.now()

        # Setup mock scheduler
        mock_scheduler = MagicMock()
        from unittest.mock import AsyncMock
        mock_scheduler.run_immediate_screening = AsyncMock(return_value={"status": "completed"})
        mock_get_scheduler.return_value = mock_scheduler

        # Trigger screening run
        response = client.post("/api/screener/run")

        # Debug if test fails
        if response.status_code != 200:
            pass

        # Verify request succeeded
        assert response.status_code == 200

        # Verify cache was invalidated
        assert _results_cache["data"] is None
        assert _results_cache["timestamp"] is None

    def test_cache_ttl_configuration(self) -> None:
        """Test that cache TTL is properly configured."""
        # Verify TTL is set to 4 hours as per story requirements
        assert _results_cache["ttl_hours"] == 4


class TestScreenerResultsPerformance:
    """Performance tests for screener results endpoint."""

    def setup_method(self) -> None:
        """Clear cache before each test."""
        _results_cache["data"] = None
        _results_cache["timestamp"] = None

    @patch('src.features.screener.router.ScreenerRepository')
    def test_response_time_with_cache(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test that cached responses are significantly faster."""
        import time

        # Setup mock for first request
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        test_data = [{"symbol": f"00000{i}.SZ", "companyName": f"Company {i}"} for i in range(50)]
        mock_repo.get_latest_screening_results.return_value = test_data
        mock_repo_class.return_value = mock_repo

        # First request - should hit database
        start_time = time.time()
        response1 = client.get("/api/screener/results")
        first_request_time = time.time() - start_time

        assert response1.status_code == 200
        assert len(response1.json()) == 50

        # Second request - should hit cache
        start_time = time.time()
        response2 = client.get("/api/screener/results")
        second_request_time = time.time() - start_time

        assert response2.status_code == 200
        assert response2.json() == response1.json()

        # Cached request should be faster (with tolerance for variance)
        # If they're very close, that's still good performance
        assert second_request_time <= first_request_time + 0.001  # Allow 1ms tolerance

        # Repository should only be called once (first request)
        mock_repo.get_latest_screening_results.assert_called_once()

    @patch('src.features.screener.router.ScreenerRepository')
    def test_response_time_acceptable(self, mock_repo_class: MagicMock, client: TestClient) -> None:
        """Test that response time is within acceptable limits."""
        import time

        # Setup mock
        mock_repo = MagicMock()
        mock_repo.__enter__ = MagicMock(return_value=mock_repo)
        mock_repo.__exit__ = MagicMock(return_value=None)
        test_data = [{"symbol": "000001.SZ", "companyName": "Test Company"}]
        mock_repo.get_latest_screening_results.return_value = test_data
        mock_repo_class.return_value = mock_repo

        # Measure response time
        start_time = time.time()
        response = client.get("/api/screener/results")
        response_time = time.time() - start_time

        assert response.status_code == 200
        # Response should be under 1 second for basic operations
        assert response_time < 1.0, f"Response time {response_time:.3f}s exceeds 1 second limit"
