"""
Tests for screener results router.
"""
from fastapi.testclient import TestClient


def test_get_screener_results(client: TestClient):
    """Test successful screener results retrieval."""
    response = client.get("/api/screener/results")

    assert response.status_code == 200
    data = response.json()

    # Should return a list
    assert isinstance(data, list)

    # If data exists, verify structure
    if data:
        for item in data:
            assert "symbol" in item
            assert "companyName" in item
            assert isinstance(item["symbol"], str)
            assert isinstance(item["companyName"], str)


def test_screener_results_response_format(client: TestClient, mock_screener_data):
    """Test that screener results match expected format."""
    response = client.get("/api/screener/results")

    assert response.status_code == 200
    data = response.json()

    # Verify it's a list and has expected structure
    assert isinstance(data, list)
    assert len(data) >= 0  # Can be empty or have items

    # Test the mock data structure matches what we expect
    for expected_item in mock_screener_data:
        assert "symbol" in expected_item
        assert "companyName" in expected_item
