"""Tests for ScreenerRepository."""

from collections.abc import Generator
from datetime import datetime, timedelta
from unittest.mock import patch

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from src.core.database import Base
from src.features.screener.repository import ScreenerRepository
from src.models.database import ScreenerResult


class TestScreenerRepository:
    """Test cases for ScreenerRepository."""

    @pytest.fixture
    def test_db_session(self) -> Generator[Session, None, None]:
        """Create in-memory SQLite database for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)

        session_local = sessionmaker(bind=engine)
        session = session_local()

        yield session

        session.close()

    @pytest.fixture
    def repository(self, test_db_session: Session) -> ScreenerRepository:
        """Create ScreenerRepository with test database session."""
        return ScreenerRepository(db_session=test_db_session)

    def test_save_screening_results_success(self, repository: ScreenerRepository) -> None:
        """Test successful saving of screening results."""
        results = [
            {'symbol': '000001.SZ', 'companyName': 'Ping An Bank Co., Ltd.'},
            {'symbol': '000002.SZ', 'company_name': 'China Vanke Co., Ltd.'}  # Test both key formats
        ]
        timestamp = datetime.now().isoformat()

        saved_count = repository.save_screening_results(results, timestamp)

        assert saved_count == 2

        # Verify data was saved correctly
        assert repository.db is not None
        all_results = repository.db.query(ScreenerResult).all()
        assert len(all_results) == 2

        # Check first result
        result1 = repository.db.query(ScreenerResult).filter_by(symbol='000001.SZ').first()
        assert result1 is not None
        assert result1.scan_timestamp == timestamp
        assert result1.company_name == 'Ping An Bank Co., Ltd.'

        # Check second result
        result2 = repository.db.query(ScreenerResult).filter_by(symbol='000002.SZ').first()
        assert result2 is not None
        assert result2.company_name == 'China Vanke Co., Ltd.'

    def test_save_screening_results_empty_list(self, repository: ScreenerRepository) -> None:
        """Test saving empty results list."""
        results: list[dict[str, str]] = []
        timestamp = datetime.now().isoformat()

        saved_count = repository.save_screening_results(results, timestamp)

        assert saved_count == 0

        # Verify no data was saved
        assert repository.db is not None
        all_results = repository.db.query(ScreenerResult).all()
        assert len(all_results) == 0

    def test_save_screening_results_database_error(self, repository: ScreenerRepository) -> None:
        """Test handling database error during save."""
        results = [{'symbol': '000001.SZ', 'companyName': 'Test'}]
        timestamp = datetime.now().isoformat()

        # Mock database error
        with patch.object(repository.db, 'add_all', side_effect=Exception("Database error")):
            with pytest.raises(Exception, match="Database error"):
                repository.save_screening_results(results, timestamp)

    def test_get_latest_screening_results_with_data(self, repository: ScreenerRepository) -> None:
        """Test getting latest screening results when data exists."""
        # Add test data with different timestamps
        older_timestamp = (datetime.now() - timedelta(days=1)).isoformat()
        newer_timestamp = datetime.now().isoformat()

        # Add older results
        assert repository.db is not None
        older_result = ScreenerResult(
            scan_timestamp=older_timestamp,
            symbol='000001.SZ',
            company_name='Old Company'
        )
        repository.db.add(older_result)

        # Add newer results
        newer_results = [
            ScreenerResult(
                scan_timestamp=newer_timestamp,
                symbol='000002.SZ',
                company_name='New Company 1'
            ),
            ScreenerResult(
                scan_timestamp=newer_timestamp,
                symbol='000003.SZ',
                company_name='New Company 2'
            )
        ]
        repository.db.add_all(newer_results)
        repository.db.commit()

        # Get latest results
        results = repository.get_latest_screening_results()

        assert len(results) == 2
        assert all(result['companyName'].startswith('New Company') for result in results)

        # Check specific results
        symbols = [result['symbol'] for result in results]
        assert '000002.SZ' in symbols
        assert '000003.SZ' in symbols
        assert '000001.SZ' not in symbols  # This is from older scan

    def test_get_latest_screening_results_no_data(self, repository: ScreenerRepository) -> None:
        """Test getting latest screening results when no data exists."""
        results = repository.get_latest_screening_results()

        assert results == []

    def test_get_latest_screening_results_missing_company_name(self, repository: ScreenerRepository) -> None:
        """Test getting results with missing company names."""
        timestamp = datetime.now().isoformat()

        assert repository.db is not None
        result = ScreenerResult(
            scan_timestamp=timestamp,
            symbol='000001.SZ',
            company_name=None
        )
        repository.db.add(result)
        repository.db.commit()

        results = repository.get_latest_screening_results()

        assert len(results) == 1
        assert results[0]['symbol'] == '000001.SZ'
        assert results[0]['companyName'] == '000001.SZ'  # Falls back to symbol

    def test_cleanup_old_results_with_old_data(self, repository: ScreenerRepository) -> None:
        """Test cleanup of old results when old data exists."""
        # Add old data (40 days ago)
        old_timestamp = (datetime.now() - timedelta(days=40)).isoformat()
        old_results = [
            ScreenerResult(scan_timestamp=old_timestamp, symbol='OLD1.SZ', company_name='Old 1'),
            ScreenerResult(scan_timestamp=old_timestamp, symbol='OLD2.SZ', company_name='Old 2'),
        ]
        assert repository.db is not None
        repository.db.add_all(old_results)

        # Add recent data (10 days ago)
        recent_timestamp = (datetime.now() - timedelta(days=10)).isoformat()
        recent_result = ScreenerResult(
            scan_timestamp=recent_timestamp,
            symbol='RECENT.SZ',
            company_name='Recent Company'
        )
        repository.db.add(recent_result)
        repository.db.commit()

        # Cleanup old results (keep last 30 days)
        deleted_count = repository.cleanup_old_results(days_to_keep=30)

        assert deleted_count == 2

        # Verify only recent data remains
        remaining_results = repository.db.query(ScreenerResult).all()
        assert len(remaining_results) == 1
        assert remaining_results[0].symbol == 'RECENT.SZ'

    def test_cleanup_old_results_no_old_data(self, repository: ScreenerRepository) -> None:
        """Test cleanup when no old data exists."""
        # Add only recent data
        recent_timestamp = datetime.now().isoformat()
        recent_result = ScreenerResult(
            scan_timestamp=recent_timestamp,
            symbol='RECENT.SZ',
            company_name='Recent Company'
        )
        assert repository.db is not None
        repository.db.add(recent_result)
        repository.db.commit()

        # Cleanup should find nothing to delete
        deleted_count = repository.cleanup_old_results(days_to_keep=30)

        assert deleted_count == 0

        # Verify data still exists
        remaining_results = repository.db.query(ScreenerResult).all()
        assert len(remaining_results) == 1

    def test_get_screening_history_with_data(self, repository: ScreenerRepository) -> None:
        """Test getting screening history when data exists."""
        # Add test data with different timestamps
        timestamps = [
            (datetime.now() - timedelta(days=1)).isoformat(),
            (datetime.now() - timedelta(days=2)).isoformat(),
            datetime.now().isoformat()
        ]

        # Add varying amounts of results per timestamp
        test_data = [
            # 3 results for newest timestamp
            (timestamps[2], '000001.SZ'), (timestamps[2], '000002.SZ'), (timestamps[2], '000003.SZ'),
            # 2 results for middle timestamp
            (timestamps[0], '000004.SZ'), (timestamps[0], '000005.SZ'),
            # 1 result for oldest timestamp
            (timestamps[1], '000006.SZ')
        ]

        assert repository.db is not None
        for timestamp, symbol in test_data:
            result = ScreenerResult(
                scan_timestamp=timestamp,
                symbol=symbol,
                company_name=f'Company {symbol}'
            )
            repository.db.add(result)

        repository.db.commit()

        # Get history
        history = repository.get_screening_history(limit=5)

        assert len(history) == 3

        # Should be ordered by timestamp descending
        assert history[0]['scan_timestamp'] == timestamps[2]
        assert history[0]['result_count'] == 3

        assert history[1]['scan_timestamp'] == timestamps[0]
        assert history[1]['result_count'] == 2

        assert history[2]['scan_timestamp'] == timestamps[1]
        assert history[2]['result_count'] == 1

    def test_get_screening_history_with_limit(self, repository: ScreenerRepository) -> None:
        """Test getting screening history with limit."""
        # Add data for 5 different timestamps
        assert repository.db is not None
        for i in range(5):
            timestamp = (datetime.now() - timedelta(days=i)).isoformat()
            result = ScreenerResult(
                scan_timestamp=timestamp,
                symbol=f'00000{i}.SZ',
                company_name=f'Company {i}'
            )
            repository.db.add(result)

        repository.db.commit()

        # Get history with limit of 3
        history = repository.get_screening_history(limit=3)

        assert len(history) == 3
        # Should return most recent 3
        for _i, entry in enumerate(history):
            assert entry['result_count'] == 1

    def test_get_screening_history_no_data(self, repository: ScreenerRepository) -> None:
        """Test getting screening history when no data exists."""
        history = repository.get_screening_history()

        assert history == []

    def test_context_manager_usage(self, test_db_session: Session) -> None:
        """Test using repository as context manager."""
        results = [{'symbol': '000001.SZ', 'companyName': 'Test Company'}]
        timestamp = datetime.now().isoformat()

        # Use repository as context manager
        with ScreenerRepository(db_session=test_db_session) as repo:
            saved_count = repo.save_screening_results(results, timestamp)
            assert saved_count == 1

        # Verify data was saved
        saved_result = test_db_session.query(ScreenerResult).first()
        assert saved_result is not None
        assert saved_result.symbol == '000001.SZ'
