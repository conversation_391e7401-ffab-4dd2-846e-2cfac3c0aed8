"""Real-time signal computation service for dynamic signal analysis."""

import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from ...models.stock_data import DailyPrice, StockData
from ...models.strategy import SignalResult
from ..data.service import DataIngestionService
from ..strategy.engine import StrategyEngine

logger = logging.getLogger(__name__)


class SignalComputationService:
    """Service for real-time signal computation and effectiveness analysis."""

    def __init__(self) -> None:
        """Initialize the signal computation service."""
        self.data_service = DataIngestionService()
        self.strategy_engine = StrategyEngine()
        self._signal_cache: Dict[str, Tuple[datetime, List[Dict]]] = {}
        self.cache_duration = timedelta(minutes=15)  # Cache for 15 minutes

    def compute_historical_signals(
        self,
        stock_code: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        strategies: Optional[List[str]] = None,
        min_confidence: Optional[float] = None,
        signal_types: Optional[List[str]] = None,
        min_success_rate: Optional[float] = None
    ) -> List[Dict]:
        """
        Compute historical signals for a stock using specified strategies.

        Args:
            stock_code: Stock symbol to analyze
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            strategies: List of strategies to compute (default: all)

        Returns:
            List of computed signals with timestamps and effectiveness
        """
        cache_key = f"{stock_code}_{start_date}_{end_date}_{strategies}"
        
        # Check cache first
        if self._is_cached_valid(cache_key):
            logger.info(f"Returning cached signals for {stock_code}")
            return self._signal_cache[cache_key][1]

        logger.info(f"Computing historical signals for {stock_code}")

        try:
            # Fetch stock data
            stock_data = self.data_service.fetch_stock_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )

            # Compute signals using sliding window approach
            signals = self._compute_signals_with_sliding_window(
                stock_data.daily_prices,
                strategies or ['macd', 'magic_nine']
            )

            # Calculate effectiveness for each signal
            signals_with_effectiveness = self._calculate_signal_effectiveness(
                signals, stock_data.daily_prices
            )

            # Apply additional filtering
            filtered_signals = signals_with_effectiveness
            
            # Apply confidence filtering
            if min_confidence is not None:
                filtered_signals = [s for s in filtered_signals if s.get('confidence', 0) >= min_confidence]
            
            # Apply signal type filtering
            if signal_types:
                filtered_signals = [s for s in filtered_signals if s.get('signal_type') in signal_types]
            
            # Apply success rate filtering
            if min_success_rate is not None:
                filtered_signals = [s for s in filtered_signals if s.get('is_successful', False)]

            # Cache the results
            self._signal_cache[cache_key] = (datetime.now(), filtered_signals)

            logger.info(f"Computed {len(filtered_signals)} signals for {stock_code}")
            return filtered_signals

        except Exception as e:
            logger.error(f"Error computing signals for {stock_code}: {str(e)}")
            raise

    def compute_signal_effectiveness(
        self,
        stock_code: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        strategies: Optional[List[str]] = None,
        min_confidence: Optional[float] = None,
        signal_types: Optional[List[str]] = None,
        min_success_rate: Optional[float] = None
    ) -> Dict:
        """
        Compute effectiveness metrics for signals.

        Args:
            stock_code: Stock symbol to analyze
            start_date: Start date for analysis
            end_date: End date for analysis

        Returns:
            Dictionary with effectiveness metrics
        """
        signals = self.compute_historical_signals(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            strategies=strategies,
            min_confidence=min_confidence,
            signal_types=signal_types,
            min_success_rate=min_success_rate
        )
        
        if not signals:
            return {
                'success_rate': 0.0,
                'average_return': 0.0,
                'max_drawdown': 0.0,
                'total_signals': 0,
                'profitable_signals': 0,
                'average_holding_period': 0
            }

        # Calculate metrics
        total_signals = len(signals)
        profitable_signals = sum(1 for s in signals if s.get('return_pct', 0) > 0)
        success_rate = (profitable_signals / total_signals) * 100 if total_signals > 0 else 0
        
        returns = [s.get('return_pct', 0) for s in signals]
        average_return = sum(returns) / len(returns) if returns else 0
        
        # Calculate max drawdown
        max_drawdown = self._calculate_max_drawdown(returns)
        
        # Calculate average holding period
        holding_periods = [s.get('holding_period_days', 0) for s in signals]
        average_holding_period = sum(holding_periods) / len(holding_periods) if holding_periods else 0

        return {
            'success_rate': round(success_rate, 2),
            'average_return': round(average_return, 2),
            'max_drawdown': round(max_drawdown, 2),
            'total_signals': total_signals,
            'profitable_signals': profitable_signals,
            'average_holding_period': round(average_holding_period, 1)
        }

    def _compute_signals_with_sliding_window(
        self,
        daily_prices: List[DailyPrice],
        strategies: List[str]
    ) -> List[Dict]:
        """
        Compute signals using a sliding window approach.

        Args:
            daily_prices: List of daily price data
            strategies: List of strategy names to compute

        Returns:
            List of computed signals
        """
        signals = []
        min_window_size = 30  # Minimum data points for analysis
        
        for i in range(min_window_size, len(daily_prices)):
            window_data = daily_prices[:i+1]
            
            # Compute signals for each strategy
            for strategy in strategies:
                signal = self._compute_single_signal(window_data, strategy, i)
                if signal:
                    signals.append(signal)
        
        return signals

    def _compute_single_signal(
        self,
        window_data: List[DailyPrice],
        strategy: str,
        index: int
    ) -> Optional[Dict]:
        """
        Compute a single signal for given window data and strategy.

        Args:
            window_data: Price data window
            strategy: Strategy name
            index: Current index in the full dataset

        Returns:
            Signal dictionary or None if no signal
        """
        try:
            current_price = window_data[-1]
            
            # Convert DailyPrice list to pandas DataFrame
            df_data = self._convert_to_dataframe(window_data)
            
            # Use strategy engine to compute signals
            result = self.strategy_engine.calculate_signals(df_data, [strategy])
            
            if result and result.get('signal') not in ['HOLD', 'NO_SIGNAL']:
                return {
                    'timestamp': current_price.date,
                    'price': current_price.close,
                    'signal_type': result.get('signal'),
                    'strategy': strategy,
                    'confidence': self._extract_confidence(result, strategy),
                    'index': index
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"Error computing {strategy} signal at index {index}: {str(e)}")
            return None

    def _calculate_signal_effectiveness(
        self,
        signals: List[Dict],
        daily_prices: List[DailyPrice]
    ) -> List[Dict]:
        """
        Calculate effectiveness for each signal based on future price movements.

        Args:
            signals: List of computed signals
            daily_prices: Full price dataset

        Returns:
            Signals with effectiveness metrics added
        """
        enhanced_signals = []
        
        for signal in signals:
            signal_index = signal['index']
            signal_price = signal['price']
            
            # Look ahead to calculate effectiveness (e.g., 5, 10, 20 days)
            effectiveness = self._calculate_forward_returns(
                signal_index, signal_price, daily_prices, signal['signal_type']
            )
            
            enhanced_signal = signal.copy()
            enhanced_signal.update(effectiveness)
            enhanced_signals.append(enhanced_signal)
        
        return enhanced_signals

    def _calculate_forward_returns(
        self,
        signal_index: int,
        signal_price: float,
        daily_prices: List[DailyPrice],
        signal_type: str
    ) -> Dict:
        """
        Calculate forward returns for signal effectiveness.

        Args:
            signal_index: Index of signal in price data
            signal_price: Price at signal time
            daily_prices: Full price dataset
            signal_type: BUY or SELL signal

        Returns:
            Dictionary with effectiveness metrics
        """
        # Default holding periods to check
        holding_periods = [5, 10, 20]  # days
        returns = {}
        
        for period in holding_periods:
            future_index = signal_index + period
            if future_index < len(daily_prices):
                future_price = daily_prices[future_index].close
                
                if signal_type == 'BUY':
                    return_pct = ((future_price - signal_price) / signal_price) * 100
                else:  # SELL
                    return_pct = ((signal_price - future_price) / signal_price) * 100
                
                returns[f'return_{period}d'] = round(return_pct, 2)
        
        # Use 10-day return as primary effectiveness metric
        primary_return = returns.get('return_10d', 0)
        
        return {
            'return_pct': primary_return,
            'holding_period_days': 10,
            'is_successful': primary_return > 0,
            'forward_returns': returns
        }

    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """
        Calculate maximum drawdown from a series of returns.

        Args:
            returns: List of return percentages

        Returns:
            Maximum drawdown percentage
        """
        if not returns:
            return 0.0
        
        cumulative = []
        running_total = 0
        
        for ret in returns:
            running_total += ret
            cumulative.append(running_total)
        
        max_drawdown = 0
        peak = cumulative[0]
        
        for value in cumulative:
            if value > peak:
                peak = value
            drawdown = peak - value
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown

    def _is_cached_valid(self, cache_key: str) -> bool:
        """
        Check if cached data is still valid.

        Args:
            cache_key: Cache key to check

        Returns:
            True if cache is valid, False otherwise
        """
        if cache_key not in self._signal_cache:
            return False
        
        cached_time, _ = self._signal_cache[cache_key]
        return datetime.now() - cached_time < self.cache_duration

    def _convert_to_dataframe(self, daily_prices: List[DailyPrice]) -> pd.DataFrame:
        """
        Convert list of DailyPrice objects to pandas DataFrame.
        
        Args:
            daily_prices: List of DailyPrice objects
            
        Returns:
            DataFrame with OHLCV data and datetime index
        """
        data = []
        for price in daily_prices:
            data.append({
                'date': price.date,
                'open': price.open,
                'high': price.high,
                'low': price.low,
                'close': price.close,
                'volume': price.volume
            })
        
        df = pd.DataFrame(data)
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        return df
    
    def _extract_confidence(self, result: Dict, strategy: str) -> float:
        """
        Extract confidence score from strategy result.
        
        Args:
            result: Strategy calculation result
            strategy: Strategy name
            
        Returns:
            Confidence score (0.0 to 1.0)
        """
        strategies_data = result.get('strategies', {})
        strategy_result = strategies_data.get(strategy, {})
        
        # Extract confidence based on strategy type
        if strategy == 'rsi':
            rsi_value = strategy_result.get('rsi_value', 50)
            # Higher confidence for more extreme RSI values
            if rsi_value <= 30:
                return min(1.0, (30 - rsi_value) / 30 + 0.5)
            elif rsi_value >= 70:
                return min(1.0, (rsi_value - 70) / 30 + 0.5)
            return 0.3
        
        elif strategy == 'bollinger_bands':
            position = strategy_result.get('current_position', {})
            if 'Below Lower Band' in str(position) or 'Above Upper Band' in str(position):
                return 0.8
            return 0.4
        
        elif strategy == 'macd':
            # Default confidence for MACD signals
            return 0.7
        
        elif strategy == 'magic_nine':
            # High confidence for Magic Nine signals when complete
            return 0.9
        
        return 0.5  # Default confidence

    def compare_strategies(
        self,
        symbol: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        strategies: Optional[List[str]] = None,
        min_confidence: Optional[float] = None,
        signal_types: Optional[List[str]] = None,
        min_success_rate: Optional[float] = None
    ) -> Dict:
        """Compare effectiveness of different strategies.
        
        Args:
            symbol: Stock symbol to analyze
            start_date: Start date for analysis
            end_date: End date for analysis
            strategies: List of strategies to compare
            min_confidence: Minimum confidence threshold
            signal_types: Filter by signal types
            min_success_rate: Minimum success rate threshold
            
        Returns:
            Dictionary with strategy comparison results
        """
        try:
            strategies_to_compare = strategies or ['macd', 'magic_nine', 'rsi', 'bollinger_bands']
            comparison_results = []
            
            for strategy in strategies_to_compare:
                # Compute effectiveness for each strategy individually
                strategy_effectiveness = self.compute_signal_effectiveness(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    strategies=[strategy],
                    min_confidence=min_confidence,
                    signal_types=signal_types,
                    min_success_rate=min_success_rate
                )
                
                comparison_results.append({
                    'strategy_name': strategy,
                    'metrics': strategy_effectiveness.get('effectiveness_metrics', {})
                })
            
            return {
                'stock_code': symbol,
                'analysis_period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'computation_time': datetime.now().isoformat(),
                'strategies': comparison_results
            }
            
        except Exception as e:
            logger.error(f"Error comparing strategies for {symbol}: {str(e)}")
            raise RuntimeError(f"Strategy comparison failed: {str(e)}")

    def get_available_strategies(self) -> List[str]:
        """Get list of available strategies.
        
        Returns:
            List of strategy names
        """
        return self.strategy_engine.get_available_strategies()

    def clear_cache(self) -> None:
        """Clear the signal computation cache."""
        self._signal_cache.clear()
        logger.info("Signal computation cache cleared")