import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'

interface ErrorMessageProps {
  message: string
  title?: string
  className?: string
  onRetry?: () => void
}

export function ErrorMessage({ 
  message, 
  title, 
  className,
  onRetry 
}: ErrorMessageProps) {
  const { t } = useTranslation('common')
  return (
    <div 
      className={cn(
        'rounded-md border border-destructive/20 bg-destructive/5 p-4',
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <svg
            className="h-5 w-5 text-destructive"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-destructive">
            {title || t('error.title')}
          </h3>
          <p className="mt-1 text-sm text-destructive/80">
            {message}
          </p>
          {onRetry && (
            <div className="mt-3">
              <button
                type="button"
                onClick={onRetry}
                className="inline-flex items-center rounded-md bg-destructive px-3 py-1.5 text-sm font-medium text-destructive-foreground hover:bg-destructive/90 focus:outline-none focus:ring-2 focus:ring-destructive focus:ring-offset-2"
              >
                {t('error.tryAgain')}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}