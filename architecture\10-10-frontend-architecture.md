### 10. Frontend Architecture

* **Component Organization:** A feature-based directory structure will be used (e.g., `/src/features/Analysis/`).
* **State Management:** Zustand will be used for simple global state. Local component state will be used for UI-specific state.
* **Routing:** `react-router-dom` will handle client-side routing between the `Analysis` and `Screener` views.
* **Services:** A dedicated API client service layer will handle all backend communication.

***
