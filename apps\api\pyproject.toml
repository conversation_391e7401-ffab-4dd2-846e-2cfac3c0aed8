[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "trading-agent-api"
version = "1.0.0"
description = "Python FastAPI backend for trading agent application"
requires-python = ">=3.11"
dependencies = [
    "apscheduler==3.10.4",
    "pandas-stubs>=2.3.0.250703",
]

[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "W", "F", "I", "N", "UP", "S", "B", "A", "C4", "T20"]
ignore = ["E501", "S101"]  # Line too long, assert used

[tool.ruff.per-file-ignores]
"tests/**/*.py" = ["S101"]  # Allow assert in tests

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[[tool.mypy.overrides]]
module = ["apscheduler.*"]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --tb=short"
asyncio_mode = "auto"

[dependency-groups]
dev = [
    "ruff>=0.12.9",
]
