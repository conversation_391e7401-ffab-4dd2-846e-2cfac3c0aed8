"""Tests for ScreeningScheduler."""

from datetime import datetime
from typing import Any, Generator
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest

from src.features.screener.scheduler import ScreeningScheduler, get_scheduler


class TestScreeningScheduler:
    """Test cases for ScreeningScheduler."""

    @pytest.fixture
    def scheduler(self) -> Generator[ScreeningScheduler, None, None]:
        """Create ScreeningScheduler instance for testing."""
        scheduler_instance = ScreeningScheduler()
        yield scheduler_instance
        # Cleanup: ensure scheduler is completely shut down after each test
        if scheduler_instance.scheduler.running:
            scheduler_instance.scheduler.shutdown(wait=True)
        scheduler_instance.is_running = False

    @pytest.fixture
    def mock_screening_result(self) -> dict[str, Any]:
        """Mock successful screening result."""
        return {
            'scan_timestamp': datetime.now().isoformat(),
            'total_stocks_processed': 10,
            'signals_found': 2,
            'results_saved': 2,
            'errors_encountered': 0,
            'processing_time_seconds': 1.5,
            'results': [
                {'symbol': '000001.SZ', 'companyName': 'Test Company 1'},
                {'symbol': '000002.SZ', 'companyName': 'Test Company 2'}
            ]
        }

    def test_scheduler_initialization(self, scheduler: ScreeningScheduler) -> None:
        """Test scheduler initialization."""
        assert scheduler.timezone == "Asia/Shanghai"
        assert not scheduler.is_running
        assert scheduler.last_job_result is None
        assert len(scheduler.job_history) == 0
        assert scheduler.screening_service is not None

    @pytest.mark.asyncio
    async def test_start_stop_scheduler(self) -> None:
        """Test starting and stopping the scheduler."""
        # Create a fresh scheduler instance for this test
        test_scheduler = ScreeningScheduler()

        try:
            # Initially not running
            assert not test_scheduler.is_running
            assert not test_scheduler.scheduler.running

            # Start scheduler
            test_scheduler.start_scheduler()
            assert test_scheduler.is_running
            assert test_scheduler.scheduler.running

            # Stop scheduler
            test_scheduler.stop_scheduler()
            assert not test_scheduler.is_running

            # Give APScheduler a moment to fully shut down
            import asyncio
            await asyncio.sleep(0.5)
            assert not test_scheduler.scheduler.running

            # Stopping again should log warning but not fail
            test_scheduler.stop_scheduler()  # Should log warning
            assert not test_scheduler.is_running
        finally:
            # Ensure cleanup
            if test_scheduler.scheduler.running:
                test_scheduler.scheduler.shutdown(wait=True)
                await asyncio.sleep(0.1)

    def test_schedule_daily_screening(self, scheduler: ScreeningScheduler) -> None:
        """Test scheduling daily screening."""
        scheduler.start_scheduler()

        # Schedule daily screening
        job = scheduler.schedule_daily_screening(hour=15, minute=30)

        assert job.id == 'daily_screening'
        assert job.name == 'Daily Stock Screening'
        assert job.next_run_time is not None

        # Check job is in scheduler
        jobs = scheduler.scheduler.get_jobs()
        assert len(jobs) == 1
        assert jobs[0].id == 'daily_screening'

        scheduler.stop_scheduler()

    def test_schedule_custom_screening(self, scheduler: ScreeningScheduler) -> None:
        """Test scheduling with custom cron expression."""
        scheduler.start_scheduler()

        # Schedule custom screening (weekdays at 2 PM)
        cron_expr = "0 14 * * 1-5"
        job = scheduler.schedule_custom_screening(cron_expr, job_id="weekday_screening")

        assert job.id == "weekday_screening"
        assert job.name == "Custom Stock Screening (weekday_screening)"
        assert job.next_run_time is not None

        scheduler.stop_scheduler()

    def test_remove_job(self, scheduler: ScreeningScheduler) -> None:
        """Test removing scheduled jobs."""
        scheduler.start_scheduler()

        # Schedule a job
        scheduler.schedule_daily_screening()
        jobs = scheduler.scheduler.get_jobs()
        assert len(jobs) == 1

        # Remove the job
        success = scheduler.remove_job('daily_screening')
        assert success is True

        jobs = scheduler.scheduler.get_jobs()
        assert len(jobs) == 0

        # Try to remove non-existent job
        success = scheduler.remove_job('non_existent')
        assert success is False

        scheduler.stop_scheduler()

    @pytest.mark.asyncio
    async def test_run_immediate_screening_success(self, scheduler: ScreeningScheduler, mock_screening_result: dict[str, Any]) -> None:
        """Test successful immediate screening run."""
        with patch.object(scheduler.screening_service, 'run_screening', return_value=mock_screening_result):
            result = await scheduler.run_immediate_screening()

            assert result['status'] == 'completed'
            assert result['total_stocks_processed'] == 10
            assert result['signals_found'] == 2
            assert 'job_start_time' in result
            assert 'job_end_time' in result
            assert result['attempt'] == 1

            # Check last job result was stored
            assert scheduler.last_job_result == result

    @pytest.mark.asyncio
    async def test_run_screening_job_with_retries(self, scheduler: ScreeningScheduler) -> None:
        """Test screening job with retry logic."""
        # Mock service to fail twice then succeed
        call_count = 0
        def mock_screening_failure(*args: Any, **kwargs: Any) -> dict[str, Any]:
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise RuntimeError("Temporary failure")
            return {
                'scan_timestamp': datetime.now().isoformat(),
                'total_stocks_processed': 5,
                'signals_found': 1,
                'results_saved': 1,
                'errors_encountered': 0,
                'processing_time_seconds': 0.5,
                'results': []
            }

        with patch.object(scheduler.screening_service, 'run_screening', side_effect=mock_screening_failure), \
             patch('asyncio.sleep', new_callable=AsyncMock):  # Mock sleep to speed up test

            result = await scheduler._run_screening_job()

            assert result['status'] == 'completed'
            assert result['attempt'] == 3  # Third attempt succeeded
            assert call_count == 3

    @pytest.mark.asyncio
    async def test_run_screening_job_max_retries_exhausted(self, scheduler: ScreeningScheduler) -> None:
        """Test screening job when all retries are exhausted."""
        with patch.object(scheduler.screening_service, 'run_screening', side_effect=RuntimeError("Persistent failure")), \
             patch('asyncio.sleep', new_callable=AsyncMock):  # Mock sleep to speed up test

            with pytest.raises(RuntimeError, match="Persistent failure"):
                await scheduler._run_screening_job()

            # Check error was recorded
            assert scheduler.last_job_result is not None
            assert scheduler.last_job_result['status'] == 'failed'
            assert scheduler.last_job_result['total_attempts'] == 3
            assert 'error' in scheduler.last_job_result

    def test_get_scheduler_status(self, scheduler: ScreeningScheduler) -> None:
        """Test getting scheduler status."""
        # Start scheduler and schedule a job
        scheduler.start_scheduler()
        scheduler.schedule_daily_screening(hour=10, minute=0)

        status = scheduler.get_scheduler_status()

        assert status['scheduler_running'] is True
        assert status['timezone'] == "Asia/Shanghai"
        assert len(status['active_jobs']) == 1
        assert status['active_jobs'][0]['id'] == 'daily_screening'
        assert status['active_jobs'][0]['name'] == 'Daily Stock Screening'
        assert status['last_job_result'] is None  # No jobs run yet
        assert status['job_history_count'] == 0

        scheduler.stop_scheduler()

    def test_job_history_management(self, scheduler: ScreeningScheduler) -> None:
        """Test job history recording and management."""
        # Simulate job execution events
        from datetime import datetime

        # Mock successful job execution
        success_event = Mock()
        success_event.job_id = 'test_job'
        success_event.scheduled_run_time = datetime.now()
        scheduler.last_job_result = {'status': 'success', 'signals_found': 5}

        scheduler._job_executed_listener(success_event)

        assert len(scheduler.job_history) == 1
        assert scheduler.job_history[0]['status'] == 'success'
        assert scheduler.job_history[0]['job_id'] == 'test_job'

        # Mock failed job execution
        error_event = Mock()
        error_event.job_id = 'test_job_2'
        error_event.scheduled_run_time = datetime.now()
        error_event.exception = RuntimeError("Test error")
        error_event.traceback = "Test traceback"

        scheduler._job_error_listener(error_event)

        assert len(scheduler.job_history) == 2
        assert scheduler.job_history[1]['status'] == 'error'
        assert scheduler.job_history[1]['error'] == 'Test error'

    def test_job_history_limit(self, scheduler: ScreeningScheduler) -> None:
        """Test that job history is limited to 10 entries."""
        # Add 12 job entries
        for i in range(12):
            event = Mock()
            event.job_id = f'job_{i}'
            event.scheduled_run_time = datetime.now()
            scheduler.last_job_result = {'job_num': i}
            scheduler._job_executed_listener(event)

        # Should only keep last 10
        assert len(scheduler.job_history) == 10
        assert scheduler.job_history[0]['result']['job_num'] == 2  # First kept entry
        assert scheduler.job_history[-1]['result']['job_num'] == 11  # Last entry

    def test_get_job_history(self, scheduler: ScreeningScheduler) -> None:
        """Test getting job history with limit."""
        # Add some job history entries
        for i in range(5):
            event = Mock()
            event.job_id = f'job_{i}'
            event.scheduled_run_time = datetime.now()
            scheduler.last_job_result = {'job_num': i}
            scheduler._job_executed_listener(event)

        # Test getting all history
        history = scheduler.get_job_history(limit=10)
        assert len(history) == 5

        # Test getting limited history
        history = scheduler.get_job_history(limit=3)
        assert len(history) == 3
        assert history[-1]['result']['job_num'] == 4  # Most recent

        # Test empty history
        scheduler.job_history = []
        history = scheduler.get_job_history()
        assert len(history) == 0

    @pytest.mark.asyncio
    async def test_managed_scheduler_context_manager(self, scheduler: ScreeningScheduler) -> None:
        """Test scheduler context manager."""
        assert not scheduler.is_running

        async with scheduler.managed_scheduler() as managed_scheduler:
            assert managed_scheduler is scheduler
            assert scheduler.is_running

        # Should be stopped after context exit
        assert not scheduler.is_running

    def test_singleton_scheduler_pattern(self) -> None:
        """Test that get_scheduler returns singleton instance."""
        scheduler1 = get_scheduler()
        scheduler2 = get_scheduler()

        assert scheduler1 is scheduler2
        assert isinstance(scheduler1, ScreeningScheduler)

    def test_scheduler_timezone_configuration(self) -> None:
        """Test scheduler with different timezone."""
        scheduler = ScreeningScheduler(timezone="UTC")

        assert scheduler.timezone == "UTC"
        assert scheduler.scheduler.timezone.zone == "UTC"
