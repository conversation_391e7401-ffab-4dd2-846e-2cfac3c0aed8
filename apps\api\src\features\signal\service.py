"""Signal service that orchestrates data fetching and strategy calculations."""

import logging
from typing import Optional

from ...models.strategy import SignalResult
from ..data.service import DataIngestionService
from ..strategy.engine import StrategyEngine

logger = logging.getLogger(__name__)


class SignalService:
    """Service that orchestrates data fetching and strategy calculations."""

    def __init__(self) -> None:
        """Initialize the signal service with required dependencies."""
        self.data_service = DataIngestionService()
        self.strategy_engine = StrategyEngine()

    def get_signal_for_stock(
        self,
        stock_code: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> SignalResult:
        """
        Get complete signal analysis for a given stock.

        Args:
            stock_code: Stock symbol to analyze (e.g., "000001")
            start_date: Optional start date in YYYY-MM-DD format
            end_date: Optional end date in YYYY-MM-DD format

        Returns:
            SignalResult with complete analysis and trading signal

        Raises:
            ValueError: If stock_code is invalid or data cannot be processed
            RuntimeError: If external services are unavailable
        """
        logger.info(f"Processing signal request for stock: {stock_code}")

        try:
            # Step 1: Fetch stock data using data ingestion service
            stock_data = self.data_service.fetch_stock_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )

            logger.info(f"Retrieved {len(stock_data.daily_prices)} data points for {stock_code}")

            # Step 2: Calculate signal using strategy engine
            signal_result = self.strategy_engine.calculate_signal(stock_data)

            logger.info(f"Signal analysis completed for {stock_code}: {signal_result.signal}")

            return signal_result

        except ValueError as e:
            # Data validation or parsing errors from data service or strategy engine
            logger.error(f"Data validation error for stock {stock_code}: {str(e)}")
            raise ValueError(f"Invalid stock data for {stock_code}: {str(e)}")

        except RuntimeError as e:
            # Check if this is from data service (external) or strategy engine (internal)
            error_msg = str(e)
            if "akshare" in error_msg.lower() or "data service" in error_msg.lower():
                # External service errors (akshare unavailable, etc.)
                logger.error(f"External service error for stock {stock_code}: {str(e)}")
                raise RuntimeError(f"Signal analysis service unavailable: {str(e)}")
            else:
                # Strategy engine or other internal processing errors
                logger.error(f"Processing error for stock {stock_code}: {str(e)}")
                raise RuntimeError(f"Signal processing failed: {str(e)}")

        except Exception as e:
            # Other unexpected errors
            logger.error(f"Unexpected error processing signal for stock {stock_code}: {str(e)}")
            raise RuntimeError(f"Signal processing failed: {str(e)}")
