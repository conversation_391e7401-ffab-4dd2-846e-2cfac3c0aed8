"""Tests for MACD calculator."""

import math
from datetime import datetime, timedelta

import pytest

from src.features.strategy.calculators.macd import MACDCalculator
from src.models.stock_data import DailyPrice


class TestMACDCalculator:
    """Test cases for MACD calculator."""

    def setup_method(self):
        """Set up test fixtures."""
        self.calculator = MACDCalculator()

    @pytest.fixture
    def basic_price_data(self):
        """Basic price data for MACD calculation."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create 50 days of price data with trend
        for i in range(50):
            date = base_date + timedelta(days=i)
            # Create slightly trending price with some volatility
            base_price = 100 + (i * 0.5) + (math.sin(i * 0.1) * 2)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=base_price - 0.5,
                high=base_price + 1.0,
                low=base_price - 1.0,
                close=base_price,
                volume=1000000 + (i * 1000)
            ))

        return prices

    @pytest.fixture
    def insufficient_data(self):
        """Insufficient data for MACD calculation."""
        base_date = datetime(2025, 1, 1)
        prices = []

        for i in range(20):  # Less than required minimum
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=100.0,
                high=101.0,
                low=99.0,
                close=100.0 + i,
                volume=1000000
            ))

        return prices

    @pytest.fixture
    def divergence_pattern_data(self):
        """Price data designed to create top divergence."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create pattern: price makes higher highs, MACD makes lower highs
        # First part: establish base trend
        base_closes = []
        for i in range(30):
            base_closes.append(100 + i * 0.3)

        # Create divergence pattern
        # Peak 1: High price, high MACD
        peak1_closes = [109, 110, 111, 112, 113]
        # Dip
        dip_closes = [111, 110, 109, 108, 107]
        # Peak 2: Higher price, but conditions for lower MACD
        peak2_closes = [109, 111, 113, 115, 117]  # Higher price peak

        all_closes = base_closes + peak1_closes + dip_closes + peak2_closes

        for i, close in enumerate(all_closes):
            date = base_date + timedelta(days=i)
            high = close + 0.5
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=high,
                low=close - 1.0,
                close=close,
                volume=1000000
            ))

        return prices

    def test_insufficient_data_raises_error(self, insufficient_data):
        """Test that insufficient data raises ValueError."""
        with pytest.raises(ValueError, match="Need at least"):
            self.calculator.calculate(insufficient_data)

    def test_basic_macd_calculation(self, basic_price_data):
        """Test basic MACD calculation."""
        result = self.calculator.calculate(basic_price_data)

        assert result is not None
        assert len(result.macd_line) == len(basic_price_data)
        assert len(result.signal_line) == len(basic_price_data)
        assert len(result.histogram) == len(basic_price_data)

        # Early values should be None due to EMA requirements
        assert result.macd_line[0] is None
        assert result.signal_line[0] is None
        assert result.histogram[0] is None

        # Later values should have calculated results
        non_none_macd = [v for v in result.macd_line if v is not None]
        non_none_signal = [v for v in result.signal_line if v is not None]
        non_none_histogram = [v for v in result.histogram if v is not None]

        assert len(non_none_macd) > 0
        assert len(non_none_signal) > 0
        assert len(non_none_histogram) > 0

    def test_ema_calculation(self):
        """Test EMA calculation with known values."""
        # Test with simple ascending values
        values = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
        ema_5 = self.calculator._calculate_ema(values, 5)

        # First 4 values should be None
        for i in range(4):
            assert ema_5[i] is None

        # 5th value should be SMA of first 5 values
        expected_sma = sum(values[:5]) / 5
        assert abs(ema_5[4] - expected_sma) < 0.001

        # Subsequent values should be calculated using EMA formula
        assert ema_5[5] is not None
        assert ema_5[5] != ema_5[4]  # Should change

    def test_macd_line_calculation(self):
        """Test MACD line calculation (fast EMA - slow EMA)."""
        fast_ema = [None, None, 10.0, 11.0, 12.0]
        slow_ema = [None, None, 9.5, 10.5, 11.5]

        macd_line = self.calculator._calculate_macd_line(fast_ema, slow_ema)

        assert macd_line[0] is None
        assert macd_line[1] is None
        assert abs(macd_line[2] - 0.5) < 0.001  # 10.0 - 9.5
        assert abs(macd_line[3] - 0.5) < 0.001  # 11.0 - 10.5
        assert abs(macd_line[4] - 0.5) < 0.001  # 12.0 - 11.5

    def test_histogram_calculation(self):
        """Test histogram calculation (MACD - Signal)."""
        macd_line = [None, None, 0.5, 0.6, 0.7]
        signal_line = [None, None, None, 0.55, 0.65]

        histogram = self.calculator._calculate_histogram(macd_line, signal_line)

        assert histogram[0] is None
        assert histogram[1] is None
        assert histogram[2] is None  # Signal not available yet
        assert abs(histogram[3] - 0.05) < 0.001  # 0.6 - 0.55
        assert abs(histogram[4] - 0.05) < 0.001  # 0.7 - 0.65

    def test_peak_detection(self):
        """Test peak detection algorithm."""
        # Create values with clear peaks
        values = [1, 2, 5, 2, 1, 3, 8, 3, 2, 4, 9, 4, 1]
        peaks = self.calculator._find_peaks(values, min_distance=2)

        # Should find peaks at indices where values are locally maximum
        assert 2 in peaks  # Value 5
        assert 6 in peaks  # Value 8
        assert 10 in peaks  # Value 9

    def test_divergence_detection(self, divergence_pattern_data):
        """Test top divergence detection."""
        result = self.calculator.calculate(divergence_pattern_data)

        # Should detect some divergence points given the pattern
        assert isinstance(result.divergence_points, list)

        # Each divergence point should have required fields
        for point in result.divergence_points:
            assert hasattr(point, 'date')
            assert hasattr(point, 'type')
            assert point.type == 'TOP'

    def test_custom_parameters(self):
        """Test MACD with custom parameters."""
        custom_calculator = MACDCalculator(
            fast_period=5,
            slow_period=10,
            signal_period=3,
            divergence_lookback=10
        )

        # Create enough data for custom parameters
        base_date = datetime(2025, 1, 1)
        prices = []

        for i in range(30):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=100.0 + i,
                high=101.0 + i,
                low=99.0 + i,
                close=100.0 + i,
                volume=1000000
            ))

        result = custom_calculator.calculate(prices)

        assert result is not None
        assert len(result.macd_line) == len(prices)

    def test_invalid_parameters(self):
        """Test that invalid parameters raise errors."""
        # Fast period >= slow period should raise error
        with pytest.raises(ValueError, match="Fast period must be less than slow period"):
            MACDCalculator(fast_period=26, slow_period=12)

        # Very small divergence lookback should raise error
        with pytest.raises(ValueError, match="Divergence lookback must be at least 5"):
            MACDCalculator(divergence_lookback=2)

    def test_all_same_prices(self):
        """Test MACD with all same closing prices."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # All same closing price
        for i in range(50):
            date = base_date + timedelta(days=i)
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=100.0,
                high=100.0,
                low=100.0,
                close=100.0,
                volume=1000000
            ))

        result = self.calculator.calculate(prices)

        # MACD should be close to zero with flat prices
        non_none_macd = [v for v in result.macd_line if v is not None]
        for macd_val in non_none_macd[-10:]:  # Check last 10 values
            assert abs(macd_val) < 0.001

    def test_signal_line_follows_macd(self, basic_price_data):
        """Test that signal line follows MACD line trends."""
        result = self.calculator.calculate(basic_price_data)

        # Find indices where both MACD and signal are not None
        valid_indices = []
        for i, (macd, signal) in enumerate(zip(result.macd_line, result.signal_line)):
            if macd is not None and signal is not None:
                valid_indices.append(i)

        assert len(valid_indices) > 10  # Should have enough valid data points

        # Signal line should generally lag MACD line movements
        # (This is a behavioral test, not exact mathematical requirement)
        macd_values = [result.macd_line[i] for i in valid_indices[-10:]]
        signal_values = [result.signal_line[i] for i in valid_indices[-10:]]

        assert len(macd_values) == len(signal_values)

    def test_no_divergence_in_trending_market(self):
        """Test that strong trending market doesn't produce false divergences."""
        base_date = datetime(2025, 1, 1)
        prices = []

        # Create strong uptrend
        for i in range(60):
            date = base_date + timedelta(days=i)
            close = 100 + (i * 1.0)  # Strong uptrend
            prices.append(DailyPrice(
                date=date.strftime('%Y-%m-%d'),
                open=close - 0.5,
                high=close + 0.5,
                low=close - 1.0,
                close=close,
                volume=1000000
            ))

        result = self.calculator.calculate(prices)

        # Should have minimal or no divergences in strong trend
        # (This is a behavioral expectation)
        assert len(result.divergence_points) <= 1
