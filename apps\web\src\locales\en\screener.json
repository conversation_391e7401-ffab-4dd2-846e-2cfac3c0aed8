{"title": "<PERSON>", "filters": {"title": "Filters", "marketCap": {"label": "Market Cap", "small": "Small Cap", "mid": "Mid Cap", "large": "Large Cap"}, "sector": {"label": "Sector", "technology": "Technology", "healthcare": "Healthcare", "finance": "Finance", "energy": "Energy", "consumer": "Consumer"}, "priceRange": {"label": "Price Range", "min": "<PERSON>", "max": "Max Price"}, "volume": {"label": "Volume", "minVolume": "Minimum Volume"}, "apply": "Apply Filters", "reset": "Reset Filters"}, "results": {"title": "Screening Results", "columns": {"symbol": "Symbol", "name": "Company Name", "price": "Price", "change": "Change", "changePercent": "Change %", "volume": "Volume", "marketCap": "Market Cap", "sector": "Sector", "signal": "Signal"}, "noResults": "No stocks match your criteria", "totalResults": "{{count}} stocks found", "loadMore": "Load More", "export": "Export Results"}, "loading": {"screening": "Screening stocks...", "loadingMore": "Loading more results..."}, "errors": {"screeningFailed": "Failed to screen stocks", "noConnection": "Unable to connect to screening service"}}