"""Pydantic models for strategy calculations and results."""

from typing import Literal, Optional

from pydantic import BaseModel, Field

from .stock_data import DailyPrice


class DivergencePoint(BaseModel):
    """A point where divergence occurs."""

    date: str = Field(..., description="Date of divergence point in YYYY-MM-DD format")
    type: Literal['TOP'] = Field(..., description="Type of divergence")


class ChartData(BaseModel):
    """Chart data containing price and indicator values."""

    daily_prices: list[DailyPrice] = Field(..., description="Daily price data")
    magic_nine_sequence: list[Optional[int]] = Field(..., description="Magic Nine Turns sequence values")
    macd_line: list[Optional[float]] = Field(..., description="MACD line values")
    signal_line: list[Optional[float]] = Field(..., description="MACD signal line values")
    divergence_points: list[DivergencePoint] = Field(..., description="Top divergence points")


class SignalResult(BaseModel):
    """Complete signal analysis result for a stock."""

    symbol: str = Field(..., description="Stock symbol")
    last_scan_date: str = Field(..., description="Last analysis date in ISO format")
    signal: Literal['SELL_CANDIDATE', 'HOLD', 'NO_SIGNAL'] = Field(..., description="Trading signal")
    chart_data: ChartData = Field(..., description="Chart data with indicators")


class MagicNineTurnsResult(BaseModel):
    """Result from Magic Nine Turns calculation."""

    sequence: list[Optional[int]] = Field(..., description="Sequence values (1-9 or None)")
    current_count: int = Field(..., description="Current count in sequence", ge=0, le=9)
    is_complete: bool = Field(..., description="Whether a 9-count sequence is complete")
    direction: Literal['UP', 'DOWN', 'NONE'] = Field(..., description="Current counting direction")


class MACDResult(BaseModel):
    """Result from MACD calculation."""

    macd_line: list[Optional[float]] = Field(..., description="MACD line values")
    signal_line: list[Optional[float]] = Field(..., description="Signal line values")
    histogram: list[Optional[float]] = Field(..., description="MACD histogram values")
    divergence_points: list[DivergencePoint] = Field(..., description="Detected divergence points")
