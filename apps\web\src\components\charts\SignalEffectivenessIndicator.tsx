import React, { memo, useCallback, useMemo } from 'react';
import { TrendingUp, TrendingDown, Target, Clock, BarChart3 } from 'lucide-react';
import { DataProcessor, PerformanceMonitor, withPerformanceTracking } from '../../utils/performance';
import type {
  SignalEffectiveness,
  SignalPerformanceMetrics,
  SignalType,
} from '@trading-agent/shared-types';

interface SignalEffectivenessIndicatorProps {
  effectiveness?: SignalEffectiveness;
  performanceMetrics?: SignalPerformanceMetrics;
  signalType?: SignalType;
  compact?: boolean;
}

const SignalEffectivenessIndicator: React.FC<SignalEffectivenessIndicatorProps> = memo(({
  effectiveness,
  performanceMetrics,
  signalType,
  compact = false,
}) => {
  const getEffectivenessRating = useCallback(DataProcessor.memoize((successRate: number) => {
    let result;
    if (successRate >= 0.8) {
      result = { rating: 'Excellent', color: 'text-green-700', bgColor: 'bg-green-100' };
    } else if (successRate >= 0.6) {
      result = { rating: 'Good', color: 'text-blue-700', bgColor: 'bg-blue-100' };
    } else if (successRate >= 0.4) {
      result = { rating: 'Fair', color: 'text-yellow-700', bgColor: 'bg-yellow-100' };
    } else {
      result = { rating: 'Poor', color: 'text-red-700', bgColor: 'bg-red-100' };
    }
    return result;
  }), []);

  const formatPercentage = useCallback(DataProcessor.memoize((value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  }), []);



  const formatReturn = useCallback(DataProcessor.memoize((value: number): string => {
    const percentage = (value * 100).toFixed(2);
    return value >= 0 ? `+${percentage}%` : `${percentage}%`;
  }), []);

  const compactView = useMemo(() => {
    if (!compact || !effectiveness) return null;
    const rating = getEffectivenessRating(effectiveness.successRate);
    return (
      <div className="flex items-center gap-2">
        <div className={`px-2 py-1 rounded-full text-xs font-medium ${rating.color} ${rating.bgColor}`}>
          {rating.rating}
        </div>
        <span className="text-sm text-gray-600">
          {formatPercentage(effectiveness.successRate)} success
        </span>
        <span className={`text-sm font-medium ${
          effectiveness.avgReturn >= 0 ? 'text-green-600' : 'text-red-600'
        }`}>
          {formatReturn(effectiveness.avgReturn)}
        </span>
      </div>
    );
  }, [compact, effectiveness, getEffectivenessRating, formatPercentage, formatReturn]);

  if (compactView) {
    return compactView;
  }

  if (!effectiveness && !performanceMetrics) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 text-center">
        <BarChart3 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-500">No effectiveness data available</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Signal Effectiveness
          {signalType && (
            <span className="ml-2 text-sm font-normal text-gray-500">
              ({signalType} Signals)
            </span>
          )}
        </h3>
        {effectiveness && (
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            getEffectivenessRating(effectiveness.successRate).color
          } ${getEffectivenessRating(effectiveness.successRate).bgColor}`}>
            {getEffectivenessRating(effectiveness.successRate).rating}
          </div>
        )}
      </div>

      {effectiveness && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Target className="w-4 h-4 text-blue-500 mr-1" />
              <span className="text-sm font-medium text-gray-700">Success Rate</span>
            </div>
            <p className="text-2xl font-bold text-blue-600">
              {formatPercentage(effectiveness.successRate)}
            </p>
            <p className="text-xs text-gray-500">
              {effectiveness.successfulSignals}/{effectiveness.totalSignals} signals
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm font-medium text-gray-700">Avg Return</span>
            </div>
            <p className={`text-2xl font-bold ${
              effectiveness.avgReturn >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatReturn(effectiveness.avgReturn)}
            </p>
            <p className="text-xs text-gray-500">
              Total: {formatReturn(effectiveness.totalReturn)}
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              <span className="text-sm font-medium text-gray-700">Max Drawdown</span>
            </div>
            <p className="text-2xl font-bold text-red-600">
              {formatReturn(effectiveness.maxDrawdown)}
            </p>
            <p className="text-xs text-gray-500">
              Risk measure
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Clock className="w-4 h-4 text-purple-500 mr-1" />
              <span className="text-sm font-medium text-gray-700">Avg Hold</span>
            </div>
            <p className="text-2xl font-bold text-purple-600">
              {effectiveness.avgHoldingPeriod.toFixed(0)}
            </p>
            <p className="text-xs text-gray-500">days</p>
          </div>
        </div>
      )}

      {performanceMetrics && (
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-md font-medium text-gray-900 mb-3">Overall Performance</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600">Total Signals</p>
              <p className="text-lg font-semibold text-gray-900">
                {performanceMetrics.totalSignals}
              </p>
              <p className="text-xs text-gray-500">
                {performanceMetrics.buySignals} buy, {performanceMetrics.sellSignals} sell
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-600">Overall Success</p>
              <p className="text-lg font-semibold text-blue-600">
                {formatPercentage(performanceMetrics.overallSuccessRate)}
              </p>
              <p className="text-xs text-gray-500">
                Buy: {formatPercentage(performanceMetrics.buySuccessRate)}, 
                Sell: {formatPercentage(performanceMetrics.sellSuccessRate)}
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-600">Win/Loss Ratio</p>
              <p className="text-lg font-semibold text-green-600">
                {performanceMetrics.winLossRatio.toFixed(2)}
              </p>
              <p className="text-xs text-gray-500">
                Consecutive: +{performanceMetrics.maxConsecutiveWins}/-{performanceMetrics.maxConsecutiveLosses}
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-600">Total Return</p>
              <p className={`text-lg font-semibold ${
                performanceMetrics.totalReturn >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatReturn(performanceMetrics.totalReturn)}
              </p>
              <p className="text-xs text-gray-500">
                Avg per signal: {formatReturn(performanceMetrics.avgReturnPerSignal)}
              </p>
            </div>

            {performanceMetrics.sharpeRatio && (
              <div>
                <p className="text-sm text-gray-600">Sharpe Ratio</p>
                <p className="text-lg font-semibold text-purple-600">
                  {performanceMetrics.sharpeRatio.toFixed(2)}
                </p>
                <p className="text-xs text-gray-500">Risk-adjusted return</p>
              </div>
            )}

            <div>
              <p className="text-sm text-gray-600">Avg Holding</p>
              <p className="text-lg font-semibold text-gray-700">
                {performanceMetrics.avgHoldingPeriod.toFixed(0)} days
              </p>
            </div>
          </div>
        </div>
      )}

      {effectiveness && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center text-sm text-gray-500">
            <span>
              Period: {new Date(effectiveness.periodStart).toLocaleDateString()} - {new Date(effectiveness.periodEnd).toLocaleDateString()}
            </span>
            <span>
              Last updated: {new Date(effectiveness.lastCalculated).toLocaleDateString()}
            </span>
          </div>
        </div>
      )}
    </div>
  );
});

SignalEffectivenessIndicator.displayName = 'SignalEffectivenessIndicator';

export default withPerformanceTracking(SignalEffectivenessIndicator, 'SignalEffectivenessIndicator');