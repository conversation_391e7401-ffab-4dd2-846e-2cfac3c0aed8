import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { ErrorMessage } from '../ErrorMessage'

describe('ErrorMessage', () => {
  it('renders with required props', () => {
    render(<ErrorMessage message="Something went wrong" />)
    
    const alert = screen.getByRole('alert')
    expect(alert).toBeInTheDocument()
    expect(alert).toHaveAttribute('aria-live', 'polite')
    
    expect(screen.getByText('Error')).toBeInTheDocument()
    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
  })

  it('renders with custom title', () => {
    render(<ErrorMessage message="Network error" title="Connection Failed" />)
    
    expect(screen.getByText('Connection Failed')).toBeInTheDocument()
    expect(screen.getByText('Network error')).toBeInTheDocument()
  })

  it('renders retry button when onRetry is provided', () => {
    const mockRetry = vi.fn()
    render(<ErrorMessage message="Failed to load" onRetry={mockRetry} />)
    
    const retryButton = screen.getByRole('button', { name: 'Try Again' })
    expect(retryButton).toBeInTheDocument()
    
    fireEvent.click(retryButton)
    expect(mockRetry).toHaveBeenCalledOnce()
  })

  it('does not render retry button when onRetry is not provided', () => {
    render(<ErrorMessage message="Failed to load" />)
    
    expect(screen.queryByRole('button', { name: 'Try Again' })).not.toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<ErrorMessage message="Error" className="custom-class" />)
    
    const alert = screen.getByRole('alert')
    expect(alert).toHaveClass('custom-class')
  })

  it('has proper accessibility attributes', () => {
    render(<ErrorMessage message="Error message" />)
    
    const alert = screen.getByRole('alert')
    expect(alert).toHaveAttribute('role', 'alert')
    expect(alert).toHaveAttribute('aria-live', 'polite')
    
    // Check for icon with aria-hidden
    const icon = alert.querySelector('svg')
    expect(icon).toHaveAttribute('aria-hidden', 'true')
  })

  it('displays error icon', () => {
    render(<ErrorMessage message="Error" />)
    
    const icon = screen.getByRole('alert').querySelector('svg')
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass('text-destructive')
  })
})