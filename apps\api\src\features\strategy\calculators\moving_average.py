"""Moving Average calculator for trading signals."""

import logging
from typing import Dict, List, Optional

import pandas as pd

from src.shared.exceptions import InvalidParameterError

logger = logging.getLogger(__name__)


class MovingAverageCalculator:
    """Calculator for Moving Average trading signals.
    
    Supports both Simple Moving Average (SMA) and Exponential Moving Average (EMA).
    Generates signals based on price crossovers and moving average crossovers.
    """
    
    def __init__(self, short_period: int = 10, long_period: int = 20, ma_type: str = "SMA"):
        """Initialize Moving Average calculator.
        
        Args:
            short_period: Period for short-term moving average (default: 10)
            long_period: Period for long-term moving average (default: 20)
            ma_type: Type of moving average - "SMA" or "EMA" (default: "SMA")
        """
        if short_period < 1 or long_period < 1:
            raise InvalidParameterError("Moving average periods must be at least 1")
        if short_period >= long_period:
            raise InvalidParameterError("Short period must be less than long period")
        if ma_type not in ["SMA", "EMA"]:
            raise InvalidParameterError("MA type must be 'SMA' or 'EMA'")
            
        self.short_period = short_period
        self.long_period = long_period
        self.ma_type = ma_type
    
    def calculate_sma(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Simple Moving Average.
        
        Args:
            prices: Series of closing prices
            period: Period for SMA calculation
            
        Returns:
            Series of SMA values
        """
        return prices.rolling(window=period, min_periods=period).mean()
    
    def calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average.
        
        Args:
            prices: Series of closing prices
            period: Period for EMA calculation
            
        Returns:
            Series of EMA values
        """
        return prices.ewm(span=period, adjust=False).mean()
    
    def calculate_moving_averages(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """Calculate both short and long moving averages.
        
        Args:
            prices: Series of closing prices
            
        Returns:
            Dictionary containing short and long moving averages
            
        Raises:
            InvalidParameterError: If insufficient data points
        """
        if len(prices) < self.long_period:
            raise InvalidParameterError(
                f"Need at least {self.long_period} data points for MA calculation, got {len(prices)}"
            )
        
        if self.ma_type == "SMA":
            short_ma = self.calculate_sma(prices, self.short_period)
            long_ma = self.calculate_sma(prices, self.long_period)
        else:  # EMA
            short_ma = self.calculate_ema(prices, self.short_period)
            long_ma = self.calculate_ema(prices, self.long_period)
        
        return {
            'short_ma': short_ma,
            'long_ma': long_ma
        }
    
    def generate_signals(self, data: pd.DataFrame) -> List[Dict]:
        """Generate Moving Average-based trading signals.
        
        Args:
            data: DataFrame with 'close' column and datetime index
            
        Returns:
            List of signal dictionaries with timestamp, signal type, and metadata
            
        Raises:
            InvalidParameterError: If data format is invalid or insufficient
        """
        if 'close' not in data.columns:
            raise InvalidParameterError("Data must contain 'close' column")
        
        if len(data) < self.long_period:
            raise InvalidParameterError(
                f"Need at least {self.long_period} data points for MA signals, got {len(data)}"
            )
        
        try:
            # Calculate moving averages
            ma_data = self.calculate_moving_averages(data['close'])
            short_ma = ma_data['short_ma']
            long_ma = ma_data['long_ma']
            
            signals = []
            
            for i in range(1, len(data)):
                current_short = short_ma.iloc[i]
                current_long = long_ma.iloc[i]
                previous_short = short_ma.iloc[i-1]
                previous_long = long_ma.iloc[i-1]
                current_price = data['close'].iloc[i]
                
                # Skip if MA values are NaN
                if pd.isna(current_short) or pd.isna(current_long) or pd.isna(previous_short) or pd.isna(previous_long):
                    continue
                
                signal_type = None
                confidence = 0.0
                signal_reason = ""
                
                # Golden Cross: Short MA crosses above Long MA (Bullish)
                if previous_short <= previous_long and current_short > current_long:
                    signal_type = "BUY"
                    signal_reason = "Golden Cross"
                    # Calculate confidence based on the strength of the crossover
                    crossover_strength = (current_short - current_long) / current_long
                    confidence = min(0.9, 0.6 + abs(crossover_strength) * 10)
                
                # Death Cross: Short MA crosses below Long MA (Bearish)
                elif previous_short >= previous_long and current_short < current_long:
                    signal_type = "SELL"
                    signal_reason = "Death Cross"
                    # Calculate confidence based on the strength of the crossover
                    crossover_strength = (current_long - current_short) / current_long
                    confidence = min(0.9, 0.6 + abs(crossover_strength) * 10)
                
                # Price crossover signals (secondary signals)
                elif pd.notna(current_short):
                    # Price crosses above short MA (Bullish)
                    if data['close'].iloc[i-1] <= short_ma.iloc[i-1] and current_price > current_short:
                        signal_type = "BUY"
                        signal_reason = "Price above Short MA"
                        confidence = 0.4
                    
                    # Price crosses below short MA (Bearish)
                    elif data['close'].iloc[i-1] >= short_ma.iloc[i-1] and current_price < current_short:
                        signal_type = "SELL"
                        signal_reason = "Price below Short MA"
                        confidence = 0.4
                
                if signal_type:
                    signals.append({
                        'timestamp': data.index[i],
                        'signal_type': signal_type,
                        'strategy': 'Moving Average',
                        'confidence': confidence,
                        'price': current_price,
                        'short_ma': current_short,
                        'long_ma': current_long,
                        'signal_reason': signal_reason,
                        'metadata': {
                            'short_period': self.short_period,
                            'long_period': self.long_period,
                            'ma_type': self.ma_type,
                            'short_ma_current': float(current_short),
                            'long_ma_current': float(current_long),
                            'short_ma_previous': float(previous_short),
                            'long_ma_previous': float(previous_long),
                            'signal_reason': signal_reason
                        }
                    })
            
            logger.info(f"Generated {len(signals)} Moving Average signals")
            return signals
            
        except Exception as e:
            logger.error(f"Error generating Moving Average signals: {str(e)}")
            raise InvalidParameterError(f"Failed to generate Moving Average signals: {str(e)}")
    
    def get_current_values(self, data: pd.DataFrame) -> Optional[Dict]:
        """Get current moving average values.
        
        Args:
            data: DataFrame with 'close' column
            
        Returns:
            Dictionary with current MA values or None if insufficient data
        """
        try:
            if len(data) < self.long_period:
                return None
            
            ma_data = self.calculate_moving_averages(data['close'])
            short_ma = ma_data['short_ma'].iloc[-1]
            long_ma = ma_data['long_ma'].iloc[-1]
            current_price = data['close'].iloc[-1]
            
            if pd.isna(short_ma) or pd.isna(long_ma):
                return None
            
            return {
                'short_ma': float(short_ma),
                'long_ma': float(long_ma),
                'current_price': float(current_price),
                'short_above_long': short_ma > long_ma,
                'price_above_short': current_price > short_ma,
                'price_above_long': current_price > long_ma
            }
            
        except Exception as e:
            logger.error(f"Error calculating current MA values: {str(e)}")
            return None
    
    def get_trend_direction(self, data: pd.DataFrame) -> str:
        """Determine trend direction based on moving averages.
        
        Args:
            data: DataFrame with 'close' column
            
        Returns:
            Trend direction: 'Bullish', 'Bearish', or 'Neutral'
        """
        current_values = self.get_current_values(data)
        
        if not current_values:
            return "Unknown"
        
        short_ma = current_values['short_ma']
        long_ma = current_values['long_ma']
        current_price = current_values['current_price']
        
        # Strong bullish: Price > Short MA > Long MA
        if current_price > short_ma > long_ma:
            return "Strong Bullish"
        
        # Bullish: Short MA > Long MA
        elif short_ma > long_ma:
            return "Bullish"
        
        # Strong bearish: Price < Short MA < Long MA
        elif current_price < short_ma < long_ma:
            return "Strong Bearish"
        
        # Bearish: Short MA < Long MA
        elif short_ma < long_ma:
            return "Bearish"
        
        else:
            return "Neutral"