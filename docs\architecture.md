# Comprehensive Trading Agent Web Application Fullstack Architecture Document

### 1. Introduction

This document outlines the complete fullstack architecture for the **Comprehensive Trading Agent Web Application**, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

#### Starter Template or Existing Project
* **N/A - Greenfield project**. While the PRD mentions using standard starter kits (like `create-react-app`) for initialization, the architecture is being designed from first principles without a pre-packaged full-stack template.

#### Change Log

| Date            | Version | Description                 | Author              |
| :-------------- | :------ | :-------------------------- | :------------------ |
| August 17, 2025 | 1.0     | Initial architecture draft. | Winston (Architect) |

---

### 2. High Level Architecture

#### Technical Summary
The application will be architected as a modern **Jamstack** application. It will consist of a responsive Single-Page Application (SPA) frontend and a pragmatic, monolithic **Python backend API**. The backend's sole responsibility is to ingest data from external sources, execute the core trading strategy, and expose the results via a simple REST API. This lean, decoupled approach is designed for rapid development and validation of the MVP, with clear paths for future scaling.

#### Platform and Infrastructure Choice
* **Recommendation:** **Vercel** for the frontend and **Render** for the Python backend.
* **Rationale:** Vercel provides best-in-class performance and continuous deployment for modern web frontends. Render offers a simple, cost-effective way to deploy a Python web service with a persistent disk for the SQLite database. This combination is fast to set up, has generous free tiers, and avoids the significant operational overhead of a full cloud provider like AWS for an MVP.

#### Repository Structure
* **Structure:** A **Monorepo** managed with **npm workspaces**.
* **Rationale:** As decided in the PRD, this will keep the frontend and backend in a single repository, simplifying dependency management and making it easier to share code, such as data types.

#### High Level Architecture Diagram
```mermaid
graph TD
    User --> Browser[Browser: Frontend SPA on Vercel];
    Browser -->|REST API Call| Backend[Python Backend on Render];
    Backend -->|Fetches Data| Akshare[akshare Library];
    Backend -->|Reads/Writes| DB[(SQLite Database)];
    Akshare --> Backend;
    DB --> Backend;
````

#### Architectural Patterns

  * **Jamstack Architecture:** The frontend will be a static or server-rendered application hosted on a global CDN (Vercel), interacting with the backend via APIs. **Rationale:** This pattern provides excellent performance, security, and scalability for the client application.
  * **Component-Based UI:** The frontend will be built using reusable components (e.g., in React or Vue). **Rationale:** This ensures a maintainable, consistent, and scalable frontend codebase.
  * **Repository Pattern (Backend):** A data access layer will abstract the database logic. **Rationale:** Although we are starting with SQLite, this pattern will make the planned future migration to PostgreSQL seamless with minimal code changes.
  * **REST API:** A standard RESTful interface will be used for all frontend-backend communication. **Rationale:** It is a simple, well-understood, and stateless communication pattern that is perfectly sufficient for the application's needs.

-----

### 3\. Tech Stack

#### Technology Stack Table

| Category               | Technology              | Version | Purpose                            | Rationale                                        |
| :--------------------- | :---------------------- | :------ | :--------------------------------- | :----------------------------------------------- |
| **Frontend Language** | TypeScript              | 5.4+    | Primary frontend language          | Ensures type safety and maintainability.         |
| **Frontend Framework** | React                   | 18+     | UI development library             | Large ecosystem, excellent for data-heavy apps.  |
| **UI Component Lib** | Radix UI                | Latest  | Unstyled, accessible components    | Provides accessibility primitives without styling. |
| **State Management** | Zustand                 | Latest  | Simple state management            | Minimal boilerplate, easy to use for MVP scope.  |
| **Backend Language** | Python                  | 3.11+   | Primary backend language           | Required for `akshare` and `Tushare` libraries.  |
| **Backend Framework** | FastAPI                 | Latest  | Web framework for APIs             | High performance, auto-generates API docs.       |
| **API Style** | REST                    | N/A     | Frontend-backend communication     | Simple, stateless, and well-understood pattern.  |
| **Database** | SQLite                  | 3+      | MVP database                       | File-based, zero-config for simplicity in MVP.   |
| **Frontend Testing** | Vitest & RTL            | Latest  | Unit/Integration testing           | Modern, fast test runner for Vite environments.  |
| **Backend Testing** | Pytest                  | Latest  | Unit/Integration testing           | De-facto standard for testing in Python.         |
| **E2E Testing** | Playwright              | Latest  | End-to-end browser testing         | Robust, modern, and excellent cross-browser support. |
| **Build Tool/Bundler** | Vite                    | Latest  | Frontend dev server & bundler      | Extremely fast performance and developer experience. |
| **CI/CD** | GitHub Actions          | N/A     | Automation and deployment          | Tightly integrated with GitHub, excellent free tier. |
| **CSS Framework** | Tailwind CSS            | Latest  | Utility-first styling              | Accelerates styling and works perfectly with Radix. |

-----

### 4\. Data Models

#### StockData

  * **Purpose:** To hold the raw, time-series historical data for a single stock, fetched from the `akshare` data source.
  * **TypeScript Interface**
    ```typescript
    interface DailyPrice {
      date: string;
      open: number;
      high: number;
      low: number;
      close: number;
      volume: number;
    }

    interface StockData {
      symbol: string;
      dailyPrices: DailyPrice[];
    }
    ```

#### SignalResult

  * **Purpose:** To encapsulate the complete, processed analysis result for a single stock.
  * **TypeScript Interface**
    ```typescript
    type Signal = 'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL';

    interface SignalResult {
      symbol: string;
      lastScanDate: string;
      signal: Signal;
      chartData: {
        dailyPrices: DailyPrice[];
        magicNineSequence: (number | null)[];
        macdLine: (number | null)[];
        signalLine: (number | null)[];
        divergencePoints: { date: string; type: 'TOP' }[];
      }
    }
    ```

-----

### 5\. API Specification

#### REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Trading Agent API
  version: 1.0.0
  description: API for the trading signal analysis and stock screening application.
servers:
  - url: http://localhost:8000
    description: Local Development Server
paths:
  /api/signal/{stock_code}:
    get:
      summary: Get a trading signal for a single stock
      parameters:
        - name: stock_code
          in: path
          required: true
          schema:
            type: string
          description: The stock code to analyze (e.g., 000001.SZ)
      responses:
        '200':
          description: Successful analysis result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SignalResult'
        '404':
          description: Stock code not found or data unavailable
  /api/screener/results:
    get:
      summary: Get the latest list of stocks flagged by the screener
      responses:
        '200':
          description: A list of stocks that meet the signal criteria
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ScreenerItem'
components:
  schemas:
    DailyPrice:
      type: object
      properties:
        date: { type: string, format: date }
        open: { type: number }
        high: { type: number }
        low: { type: number }
        close: { type: number }
        volume: { type: integer }
    SignalResult:
      type: object
      properties:
        symbol: { type: string }
        lastScanDate: { type: string, format: date-time }
        signal: { type: string, enum: [SELL_CANDIDATE, HOLD, NO_SIGNAL] }
        chartData:
          type: object
          properties:
            dailyPrices:
              type: array
              items: { $ref: '#/components/schemas/DailyPrice' }
            magicNineSequence:
              type: array
              items: { type: integer, nullable: true }
            macdLine:
              type: array
              items: { type: number, nullable: true }
            signalLine:
              type: array
              items: { type: number, nullable: true }
            divergencePoints:
              type: array
              items:
                type: object
                properties:
                  date: { type: string, format: date }
                  type: { type: string, enum: [TOP] }
    ScreenerItem:
      type: object
      properties:
        symbol: { type: string }
        companyName: { type: string }
```

-----

### 6\. Components

#### Component List

  * **Backend Components:**
      * **Web Server (FastAPI):** Handles HTTP requests and routing.
      * **Signal Service:** Core business logic orchestrator.
      * **Data Ingestion Service:** Fetches and parses data from `akshare`.
      * **Strategy Engine:** Pure calculation module for the indicators.
  * **Frontend Components:**
      * **API Client Service:** Manages all communication with the backend.
      * **State Management Store (Zustand):** Holds global UI state.
      * **View Components (React):** Renders the main application pages.
      * **UI Component Library (Radix + Tailwind):** Provides reusable UI building blocks.

#### Component Diagram

```mermaid
graph TD
    subgraph Frontend (Browser)
        View[View Components] --> APIClient[API Client Service];
        View --> Store[State Management Store];
        APIClient --> Store;
    end
    subgraph Backend (Render)
        WebServer[Web Server] --> SignalSvc[Signal Service];
        SignalSvc --> DataSvc[Data Ingestion Service];
        SignalSvc --> Engine[Strategy Engine];
    end
    APIClient -->|REST API| WebServer;
    DataSvc -->|fetches| Akshare[External: akshare];
```

-----

### 7\. External APIs

#### `akshare` Data Library

  * **Purpose:** The **primary source** for fetching Chinese stock market data.
  * **Authentication:** None required.
  * **Integration Notes:** Will be called directly from the backend **Data Ingestion Service**.

#### `Tushare` Data API

  * **Purpose:** The **secondary/backup data source**, planned for post-MVP.
  * **Authentication:** **API Token** is required and must be stored securely.

-----

### 8\. Core Workflows

#### Workflow 1: Single Stock Analysis

```mermaid
sequenceDiagram
    participant User
    participant Frontend SPA
    participant Backend API
    participant Akshare
    User->>+Frontend SPA: Enters stock code & clicks "Get Signal"
    Frontend SPA->>+Backend API: GET /api/signal/{stock_code}
    Backend API->>+Akshare: Get historical data()
    Akshare-->>-Backend API: Return stock data
    Backend API-->>-Frontend SPA: 200 OK with SignalResult JSON
    Frontend SPA-->>-User: Display signal and chart
```

#### Workflow 2: Displaying Screener Results

```mermaid
sequenceDiagram
    participant User
    participant Frontend SPA
    participant Backend API
    participant SQLite DB
    User->>+Frontend SPA: Navigates to Screener page
    Frontend SPA->>+Backend API: GET /api/screener/results
    Backend API->>+SQLite DB: Get latest screener results()
    SQLite DB-->>-Backend API: Return cached results
    Backend API-->>-Frontend SPA: 200 OK with list of stocks
    Frontend SPA-->>-User: Display list of flagged stocks
```

-----

### 9\. Database Schema

```sql
-- Table to store the results of each daily screener run
CREATE TABLE screener_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scan_timestamp TEXT NOT NULL, -- ISO 8601 format timestamp
    symbol TEXT NOT NULL,
    company_name TEXT
);

-- Index to quickly retrieve the results from the latest scan
CREATE INDEX idx_scan_timestamp ON screener_results (scan_timestamp);
```

-----

### 10\. Frontend Architecture

  * **Component Organization:** A feature-based directory structure will be used (e.g., `/src/features/Analysis/`).
  * **State Management:** Zustand will be used for simple global state. Local component state will be used for UI-specific state.
  * **Routing:** `react-router-dom` will handle client-side routing between the `Analysis` and `Screener` views.
  * **Services:** A dedicated API client service layer will handle all backend communication.

-----

### 11\. Backend Architecture

  * **Service Architecture:** A feature-based structure will be used for FastAPI routers and services (e.g., `/src/features/signal/router.py`).
  * **Data Access:** The Repository Pattern will be used to abstract all database logic, simplifying the future migration from SQLite.
  * **Authentication:** Not applicable for the MVP.

-----

### 12\. Unified Project Structure

A monorepo structure managed with `npm workspaces` and `Turborepo` will be used to house the `apps/web` (frontend) and `apps/api` (backend) applications, along with a `packages/shared-types` directory.

-----

### 13\. Development Workflow

  * **Prerequisites:** Node.js v18+, Python 3.11+, Git.
  * **Setup:** `npm install` at the root will set up the entire project.
  * **Commands:** `npm run dev` will start both services. `npm test` will run all tests.
  * **Environment Variables:** Frontend (`.env.local`) will require `VITE_API_BASE_URL`. Backend (`.env`) will require `TUSHARE_API_TOKEN` post-MVP.

-----

### 14\. Deployment Architecture

  * **Frontend:** Deployed on **Vercel**, leveraging its global edge network.
  * **Backend:** Deployed as a web service on **Render**.
  * **CI/CD:** **GitHub Actions** will be used to run linting and tests on every push, and to trigger deployments.

-----

### 15\. Security and Performance

  * **Security:** A strict Content Security Policy (CSP), automated dependency scanning, backend input validation with Pydantic, and a restrictive CORS policy will be implemented.
  * **Performance:** Code-splitting, asset caching via Vercel, client-side data caching, and asynchronous backend jobs will be used to ensure a fast user experience.

-----

### 16\. Testing Strategy

The "Testing Pyramid" model will be followed, with a large base of unit tests (Vitest/RTL for frontend, Pytest for backend), a smaller set of integration tests, and a few critical end-to-end tests using Playwright.

-----

### 17\. Coding Standards

  * **Critical Rules:** Enforce the use of shared types, the API service layer, the repository pattern, and proper state management.
  * **Naming Conventions:** Use `PascalCase` for React components, `kebab-case` for API routes, and `snake_case` for database tables.

-----

### 18\. Error Handling Strategy

A unified strategy will be used, featuring a standardized JSON error format from the backend, a global exception handler in FastAPI, and a global error interceptor in the frontend API client.

-----

### 19\. Monitoring and Observability

  * **Stack:** Vercel Analytics for the frontend, Render's built-in metrics for the backend, and Sentry for real-time error tracking across the full stack.

-----

### 20\. Checklist Results Report

#### Architecture Validation Summary

  * **Overall Readiness:** **High**. The architecture is comprehensive, pragmatic, and directly aligned with all requirements.
  * **Critical Risks:** **None**. No critical architectural flaws or blockers were identified.
  * **Key Strengths:** The design features a clear separation of concerns, pragmatic technology choices that prioritize MVP velocity, and a high degree of modularity, making it exceptionally well-suited for AI-driven development.

<!-- end list -->
