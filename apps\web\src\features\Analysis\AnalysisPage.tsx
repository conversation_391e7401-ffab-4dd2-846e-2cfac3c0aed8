import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { LoadingState } from '@/components/ui/LoadingState'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { StockInputForm } from './StockInputForm'
import { SignalDisplay } from './SignalDisplay'
import { InteractiveChart } from './InteractiveChart'
import { SignalControls } from '@/components/Chart/SignalControls'
import { useSignalComputationStore } from '@/stores/signalComputationStore'
import { apiClient, ApiError } from '@/lib/api'
import type { SignalResult } from '@trading-agent/shared-types'

export function AnalysisPage() {
  const { t } = useTranslation('analysis')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [result, setResult] = useState<SignalResult | null>(null)
  const [searchParams, setSearchParams] = useSearchParams()
  const [initialStock, setInitialStock] = useState<string | null>(null)
  
  // Signal computation store
  const {
    signals,
    filteredSignals,
    effectivenessMetrics,
    loading: signalsLoading,
    error: signalsError,
    filterOptions,
    displayOptions,
    computeSignals,
    updateFilterOptions,
    updateDisplayOptions,
    clearError: clearSignalsError
  } = useSignalComputationStore()

  // Handle URL parameters for pre-populated stock code
  useEffect(() => {
    const stockParam = searchParams.get('stock')
    if (stockParam) {
      setInitialStock(stockParam)
      // Auto-analyze if stock code is provided via URL
      handleAnalyze(stockParam)
      // Clear the URL parameter after processing
      setSearchParams({})
    }
  }, [searchParams, setSearchParams])

  const handleAnalyze = async (stockCode: string) => {
    setLoading(true)
    setError(null)
    setResult(null)
    clearSignalsError()
    
    try {
      // Get basic stock data first
      const signalResult = await apiClient.getSignalForStock(stockCode)
      setResult(signalResult)
      
      // Then compute real-time signals
      await computeSignals({
        symbol: stockCode,
        strategies: ['rsi', 'macd', 'moving_average', 'bollinger_bands'],
        start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Last 30 days
        end_date: new Date().toISOString().split('T')[0]
      })
    } catch (err) {
      if (err instanceof ApiError) {
        // Handle specific API errors with user-friendly messages
        if (err.status === 404) {
          setError(t('errors.invalidSymbol'))
        } else if (err.status === 0) {
          setError(err.message) // Network errors already have user-friendly messages
        } else {
          setError(err.message || t('errors.fetchFailed'))
        }
      } else {
        setError(t('errors.fetchFailed'))
      }
    } finally {
      setLoading(false)
    }
  }

  const handleRetry = () => {
    setError(null)
    clearSignalsError()
  }

  // Signal control handlers
  const handleFilterTypeChange = (type: 'buy' | 'sell' | 'all') => {
    updateFilterOptions({
      ...filterOptions,
      signalTypes: type === 'all' ? ['buy', 'sell'] : [type]
    })
  }

  const handleMinConfidenceChange = (confidence: number) => {
    updateFilterOptions({
      ...filterOptions,
      minConfidence: confidence,
      maxConfidence: filterOptions.maxConfidence || 1.0
    })
  }

  const handleShowTooltipsChange = (show: boolean) => {
    updateDisplayOptions({
      ...displayOptions,
      showTooltips: show
    })
  }

  const handleStrategyToggle = (strategy: string) => {
    const currentStrategies = filterOptions.strategies || []
    const newStrategies = currentStrategies.includes(strategy)
      ? currentStrategies.filter(s => s !== strategy)
      : [...currentStrategies, strategy]
    
    updateFilterOptions({
      ...filterOptions,
      strategies: newStrategies
    })
  }

  // Calculate signal counts for controls
  const getSignalCounts = () => {
    const buySignals = signals.filter(s => s.signal_type === 'buy')
    const sellSignals = signals.filter(s => s.signal_type === 'sell')
    
    return {
      total: signals.length,
      buy: buySignals.length,
      sell: sellSignals.length,
      filtered: filteredSignals.length
    }
  }

  // Get unique strategies from signals
  const getAvailableStrategies = () => {
    return [...new Set(signals.map(s => s.strategy))]
  }

  const renderResults = () => {
    if (loading || signalsLoading) {
      return (
        <LoadingState 
          message={t('loading.fetchingData')} 
          className="justify-center py-8"
        />
      )
    }

    if (error || signalsError) {
      return (
        <ErrorMessage 
          message={error || signalsError || t('errors.fetchFailed')}
          title={t('errors.fetchFailed')}
          onRetry={handleRetry}
        />
      )
    }

    if (result) {
      return (
        <div className="space-y-6">
          {/* Signal Controls */}
          {signals.length > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-1">
                <SignalControls
                  filterByType={filterOptions.signalTypes?.length === 1 ? filterOptions.signalTypes[0] as 'buy' | 'sell' : 'all'}
                  onFilterTypeChange={handleFilterTypeChange}
                  minConfidence={filterOptions.minConfidence || 0}
                  onMinConfidenceChange={handleMinConfidenceChange}
                  showTooltips={displayOptions.showTooltips || false}
                  onShowTooltipsChange={handleShowTooltipsChange}
                  signalCount={getSignalCounts()}
                  strategies={getAvailableStrategies()}
                  selectedStrategies={filterOptions.strategies || []}
                  onStrategyToggle={handleStrategyToggle}
                />
              </div>
              <div className="lg:col-span-3">
                <SignalDisplay 
                  result={result} 
                  signals={filteredSignals}
                  effectivenessMetrics={effectivenessMetrics || undefined}
                />
              </div>
            </div>
          )}
          
          {/* Chart */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('chart.title')}</h3>
            {/* Debug logging for signals */}
            {console.log('AnalysisPage - Raw signals:', signals)}
            {console.log('AnalysisPage - Filtered signals:', filteredSignals)}
            {console.log('AnalysisPage - Filter options:', filterOptions)}
            <InteractiveChart 
              result={result} 
              signals={filteredSignals}
            />
          </div>
        </div>
      )
    }

    return (
      <p className="text-muted-foreground text-center py-8">
        {t('stockInput.placeholder')}
      </p>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">{t('title')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('stockInput.placeholder')}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('stockInput.submit')}</CardTitle>
        </CardHeader>
        <CardContent>
          <StockInputForm 
            onSubmit={handleAnalyze}
            loading={loading}
            disabled={loading}
            initialValue={initialStock || undefined}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t('signals.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          {renderResults()}
        </CardContent>
      </Card>
    </div>
  )
}