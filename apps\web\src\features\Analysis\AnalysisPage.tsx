import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { LoadingState } from '@/components/ui/LoadingState'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { StockInputForm } from './StockInputForm'
import { SignalDisplay } from './SignalDisplay'
import { InteractiveChart } from './InteractiveChart'
import { apiClient, ApiError } from '@/lib/api'
import type { SignalResult } from '@trading-agent/shared-types'

export function AnalysisPage() {
  const { t } = useTranslation('analysis')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [result, setResult] = useState<SignalResult | null>(null)
  const [searchParams, setSearchParams] = useSearchParams()
  const [initialStock, setInitialStock] = useState<string | null>(null)

  // Handle URL parameters for pre-populated stock code
  useEffect(() => {
    const stockParam = searchParams.get('stock')
    if (stockParam) {
      setInitialStock(stockParam)
      // Auto-analyze if stock code is provided via URL
      handleAnalyze(stockParam)
      // Clear the URL parameter after processing
      setSearchParams({})
    }
  }, [searchParams, setSearchParams])

  const handleAnalyze = async (stockCode: string) => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      const signalResult = await apiClient.getSignalForStock(stockCode)
      setResult(signalResult)
    } catch (err) {
      if (err instanceof ApiError) {
        // Handle specific API errors with user-friendly messages
        if (err.status === 404) {
          setError(t('errors.invalidSymbol'))
        } else if (err.status === 0) {
          setError(err.message) // Network errors already have user-friendly messages
        } else {
          setError(err.message || t('errors.fetchFailed'))
        }
      } else {
        setError(t('errors.fetchFailed'))
      }
    } finally {
      setLoading(false)
    }
  }

  const handleRetry = () => {
    setError(null)
  }

  const renderResults = () => {
    if (loading) {
      return (
        <LoadingState 
          message={t('loading.fetchingData')} 
          className="justify-center py-8"
        />
      )
    }

    if (error) {
      return (
        <ErrorMessage 
          message={error}
          title={t('errors.fetchFailed')}
          onRetry={handleRetry}
        />
      )
    }

    if (result) {
      return (
        <div className="space-y-6">
          <SignalDisplay result={result} />
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('chart.title')}</h3>
            <InteractiveChart result={result} />
          </div>
        </div>
      )
    }

    return (
      <p className="text-muted-foreground text-center py-8">
        {t('stockInput.placeholder')}
      </p>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">{t('title')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('stockInput.placeholder')}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('stockInput.submit')}</CardTitle>
        </CardHeader>
        <CardContent>
          <StockInputForm 
            onSubmit={handleAnalyze}
            loading={loading}
            disabled={loading}
            initialValue={initialStock || undefined}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t('signals.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          {renderResults()}
        </CardContent>
      </Card>
    </div>
  )
}