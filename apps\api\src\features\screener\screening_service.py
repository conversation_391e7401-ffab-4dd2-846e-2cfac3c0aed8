"""Screening service for batch processing stock signals."""

import asyncio
import logging
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any

from ...models.strategy import SignalResult
from ..signal.service import SignalService
from .repository import ScreenerRepository

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry for stock signal results."""
    signal_result: SignalResult | None
    timestamp: datetime
    is_sell_candidate: bool


class ScreeningService:
    """Service for batch processing stock screening operations."""

    def __init__(self, max_concurrent: int = 5, rate_limit_delay: float = 0.1, cache_ttl_hours: int = 4):
        """
        Initialize screening service.

        Args:
            max_concurrent: Maximum concurrent signal processing operations
            rate_limit_delay: Delay between requests to respect API limits (seconds)
            cache_ttl_hours: Time to live for cache entries in hours (default: 4)
        """
        self.signal_service = SignalService()
        self.max_concurrent = max_concurrent
        self.rate_limit_delay = rate_limit_delay
        self.cache_ttl_hours = cache_ttl_hours
        self._semaphore = asyncio.Semaphore(max_concurrent)
        self._cache: dict[str, CacheEntry] = {}  # Simple in-memory cache

    def _is_cache_valid(self, cache_entry: CacheEntry) -> bool:
        """Check if cache entry is still valid."""
        age = datetime.now() - cache_entry.timestamp
        return age < timedelta(hours=self.cache_ttl_hours)

    def _get_from_cache(self, stock_code: str) -> CacheEntry | None:
        """Get stock signal from cache if valid."""
        if stock_code in self._cache:
            entry = self._cache[stock_code]
            if self._is_cache_valid(entry):
                return entry
            else:
                # Remove expired entry
                del self._cache[stock_code]
        return None

    def _cache_result(self, stock_code: str, signal_result: SignalResult | None, is_sell_candidate: bool) -> None:
        """Cache the signal result for a stock."""
        self._cache[stock_code] = CacheEntry(
            signal_result=signal_result,
            timestamp=datetime.now(),
            is_sell_candidate=is_sell_candidate
        )

    def clear_cache(self) -> None:
        """Clear all cached results."""
        self._cache.clear()
        logger.info("Cache cleared")

    def get_cache_stats(self) -> dict[str, int]:
        """Get cache statistics."""
        total_entries = len(self._cache)
        valid_entries = sum(1 for entry in self._cache.values() if self._is_cache_valid(entry))
        return {
            'total_entries': total_entries,
            'valid_entries': valid_entries,
            'expired_entries': total_entries - valid_entries
        }

    def get_csi_300_stock_list(self) -> list[str]:
        """
        Get CSI 300 stock codes list.

        For MVP, returns a sample list. In production, this would fetch
        from akshare or a maintained configuration.

        Returns:
            List of CSI 300 stock codes
        """
        # Sample CSI 300 stocks for MVP implementation
        # In production, this would be fetched from akshare or config
        sample_stocks = [
            "000001.SZ",  # Ping An Bank
            "000002.SZ",  # China Vanke
            "000858.SZ",  # Wuliangye
            "000895.SZ",  # Shuanghui Development
            "600000.SH",  # Pudong Development Bank
            "600036.SH",  # China Merchants Bank
            "600519.SH",  # Kweichow Moutai
            "600887.SH",  # Inner Mongolia Yili
            "000001.SS",  # Shanghai Stock Exchange Composite Index
            "000300.SS",  # CSI 300 Index
            # Add more representative stocks for testing
            "600276.SH",  # Hengrui Medicine
            "000963.SZ",  # Huadong Medicine
            "002415.SZ",  # Hikvision
            "000858.SZ",  # Wuliangye Yibin
            "002594.SZ",  # BYD Company
        ]

        logger.info(f"Retrieved {len(sample_stocks)} stock codes for screening")
        return sample_stocks

    async def _process_single_stock(self, stock_code: str) -> dict[str, str] | None:
        """
        Process a single stock for signal analysis.

        Args:
            stock_code: Stock symbol to analyze

        Returns:
            Dict with symbol and companyName if signal is SELL_CANDIDATE, None otherwise
        """
        async with self._semaphore:
            try:
                # Check cache first
                cached_entry = self._get_from_cache(stock_code)
                if cached_entry:
                    logger.debug(f"Using cached result for {stock_code}")
                    if cached_entry.is_sell_candidate:
                        return {
                            'symbol': stock_code,
                            'companyName': self._get_company_name(stock_code)
                        }
                    else:
                        return None

                # Add rate limiting delay
                if self.rate_limit_delay > 0:
                    await asyncio.sleep(self.rate_limit_delay)

                # Get signal analysis (this is synchronous, but we run it in executor)
                loop = asyncio.get_event_loop()
                signal_result = await loop.run_in_executor(
                    None,
                    self.signal_service.get_signal_for_stock,
                    stock_code
                )

                # Check if signal indicates a sell candidate
                is_sell_candidate = signal_result.signal == 'SELL_CANDIDATE'

                # Cache the result
                self._cache_result(stock_code, signal_result, is_sell_candidate)

                if is_sell_candidate:
                    logger.info(f"Found SELL_CANDIDATE signal for {stock_code}")
                    return {
                        'symbol': stock_code,
                        'companyName': self._get_company_name(stock_code)
                    }
                else:
                    logger.debug(f"No signal for {stock_code}: {signal_result.signal}")
                    return None

            except ValueError as e:
                # Invalid stock data or parsing errors - log but continue
                logger.warning(f"Data validation error for {stock_code}: {str(e)}")
                # Cache the error result to avoid repeated failures
                self._cache_result(stock_code, None, False)
                return None

            except RuntimeError as e:
                # External service errors or processing failures - log but continue
                logger.warning(f"Processing error for {stock_code}: {str(e)}")
                # Cache the error result to avoid repeated failures
                self._cache_result(stock_code, None, False)
                return None

            except Exception as e:
                # Unexpected errors - log but don't crash the batch
                logger.error(f"Unexpected error processing {stock_code}: {str(e)}")
                # Cache the error result to avoid repeated failures
                self._cache_result(stock_code, None, False)
                return None

    def _get_company_name(self, stock_code: str) -> str:
        """
        Get company name for stock code.

        For MVP, returns a simple mapping. In production, this would
        fetch from akshare or a company database.

        Args:
            stock_code: Stock symbol

        Returns:
            Company name or stock code if not found
        """
        # Simple mapping for MVP
        company_names = {
            "000001.SZ": "Ping An Bank Co., Ltd.",
            "000002.SZ": "China Vanke Co., Ltd.",
            "000858.SZ": "Wuliangye Yibin Co., Ltd.",
            "000895.SZ": "Shuanghui Development Co., Ltd.",
            "600000.SH": "Pudong Development Bank Co., Ltd.",
            "600036.SH": "China Merchants Bank Co., Ltd.",
            "600519.SH": "Kweichow Moutai Co., Ltd.",
            "600887.SH": "Inner Mongolia Yili Industrial Group Co., Ltd.",
            "600276.SH": "Jiangsu Hengrui Medicine Co., Ltd.",
            "000963.SZ": "Huadong Medicine Co., Ltd.",
            "002415.SZ": "Hangzhou Hikvision Digital Technology Co., Ltd.",
            "002594.SZ": "BYD Company Limited",
        }

        return company_names.get(stock_code, stock_code)

    async def run_screening(self, stock_list: list[str] | None = None) -> dict[str, Any]:
        """
        Run screening analysis on a list of stocks.

        Args:
            stock_list: Optional list of stock codes. If None, uses CSI 300 list.

        Returns:
            Dict with screening results and metadata
        """
        start_time = time.time()
        scan_timestamp = datetime.now().isoformat()

        # Get stock list to process
        if stock_list is None:
            stock_list = self.get_csi_300_stock_list()

        logger.info(f"Starting screening analysis for {len(stock_list)} stocks")

        try:
            # Process all stocks concurrently
            tasks = [self._process_single_stock(stock_code) for stock_code in stock_list]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Filter out None results and count errors
            valid_results: list[dict[str, str]] = []
            error_count = 0

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Exception processing {stock_list[i]}: {str(result)}")
                    error_count += 1
                elif result is not None and isinstance(result, dict):
                    # Type narrowed: result is dict[str, str] at this point
                    valid_results.append(result)
                # Note: None results are stocks that didn't meet criteria (not errors)

            # Save results to database
            total_saved = 0
            if valid_results:
                with ScreenerRepository() as repo:
                    total_saved = repo.save_screening_results(valid_results, scan_timestamp)

                    # Cleanup old results (keep last 30 days)
                    cleaned_count = repo.cleanup_old_results(days_to_keep=30)
                    if cleaned_count > 0:
                        logger.info(f"Cleaned up {cleaned_count} old screening results")

            # Calculate processing metrics
            end_time = time.time()
            processing_time = end_time - start_time

            screening_summary = {
                'scan_timestamp': scan_timestamp,
                'total_stocks_processed': len(stock_list),
                'signals_found': len(valid_results),
                'results_saved': total_saved,
                'errors_encountered': error_count,
                'processing_time_seconds': round(processing_time, 2),
                'results': valid_results
            }

            logger.info(
                f"Screening completed: {len(valid_results)} signals found from "
                f"{len(stock_list)} stocks in {processing_time:.2f}s "
                f"({error_count} errors)"
            )

            return screening_summary

        except Exception as e:
            logger.error(f"Critical error during screening process: {str(e)}")
            raise RuntimeError(f"Screening process failed: {str(e)}") from e

    def get_latest_screening_results(self) -> list[dict[str, str]]:
        """
        Get the most recent screening results from database.

        Returns:
            List of screening results with symbol and companyName
        """
        try:
            with ScreenerRepository() as repo:
                results: list[dict[str, str]] = repo.get_latest_screening_results()
                return results
        except Exception as e:
            logger.error(f"Error retrieving latest screening results: {str(e)}")
            raise RuntimeError(f"Failed to retrieve screening results: {str(e)}") from e

    def get_screening_stats(self) -> dict[str, Any]:
        """
        Get screening statistics and history.

        Returns:
            Dict with screening statistics
        """
        try:
            with ScreenerRepository() as repo:
                history = repo.get_screening_history(limit=10)
                latest_results = repo.get_latest_screening_results()

                stats = {
                    'latest_scan_count': len(latest_results),
                    'recent_scans': history,
                    'total_recent_scans': len(history),
                    'last_scan_timestamp': history[0]['scan_timestamp'] if history else None
                }

                return stats

        except Exception as e:
            logger.error(f"Error retrieving screening statistics: {str(e)}")
            raise RuntimeError(f"Failed to retrieve screening statistics: {str(e)}") from e
