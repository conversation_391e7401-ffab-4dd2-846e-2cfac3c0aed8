import type { Signal, SignalResult } from '@trading-agent/shared-types'
import type { ComputedSignal, EffectivenessMetrics } from '@/types/signal'
import SignalEffectivenessIndicator from '@/components/charts/SignalEffectivenessIndicator'

interface SignalDisplayProps {
  result: SignalResult
  signals?: ComputedSignal[]
  effectivenessMetrics?: EffectivenessMetrics
}

interface SignalConfig {
  color: string
  backgroundColor: string
  borderColor: string
  icon: string
  ariaLabel: string
}

const signalConfigs: Record<Signal, SignalConfig> = {
  SELL_CANDIDATE: {
    color: 'text-red-700',
    backgroundColor: 'bg-red-50',
    borderColor: 'border-red-200',
    icon: '⚠️',
    ariaLabel: 'Sell candidate signal - consider selling'
  },
  HOLD: {
    color: 'text-green-700',
    backgroundColor: 'bg-green-50',
    borderColor: 'border-green-200',
    icon: '✅',
    ariaLabel: 'Hold signal - maintain position'
  },
  NO_SIGNAL: {
    color: 'text-gray-700',
    backgroundColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    icon: '➖',
    ariaLabel: 'No signal - no clear trading recommendation'
  }
}

export function SignalDisplay({ result, signals = [], effectivenessMetrics }: SignalDisplayProps) {
  const config = signalConfigs[result.signal]
  const formattedDate = new Date(result.lastScanDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
  
  const displaySignal = result.signal.replace('_', ' ')

  // Get the most recent and highest confidence signal
  const getLatestSignal = () => {
    if (signals.length === 0) return null
    return signals
      .sort((a, b) => {
        const timeCompare = new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        if (timeCompare !== 0) return timeCompare
        return b.confidence - a.confidence
      })[0]
  }

  const latestSignal = getLatestSignal()
  const hasComputedSignals = signals.length > 0

  return (
    <div className="space-y-4">
      {/* Legacy Signal Display - Always show analysis results */}
      <div 
        className={`p-6 rounded-lg border-2 ${config.backgroundColor} ${config.borderColor}`}
        role="alert"
        aria-live="polite"
        aria-label={config.ariaLabel}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-2xl" role="img" aria-hidden="true">
              {config.icon}
            </span>
            <div>
              <h2 className={`text-2xl font-bold ${config.color}`}>
                {displaySignal}
              </h2>
              <p className={`text-sm ${config.color} opacity-80`}>
                Analysis Result
              </p>
            </div>
          </div>
          <div className={`px-4 py-2 rounded-full border ${config.borderColor} ${config.backgroundColor}`}>
            <span className={`text-sm font-semibold ${config.color}`}>
              {displaySignal}
            </span>
          </div>
        </div>
      </div>

      {/* Computed Signals Display */}
      {hasComputedSignals && latestSignal && (
        <div className="p-6 rounded-lg border-2 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl" role="img" aria-hidden="true">
                {latestSignal.signal_type === 'buy' ? '📈' : latestSignal.signal_type === 'sell' ? '📉' : '➖'}
              </span>
              <div>
                <h2 className="text-2xl font-bold text-blue-700">
                  {latestSignal.signal_type.toUpperCase()}
                </h2>
                <p className="text-sm text-blue-600">
                  {latestSignal.strategy} • Confidence: {(latestSignal.confidence * 100).toFixed(1)}%
                </p>
              </div>
            </div>
            <div className="px-4 py-2 rounded-full border border-blue-300 bg-blue-100">
              <span className="text-sm font-semibold text-blue-700">
                ${latestSignal.price.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Effectiveness Metrics */}
      {effectivenessMetrics && (
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Signal Effectiveness
          </h3>
          <SignalEffectivenessIndicator effectiveness={effectivenessMetrics} />
        </div>
      )}

      {/* Signal Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {result.symbol}
              </h3>
              <p className="text-sm text-gray-600">
                Stock Symbol
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {signals.length}
              </h3>
              <p className="text-sm text-gray-600">
                Total Signals
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {formattedDate}
              </h3>
              <p className="text-sm text-gray-600">
                Last Analysis
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Signals List */}
      {signals.length > 0 && (
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">
            Recent Signals ({signals.slice(0, 5).length} of {signals.length})
          </h3>
          <div className="space-y-2">
            {signals.slice(0, 5).map((signal, index) => (
              <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded">
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    signal.signal_type === 'buy' 
                      ? 'bg-green-100 text-green-700' 
                      : signal.signal_type === 'sell'
                      ? 'bg-red-100 text-red-700'
                      : 'bg-gray-100 text-gray-700'
                  }`}>
                    {signal.signal_type.toUpperCase()}
                  </span>
                  <span className="text-sm text-gray-600">
                    {signal.strategy}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>${signal.price.toFixed(2)}</span>
                  <span>•</span>
                  <span>{(signal.confidence * 100).toFixed(0)}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Chart Data Summary */}
      {result.chartData ? (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">
            Analysis Data Summary
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Daily Prices:</span>
              <span className="ml-2 text-gray-600">
                {result.chartData.dailyPrices?.length || 0} data points
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Divergence Points:</span>
              <span className="ml-2 text-gray-600">
                {result.chartData.divergencePoints?.length || 0}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">MACD Data:</span>
              <span className="ml-2 text-gray-600">
                {result.chartData.macdLine?.filter(v => v !== null).length || 0} values
              </span>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <h3 className="text-sm font-semibold text-yellow-800 mb-2">
            Chart Data Unavailable
          </h3>
          <p className="text-sm text-yellow-700">
            Technical analysis data is not available for this stock at the moment.
          </p>
        </div>
      )}
    </div>
  )
}