import type { Signal, SignalResult } from '@trading-agent/shared-types'

interface SignalDisplayProps {
  result: SignalResult
}

interface SignalConfig {
  color: string
  backgroundColor: string
  borderColor: string
  icon: string
  ariaLabel: string
}

const signalConfigs: Record<Signal, SignalConfig> = {
  SELL_CANDIDATE: {
    color: 'text-red-700',
    backgroundColor: 'bg-red-50',
    borderColor: 'border-red-200',
    icon: '⚠️',
    ariaLabel: 'Sell candidate signal - consider selling'
  },
  HOLD: {
    color: 'text-green-700',
    backgroundColor: 'bg-green-50',
    borderColor: 'border-green-200',
    icon: '✅',
    ariaLabel: 'Hold signal - maintain position'
  },
  NO_SIGNAL: {
    color: 'text-gray-700',
    backgroundColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    icon: '➖',
    ariaLabel: 'No signal - no clear trading recommendation'
  }
}

export function SignalDisplay({ result }: SignalDisplayProps) {
  const config = signalConfigs[result.signal]
  const formattedDate = new Date(result.lastScanDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
  
  const displaySignal = result.signal.replace('_', ' ')

  return (
    <div className="space-y-4">
      {/* Main Signal Display - Most Prominent */}
      <div 
        className={`p-6 rounded-lg border-2 ${config.backgroundColor} ${config.borderColor}`}
        role="alert"
        aria-live="polite"
        aria-label={config.ariaLabel}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-2xl" role="img" aria-hidden="true">
              {config.icon}
            </span>
            <div>
              <h2 className={`text-2xl font-bold ${config.color}`}>
                {displaySignal}
              </h2>
              <p className={`text-sm ${config.color} opacity-80`}>
                Trading Signal
              </p>
            </div>
          </div>
          <div className={`px-4 py-2 rounded-full border ${config.borderColor} ${config.backgroundColor}`}>
            <span className={`text-sm font-semibold ${config.color}`}>
              {displaySignal}
            </span>
          </div>
        </div>
      </div>

      {/* Additional Summary Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {result.symbol}
              </h3>
              <p className="text-sm text-gray-600">
                Stock Symbol
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {formattedDate}
              </h3>
              <p className="text-sm text-gray-600">
                Last Analysis
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Chart Data Summary */}
      {result.chartData ? (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">
            Analysis Data Summary
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Daily Prices:</span>
              <span className="ml-2 text-gray-600">
                {result.chartData.dailyPrices?.length || 0} data points
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Divergence Points:</span>
              <span className="ml-2 text-gray-600">
                {result.chartData.divergencePoints?.length || 0}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">MACD Data:</span>
              <span className="ml-2 text-gray-600">
                {result.chartData.macdLine?.filter(v => v !== null).length || 0} values
              </span>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <h3 className="text-sm font-semibold text-yellow-800 mb-2">
            Chart Data Unavailable
          </h3>
          <p className="text-sm text-yellow-700">
            Technical analysis data is not available for this stock at the moment.
          </p>
        </div>
      )}
    </div>
  )
}