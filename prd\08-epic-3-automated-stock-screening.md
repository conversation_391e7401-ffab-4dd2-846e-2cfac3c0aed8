### Epic 3: Automated Stock Screening

This epic delivers the second major feature of the application, transforming it from a single-stock analysis tool into a market discovery engine. It focuses on building the backend process to automatically scan a wide range of stocks and the frontend view to display the results. This provides immense value to users by proactively bringing trading opportunities to their attention.

#### Story 3.1: Backend Screening Service

**As a** System, **I want** to periodically run the unified signal strategy against a predefined list of stock codes, **so that** I can identify all stocks that currently meet the criteria for a potential trade.
**Acceptance Criteria:**

1. A new backend service is created that takes a list of stock codes (e.g., the CSI 300) as input.
2. The service iterates through the list, applying the core signal generation logic (from Epic 1) to each stock.
3. The service collects and stores the list of all stock codes that produced a positive signal.
4. The process is optimized to handle a large number of stocks efficiently.
5. A mechanism is in place to trigger this service on a recurring schedule (e.g., once per day).

#### Story 3.2: Screener Results API Endpoint

**As a** System, **I want** an API endpoint that provides the latest results from the stock screener, **so that** the frontend application can display these results to the user.
**Acceptance Criteria:**

1. A new backend API endpoint (e.g., `GET /api/screener/results`) is created.
2. The endpoint retrieves and returns a JSON array of the most recent list of stocks identified by the screening service.
3. The response includes the necessary data for each stock to be displayed in a list (e.g., stock code, company name).
4. The endpoint serves a cached or pre-computed result to ensure a fast response time.

#### Story 3.3: Screener Results UI

**As a** User, **I want** to see a clear list of stocks that have been flagged by the screener, **so that** I can discover and investigate potential trading opportunities.
**Acceptance Criteria:**

1. The "Screener" view calls the `GET /api/screener/results` endpoint when loaded.
2. The returned list of stocks is displayed in a simple, readable table or list format.
3. Each stock code in the list is a clickable link.
4. Clicking a stock code navigates the user to the "Analysis" view and automatically populates the input with that stock code for a detailed view.
5. A timestamp is displayed on the page indicating when the results were last updated.

***
