import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { StockInputForm } from '../StockInputForm'

const mockOnSubmit = vi.fn()

describe('StockInputForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders input field and submit button', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    expect(screen.getByPlaceholderText('Enter stock code (e.g., 000001)')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /analyze/i })).toBeInTheDocument()
  })

  it('accepts user input', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)') as HTMLInputElement
    
    fireEvent.change(input, { target: { value: '000001' } })
    
    expect(input.value).toBe('000001')
  })

  it('calls onSubmit with stock code when form is submitted', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })
    
    fireEvent.change(input, { target: { value: '000001' } })
    fireEvent.click(button)
    
    expect(mockOnSubmit).toHaveBeenCalledWith('000001')
  })

  it('calls onSubmit when Enter key is pressed', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    
    fireEvent.change(input, { target: { value: '000002' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    expect(mockOnSubmit).toHaveBeenCalledWith('000002')
  })

  it('validates stock code format', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })
    
    // Test invalid format
    fireEvent.change(input, { target: { value: '12345' } })
    fireEvent.click(button)
    
    expect(screen.getByText('Invalid format. Please use 6-digit format: 000001')).toBeInTheDocument()
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('shows error for empty input', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const button = screen.getByRole('button', { name: /analyze/i })
    
    fireEvent.click(button)
    
    expect(screen.getByText('Stock code is required')).toBeInTheDocument()
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('clears error when valid input is entered', async () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })
    
    // First, trigger an error
    fireEvent.click(button)
    expect(screen.getByText('Stock code is required')).toBeInTheDocument()
    
    // Then enter valid input
    fireEvent.change(input, { target: { value: '000001' } })
    
    await waitFor(() => {
      expect(screen.queryByText('Stock code is required')).not.toBeInTheDocument()
    })
  })

  it('converts input to uppercase', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)') as HTMLInputElement
    
    fireEvent.change(input, { target: { value: '000001' } })
    
    expect(input.value).toBe('000001')
  })

  it('shows loading state when loading prop is true', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} loading={true} />)
    
    const button = screen.getByRole('button', { name: /analyzing/i })
    
    expect(button).toBeDisabled()
    expect(screen.getByText('Analyzing...')).toBeInTheDocument()
  })

  it('disables form when disabled prop is true', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} disabled={true} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })
    
    expect(input).toBeDisabled()
    expect(button).toBeDisabled()
  })

  it('pre-populates with initialValue prop', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} initialValue="000003" />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)') as HTMLInputElement
    
    expect(input.value).toBe('000003')
  })

  it('updates input when initialValue prop changes', () => {
    const { rerender } = render(<StockInputForm onSubmit={mockOnSubmit} initialValue="000001" />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)') as HTMLInputElement
    expect(input.value).toBe('000001')
    
    // Change the initialValue prop
    rerender(<StockInputForm onSubmit={mockOnSubmit} initialValue="000002" />)
    
    expect(input.value).toBe('000002')
  })

  it('handles undefined initialValue gracefully', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} initialValue={undefined} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)') as HTMLInputElement
    
    expect(input.value).toBe('')
  })

  it('allows user to override initialValue', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} initialValue="000001" />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)') as HTMLInputElement
    expect(input.value).toBe('000001')
    
    // User can still change the value
    fireEvent.change(input, { target: { value: '000002' } })
    expect(input.value).toBe('000002')
  })

  it('validates stock code with correct pattern', () => {
    render(<StockInputForm onSubmit={mockOnSubmit} />)
    
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })
    
    // Test valid codes
    const validCodes = ['000001', '000002', '600000', '300001', '002001']
    
    validCodes.forEach(code => {
      fireEvent.change(input, { target: { value: code } })
      fireEvent.click(button)
      
      expect(mockOnSubmit).toHaveBeenCalledWith(code)
      mockOnSubmit.mockClear()
    })
    
    // Test invalid codes
    const invalidCodes = ['', '123', '12345', '1234567', 'AAPL', '00000A']
    
    invalidCodes.forEach(code => {
      fireEvent.change(input, { target: { value: code } })
      fireEvent.click(button)
      
      expect(mockOnSubmit).not.toHaveBeenCalled()
    })
  })
})