import { test, expect } from '@playwright/test'

test.describe('Basic Navigation', () => {
  test('should load homepage and navigate between pages', async ({ page }) => {
    // Go to the homepage
    await page.goto('/')

    // Expect page title to contain "Trading Agent"
    await expect(page).toHaveTitle(/Trading Agent/)

    // Expect header to be visible
    await expect(page.getByText('Trading Agent')).toBeVisible()

    // Expect navigation links to be visible
    await expect(page.getByRole('link', { name: 'Analysis' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'Screener' })).toBeVisible()

    // Navigate to Screener page
    await page.getByRole('link', { name: 'Screener' }).click()
    await expect(page.getByText('Stock Screener')).toBeVisible()

    // Navigate back to Analysis page
    await page.getByRole('link', { name: 'Analysis' }).click()
    await expect(page.getByText('Stock Analysis')).toBeVisible()
  })
})