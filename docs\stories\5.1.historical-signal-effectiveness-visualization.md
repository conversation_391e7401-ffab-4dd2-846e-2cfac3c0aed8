---
status: Review
story: |
  **Epic 5: Historical Analysis & Signal Validation**
  **Story 5.1: Historical Signal Effectiveness Visualization**
  
  As a **Trader**, I want to view historical buy/sell signals overlaid on interactive charts with effectiveness indicators, so that I can evaluate the performance and reliability of these signals before making investment decisions.
  
  **Business Value:**
  - Enable data-driven decision making by showing signal track record
  - Reduce investment risk through historical performance analysis
  - Increase trader confidence in signal reliability
  - Provide transparency in algorithmic trading recommendations

acceptance_criteria: |
  **Given** I am viewing a stock chart with historical data
  **When** I enable historical signal visualization
  **Then** I should see:
  
  1. **Signal Overlay Display:**
     - Buy signals marked with green upward arrows at historical trigger points
     - Sell signals marked with red downward arrows at historical trigger points
     - Signal timestamps and trigger prices clearly visible
  
  2. **Effectiveness Indicators:**
     - Success rate percentage for each signal type (buy/sell)
     - Average return/loss per signal over configurable time periods
     - Color-coded effectiveness rating (green=high, yellow=medium, red=low)
     - Profit/loss indicators showing actual vs predicted outcomes
  
  3. **Interactive Features:**
     - Hover tooltips showing detailed signal information
     - Click-to-expand signal details with entry/exit points
     - Filter signals by date range, effectiveness threshold, or signal type
     - Toggle signal visibility on/off without losing chart state
  
  4. **Performance Metrics:**
     - Overall portfolio impact from following historical signals
     - Win/loss ratio statistics
     - Maximum drawdown and recovery periods
     - Comparison with buy-and-hold strategy
  
  5. **Integration Requirements:**
     - Seamless integration with existing Story 2.4 chart functionality
     - No performance degradation when loading historical signal data
     - Responsive design maintaining usability across device sizes

tasks:
  - task_id: "5.1.1"
    title: "Backend Real-Time Signal Computation API"
    owner: "Dev Agent"
    subtasks:
      - "*******: Implement real-time signal calculation algorithms from raw market data"
      - "*******: Create FastAPI endpoints for on-demand signal computation with filtering"
      - "5.1.1.3: Build signal effectiveness calculation engine for dynamic analysis"
      - "5.1.1.4: Add caching layer for computed signals to optimize performance"
      - "5.1.1.5: Implement data validation and error handling for computation requests"
  
  - task_id: "5.1.2"
    title: "Frontend Signal Visualization Components"
    owner: "Dev Agent"
    subtasks:
      - "5.1.2.1: Extend existing chart component to support signal overlays"
      - "5.1.2.2: Create SignalMarker component with hover interactions"
      - "5.1.2.3: Implement signal filtering and toggle controls"
      - "5.1.2.4: Design effectiveness indicator UI components"
      - "5.1.2.5: Add responsive design for mobile signal visualization"
  
  - task_id: "5.1.3"
    title: "Signal Effectiveness Analytics"
    owner: "Dev Agent"
    subtasks:
      - "5.1.3.1: Implement performance metrics calculation engine"
      - "5.1.3.2: Create statistical analysis for win/loss ratios"
      - "5.1.3.3: Build comparison algorithms vs benchmark strategies"
      - "5.1.3.4: Design effectiveness rating system"
      - "5.1.3.5: Add real-time effectiveness updates"
  
  - task_id: "5.1.4"
    title: "Integration & Performance Optimization"
    owner: "Dev Agent"
    subtasks:
      - "5.1.4.1: Integrate with existing Zustand state management for signal computation"
      - "5.1.4.2: Optimize chart rendering with real-time computed signals"
      - "5.1.4.3: Implement intelligent caching for computed signal results"
      - "5.1.4.4: Add error boundaries and fallback UI states for computation failures"
      - "5.1.4.5: Performance testing and optimization for real-time calculations"
  
  - task_id: "5.1.5"
    title: "Testing & Quality Assurance"
    owner: "QA Agent"
    subtasks:
      - "5.1.5.1: Unit tests for signal calculation algorithms"
      - "5.1.5.2: Integration tests for API endpoints"
      - "5.1.5.3: Component tests for signal visualization"
      - "5.1.5.4: End-to-end tests for complete user workflows"
      - "5.1.5.5: Performance and load testing"

dev_notes: |
  ## Source Tree Context
  
  **Relevant Existing Components:**
  - `apps/web/src/components/Chart/` - Extend existing chart infrastructure
  - `apps/web/src/stores/` - Zustand state management for signal data
  - `apps/api/src/services/signal_service.py` - Backend signal generation logic
  - `apps/api/src/models/` - Database models for signal storage
  
  **New Components to Create:**
  - `apps/web/src/components/Chart/SignalOverlay.tsx`
  - `apps/web/src/components/Chart/SignalMarker.tsx`
  - `apps/web/src/components/Chart/EffectivenessIndicator.tsx`
  - `apps/web/src/stores/signalComputationStore.ts`
  - `apps/api/src/services/signal_computation_service.py`
  - `apps/api/src/calculators/signal_algorithms.py`
  
  ## Tech Stack Integration
  
  **Frontend (React/TypeScript):**
  - Extend existing chart library integration (likely Recharts or similar)
  - Use Radix UI components for signal controls and tooltips
  - Implement with Tailwind CSS for consistent styling
  - Integrate with existing Zustand stores for state management
  
  **Backend (Python/FastAPI):**
  - Extend existing FastAPI structure in `apps/api/src/`
  - Use SQLAlchemy ORM for database operations
  - Implement async endpoints for performance
  - Add Pydantic models for request/response validation
  
  **Database (SQLite):**
  - Utilize existing market data tables for signal computation
  - Ensure proper indexing on timestamp and symbol for fast data retrieval
  - No additional tables needed for signal storage - signals computed on-demand
  
  ## Implementation Approach
  
  **Phase 1: Computation Foundation**
  1. Design real-time signal calculation algorithms using existing market data
  2. Implement signal computation service with configurable parameters
  3. Create API endpoints for on-demand signal generation
  
  **Phase 2: API Development**
  1. Build FastAPI endpoints for real-time signal computation with filtering
  2. Implement effectiveness calculation algorithms for dynamically computed signals
  3. Add intelligent caching layer for computed results to reduce redundant calculations
  
  **Phase 3: Frontend Integration**
  1. Extend existing chart component architecture
  2. Create reusable signal visualization components
  3. Implement interactive features (hover, click, filter)
  
  **Phase 4: Analytics & Optimization**
  1. Build performance metrics calculation engine
  2. Optimize rendering for large datasets
  3. Add real-time effectiveness updates
  
  ## File Structure
  
  ```
  apps/
  ├── api/
  │   ├── src/
  │   │   ├── calculators/
  │   │   │   ├── signal_algorithms.py
  │   │   │   └── effectiveness_calculator.py
  │   │   ├── services/
  │   │   │   └── signal_computation_service.py
  │   │   ├── routers/
  │   │   │   └── signal_computation.py
  │   │   └── schemas/
  │   │       └── signal_computation.py
  │   └── tests/
  │       └── test_signal_computation.py
  └── web/
      ├── src/
      │   ├── components/
      │   │   └── Chart/
      │   │       ├── SignalOverlay.tsx
      │   │       ├── SignalMarker.tsx
      │   │       ├── EffectivenessIndicator.tsx
      │   │       └── SignalControls.tsx
      │   ├── stores/
      │  ├── stores/
      │   └── signalComputationStore.ts
      ├── services/
      │   └── signalComputationApi.ts
      └── types/
          └── signalComputation.ts
      └── src/components/__tests__/
          └── Chart/
              ├── SignalOverlay.test.tsx
              └── SignalMarker.test.tsx
  ```
  
  ## Architecture Considerations
  
  **Performance:**
  - Implement virtual scrolling for large historical datasets
  - Use React.memo and useMemo for expensive calculations
  - Consider WebWorkers for heavy signal processing
  - Implement progressive loading of historical data
  
  **State Management:**
  - Extend existing Zustand stores with signal computation state
  - Implement optimistic updates for better UX during computation
  - Cache computed signal results to avoid redundant calculations
  - Handle real-time signal updates without full re-renders
  
  **Testing Strategy:**
  - Unit tests for signal calculation algorithms (>90% coverage)
  - Integration tests for API endpoints
  - Component tests using React Testing Library
  - E2E tests for complete user workflows using Playwright
  - Performance tests for large dataset handling
  
  ## Dependencies
  
  **New Frontend Dependencies:**
  - Consider `react-window` for virtualization if needed
  - `date-fns` for date manipulation in filters
  - Existing: React, TypeScript, Tailwind CSS, Radix UI
  
  **New Backend Dependencies:**
  - `pandas` for signal effectiveness calculations
  - `numpy` for statistical analysis
  - Existing: FastAPI, SQLAlchemy, Pydantic
  
  ## Risk Mitigation
  
  **Performance Risks:**
  - Real-time signal computation may impact response times
  - Solution: Implement intelligent caching and optimize calculation algorithms
  
  **Data Accuracy Risks:**
  - Historical signal effectiveness calculations must be precise
  - Solution: Comprehensive unit tests and data validation
  
  **Computation Risks:**
  - Complex signal calculations may cause performance bottlenecks
  - Solution: Optimize algorithms and implement progressive computation
  
  **Integration Risks:**
  - Changes to existing chart component may break functionality
  - Solution: Thorough regression testing and feature flags

change_log:
  - date: "2024-01-XX"
    author: "Scrum Master"
    changes: "Initial story creation with comprehensive requirements and technical specifications"

dev_agent_record: |
  # Dev Agent Implementation Record
  
  *This section will be populated by the Dev Agent during implementation*
  
  ## Implementation Progress
  - [ ] Real-time signal calculation algorithms
  - [ ] Backend computation API development
  - [ ] Frontend component development
  - [ ] Integration testing
  - [ ] Performance optimization
  
  ## Technical Decisions
  *To be documented during development*
  
  ## Challenges & Solutions
  *To be documented during development*
  
  ## Code Review Notes
  *To be documented during development*

qa_results:
  reviewer: "QA Agent"
  status: "Pending"
  review_date: ""
  findings: |
    *QA review pending - story in Draft status*
    
    **Review Checklist:**
    - [ ] Acceptance criteria completeness
    - [ ] Technical feasibility assessment
    - [ ] Integration impact analysis
    - [ ] Performance requirements validation
    - [ ] Testing strategy adequacy
    - [ ] Security considerations review
  
  recommendations: |
    *To be completed after implementation*
---