"""Tests for signal service module."""

from unittest.mock import Mock, patch

import pytest

from src.features.signal.service import SignalService
from src.models.stock_data import DailyPrice, StockData
from src.models.strategy import ChartData, SignalResult


class TestSignalService:
    """Test cases for SignalService class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.signal_service = SignalService()

        # Create mock stock data with enough points for MACD (35+ required)
        self.mock_daily_prices = []
        for i in range(40):  # Create 40 data points
            date = f"2025-07-{str(i+1).zfill(2)}" if i < 31 else f"2025-08-{str(i-30).zfill(2)}"
            price = 100.0 + i * 0.5  # Gradually increasing prices
            self.mock_daily_prices.append(
                DailyPrice(
                    date=date,
                    open=price,
                    high=price + 2.0,
                    low=price - 2.0,
                    close=price + 1.0,
                    volume=1000 + i * 10
                )
            )
        self.mock_stock_data = StockData(symbol="000001", daily_prices=self.mock_daily_prices)

        # Create mock signal result
        self.mock_chart_data = ChartData(
            daily_prices=self.mock_daily_prices,
            magic_nine_sequence=[None] * 37 + [1, 2, 3],  # Match the 40 data points
            macd_line=[None] * 35 + [0.5, 0.6, 0.7, 0.8, 0.9],  # Start after 35 points
            signal_line=[None] * 35 + [0.4, 0.5, 0.6, 0.7, 0.8],
            divergence_points=[]
        )
        self.mock_signal_result = SignalResult(
            symbol="000001",
            last_scan_date="2025-08-17T10:00:00Z",
            signal="HOLD",
            chart_data=self.mock_chart_data
        )

    @patch('src.features.signal.service.StrategyEngine')
    @patch('src.features.signal.service.DataIngestionService')
    def test_get_signal_for_stock_success(self, mock_data_service_class, mock_strategy_engine_class):
        """Test successful signal generation for a stock."""
        # Setup mocks
        mock_data_service = Mock()
        mock_data_service.fetch_stock_data.return_value = self.mock_stock_data
        mock_data_service_class.return_value = mock_data_service

        mock_strategy_engine = Mock()
        mock_strategy_engine.calculate_signal.return_value = self.mock_signal_result
        mock_strategy_engine_class.return_value = mock_strategy_engine

        # Create new service instance with mocked dependencies
        service = SignalService()
        service.data_service = mock_data_service
        service.strategy_engine = mock_strategy_engine

        # Test the service
        result = service.get_signal_for_stock("000001")

        # Verify calls
        mock_data_service.fetch_stock_data.assert_called_once_with(
            stock_code="000001",
            start_date=None,
            end_date=None
        )
        mock_strategy_engine.calculate_signal.assert_called_once_with(self.mock_stock_data)

        # Verify result
        assert result == self.mock_signal_result
        assert result.symbol == "000001"
        assert result.signal == "HOLD"

    @patch('src.features.signal.service.StrategyEngine')
    @patch('src.features.signal.service.DataIngestionService')
    def test_get_signal_for_stock_with_date_range(self, mock_data_service_class, mock_strategy_engine_class):
        """Test signal generation with custom date range."""
        # Setup mocks
        mock_data_service = Mock()
        mock_data_service.fetch_stock_data.return_value = self.mock_stock_data
        mock_data_service_class.return_value = mock_data_service

        mock_strategy_engine = Mock()
        mock_strategy_engine.calculate_signal.return_value = self.mock_signal_result
        mock_strategy_engine_class.return_value = mock_strategy_engine

        # Create new service instance with mocked dependencies
        service = SignalService()
        service.data_service = mock_data_service
        service.strategy_engine = mock_strategy_engine

        # Test with date range
        result = service.get_signal_for_stock(
            "000001",
            start_date="2025-07-01",
            end_date="2025-08-17"
        )

        # Verify calls with date parameters
        mock_data_service.fetch_stock_data.assert_called_once_with(
            stock_code="000001",
            start_date="2025-07-01",
            end_date="2025-08-17"
        )
        assert result == self.mock_signal_result

    @patch('src.features.signal.service.StrategyEngine')
    @patch('src.features.signal.service.DataIngestionService')
    def test_get_signal_for_stock_data_service_error(self, mock_data_service_class, mock_strategy_engine_class):
        """Test handling of data service errors."""
        # Setup mock to raise ValueError
        mock_data_service = Mock()
        mock_data_service.fetch_stock_data.side_effect = ValueError("Invalid stock code")
        mock_data_service_class.return_value = mock_data_service

        # Create new service instance with mocked dependencies
        service = SignalService()
        service.data_service = mock_data_service

        # Test error handling
        with pytest.raises(ValueError, match="Invalid stock data for 000001: Invalid stock code"):
            service.get_signal_for_stock("000001")

    @patch('src.features.signal.service.StrategyEngine')
    @patch('src.features.signal.service.DataIngestionService')
    def test_get_signal_for_stock_strategy_engine_error(self, mock_data_service_class, mock_strategy_engine_class):
        """Test handling of strategy engine errors."""
        # Setup mocks - data service succeeds, strategy engine fails
        mock_data_service = Mock()
        mock_data_service.fetch_stock_data.return_value = self.mock_stock_data
        mock_data_service_class.return_value = mock_data_service

        mock_strategy_engine = Mock()
        mock_strategy_engine.calculate_signal.side_effect = RuntimeError("Strategy calculation failed")
        mock_strategy_engine_class.return_value = mock_strategy_engine

        # Create new service instance with mocked dependencies
        service = SignalService()
        service.data_service = mock_data_service
        service.strategy_engine = mock_strategy_engine

        # Test error handling
        with pytest.raises(RuntimeError, match="Signal processing failed: Strategy calculation failed"):
            service.get_signal_for_stock("000001")

    @patch('src.features.signal.service.StrategyEngine')
    @patch('src.features.signal.service.DataIngestionService')
    def test_get_signal_for_stock_external_service_error(self, mock_data_service_class, mock_strategy_engine_class):
        """Test handling of external service unavailability."""
        # Setup mock to raise RuntimeError (external service issue)
        mock_data_service = Mock()
        mock_data_service.fetch_stock_data.side_effect = RuntimeError("akshare service unavailable")
        mock_data_service_class.return_value = mock_data_service

        # Create new service instance with mocked dependencies
        service = SignalService()
        service.data_service = mock_data_service

        # Test error handling
        with pytest.raises(RuntimeError, match="Signal analysis service unavailable: akshare service unavailable"):
            service.get_signal_for_stock("000001")

    def test_signal_service_initialization(self):
        """Test that signal service initializes correctly."""
        service = SignalService()

        # Verify dependencies are initialized
        assert hasattr(service, 'data_service')
        assert hasattr(service, 'strategy_engine')
        assert service.data_service is not None
        assert service.strategy_engine is not None
