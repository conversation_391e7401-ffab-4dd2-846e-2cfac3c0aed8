import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useSignalHistoryStore } from '../signalHistoryStore';
import type { SignalFilters } from '../signalHistoryStore';

// Mock the API service
vi.mock('../../services/signalHistoryApi', () => ({
  signalHistoryApi: {
    getSignalHistory: vi.fn(),
    getSignalEffectiveness: vi.fn(),
    getSignalPerformanceMetrics: vi.fn(),
    getAvailableStrategies: vi.fn(),
    compareSignalStrategies: vi.fn()
  },
  getCachedSignalHistory: vi.fn(),
  getCachedSignalEffectiveness: vi.fn(),
  filtersToApiRequest: vi.fn(),
  SignalHistoryApiError: class SignalHistoryApiError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'SignalHistoryApiError';
    }
  }
}));

import { signalHistoryApi, getCachedSignalHistory, getCachedSignalEffectiveness, filtersToApiRequest, SignalHistoryApiError } from '../../services/signalHistoryApi';

const mockSignalHistoryApi = signalHistoryApi as any;
const mockGetCachedSignalHistory = getCachedSignalHistory as any;
const mockGetCachedSignalEffectiveness = getCachedSignalEffectiveness as any;
const mockFiltersToApiRequest = filtersToApiRequest as any;

describe('useSignalHistoryStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset the store state
    const { result } = renderHook(() => useSignalHistoryStore());
    act(() => {
      result.current.reset();
    });
    
    // Setup default mock implementations
    mockFiltersToApiRequest.mockImplementation((symbol: string, filters: any, pagination: any) => ({
      symbol,
      start_date: filters.dateRange?.start || '',
      end_date: filters.dateRange?.end || '',
      signal_types: filters.signalTypes?.join(',') || '',
      min_confidence: filters.minConfidence || 0,
      min_effectiveness: filters.minEffectiveness || 0,
      strategies: filters.strategies?.join(',') || undefined,
      page: pagination.page || 1,
      page_size: pagination.pageSize || 50
    }));
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useSignalHistoryStore());
      
      expect(result.current.signals).toEqual([]);
      expect(result.current.effectiveness).toBeNull();
      expect(result.current.performanceMetrics).toBeNull();
      expect(result.current.availableStrategies).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.filters.signalTypes).toEqual(['buy', 'sell']);
      expect(result.current.filters.minConfidence).toBe(0);
      expect(result.current.filters.minEffectiveness).toBe(0);
      expect(result.current.filters.strategies).toEqual([]);
    });
  });

  describe('setFilters', () => {
    it('should update filters correctly', () => {
      const { result } = renderHook(() => useSignalHistoryStore());
      
      const newFilters: SignalFilters = {
        dateRange: { start: '2024-01-01', end: '2024-12-31' },
        signalTypes: ['buy'],
        strategies: ['momentum'],
        minConfidence: 0.8,
        minEffectiveness: 0.7
      };
      
      act(() => {
        result.current.setFilters(newFilters);
      });
      
      expect(result.current.filters).toEqual(newFilters);
    });

    it('should merge partial filters with existing ones', () => {
      const { result } = renderHook(() => useSignalHistoryStore());
      
      // Set initial filters
      act(() => {
        result.current.setFilters({
          dateRange: { start: '2024-01-01', end: '2024-12-31' },
          signalTypes: ['buy'],
          strategies: ['momentum'],
          minConfidence: 0.8,
          minEffectiveness: 0.7
        });
      });
      
      // Update only some filters
      act(() => {
        result.current.setFilters({
          signalTypes: ['buy', 'sell'],
          minConfidence: 0.9
        } as Partial<SignalFilters>);
      });
      
      expect(result.current.filters).toEqual({
        dateRange: { start: '2024-01-01', end: '2024-12-31' },
        signalTypes: ['buy', 'sell'],
        strategies: ['momentum'],
        minConfidence: 0.9,
        minEffectiveness: 0.7
      });
    });
  });

  describe('clearError', () => {
    it('should clear error state', () => {
      const { result } = renderHook(() => useSignalHistoryStore());
      
      // Simulate an error state
      act(() => {
        // Manually set error for testing
        (result.current as any).error = 'Test error';
      });
      
      act(() => {
        result.current.clearError();
      });
      
      expect(result.current.error).toBeNull();
    });
  });

  describe('fetchSignals', () => {
    it('should fetch signals successfully', async () => {
      const mockResponse = {
        signals: [
          {
            id: '1',
            symbol: 'AAPL',
            signal_type: 'buy',
            timestamp: '2024-01-01T00:00:00Z',
            price: 150.0,
            confidence: 0.85,
            strategy_name: 'momentum'
          }
        ],
        page: 1,
        page_size: 50,
        total_count: 1
      };
      
      mockGetCachedSignalHistory.mockResolvedValueOnce(mockResponse);
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchSignals('AAPL');
      });
      
      expect(result.current.signals).toEqual(mockResponse.signals);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(mockGetCachedSignalHistory).toHaveBeenCalled();
    });

    it('should handle fetch signals error', async () => {
      const error = new Error('API Error');
      mockGetCachedSignalHistory.mockRejectedValueOnce(error);
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchSignals('AAPL');
      });
      
      expect(result.current.signals).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe('API Error');
    });

    it('should set loading state during fetch', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      
      mockSignalHistoryApi.getSignalHistory.mockReturnValueOnce(promise);
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      act(() => {
        result.current.fetchSignals('AAPL');
      });
      
      expect(result.current.isLoading).toBe(true);
      
      await act(async () => {
        resolvePromise!({ signals: [], pagination: {} });
        await promise;
      });
      
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('fetchEffectiveness', () => {
    it('should fetch effectiveness data successfully', async () => {
      const mockEffectiveness = {
        effectiveness_data: [
          {
            strategy_name: 'momentum',
            effectiveness_score: 0.75,
            total_signals: 100,
            successful_signals: 75
          }
        ]
      };
      
      mockGetCachedSignalEffectiveness.mockResolvedValueOnce(mockEffectiveness);
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchEffectiveness('AAPL');
      });
      
      expect(result.current.effectiveness).toEqual(mockEffectiveness);
      expect(result.current.error).toBeNull();
    });

    it('should handle fetch effectiveness error', async () => {
      const error = new Error('API Error');
      mockGetCachedSignalEffectiveness.mockRejectedValueOnce(error);
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchEffectiveness('AAPL');
      });
      
      expect(result.current.effectiveness).toBeNull();
      expect(result.current.error).toBe('API Error');
    });
  });

  describe('fetchPerformanceMetrics', () => {
    it('should fetch performance metrics successfully', async () => {
      const mockMetrics = {
        total_return: 0.15,
        sharpe_ratio: 1.2,
        max_drawdown: -0.05,
        win_rate: 0.65
      };
      
      mockSignalHistoryApi.getSignalPerformanceMetrics.mockResolvedValueOnce(mockMetrics);
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchPerformanceMetrics('AAPL');
      });
      
      expect(result.current.performanceMetrics).toEqual(mockMetrics);
      expect(result.current.error).toBeNull();
    });

    it('should handle fetch performance metrics error', async () => {
      const mockError = new SignalHistoryApiError('API Error', 500);
      mockSignalHistoryApi.getSignalPerformanceMetrics.mockRejectedValueOnce(mockError);
      
      // Mock console.error to avoid console output during tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchPerformanceMetrics('AAPL');
      });
      
      // The store doesn't set error state on failure, it just logs
      expect(consoleSpy).toHaveBeenCalledWith('Failed to fetch performance metrics:', mockError);
      
      consoleSpy.mockRestore();
    });
  });

  describe('fetchStrategies', () => {
    it('should fetch strategies successfully', async () => {
      const mockStrategies = {
        strategies: [
          { name: 'momentum', description: 'Momentum strategy' },
          { name: 'mean-reversion', description: 'Mean reversion strategy' }
        ]
      };
      
      mockSignalHistoryApi.getAvailableStrategies.mockResolvedValueOnce(mockStrategies);
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchStrategies();
      });
      
      expect(result.current.availableStrategies).toEqual(mockStrategies);
      expect(result.current.error).toBeNull();
    });

    it('should handle fetch strategies error', async () => {
      const mockError = new SignalHistoryApiError('API Error', 500);
      mockSignalHistoryApi.getAvailableStrategies.mockRejectedValueOnce(mockError);
      
      // Mock console.error to avoid console output during tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const { result } = renderHook(() => useSignalHistoryStore());
      
      await act(async () => {
        await result.current.fetchStrategies();
      });
      
      // The store doesn't clear strategies or set error on failure, it just logs
       expect(consoleSpy).toHaveBeenCalledWith('Failed to fetch strategies:', mockError);
       
       consoleSpy.mockRestore();
     });
  });
});