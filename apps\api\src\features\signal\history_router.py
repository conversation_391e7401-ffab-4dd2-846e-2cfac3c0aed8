"""API router for historical signal data and effectiveness endpoints."""

import logging
from datetime import datetime
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...models.signal_history_schemas import (
    SignalEffectivenessListResponse,
    SignalEffectivenessResponse,
    SignalHistoryCreate,
    SignalHistoryFilter,
    SignalHistoryListResponse,
    SignalHistoryResponse,
    SignalHistoryUpdate,
    SignalPerformanceMetrics,
)
from .history_service import SignalHistoryService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/signal-history", tags=["Signal History"])


@router.post("/", response_model=SignalHistoryResponse)
async def create_signal_record(
    signal_data: SignalHistoryCreate,
    db: Session = Depends(get_db)
) -> SignalHistoryResponse:
    """Create a new historical signal record."""
    try:
        service = SignalHistoryService(db)
        return service.create_signal_record(signal_data)
    except ValueError as e:
        logger.warning(f"Invalid signal data: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating signal record: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create signal record")


@router.put("/{signal_id}", response_model=SignalHistoryResponse)
async def update_signal_outcome(
    signal_id: int,
    update_data: SignalHistoryUpdate,
    db: Session = Depends(get_db)
) -> SignalHistoryResponse:
    """Update a signal record with outcome data."""
    try:
        service = SignalHistoryService(db)
        return service.update_signal_outcome(signal_id, update_data)
    except ValueError as e:
        logger.warning(f"Signal not found or invalid data: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating signal record: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update signal record")


@router.get("/", response_model=SignalHistoryListResponse)
async def get_signal_history(
    symbol: str = Query(None, description="Filter by stock symbol"),
    signal_type: str = Query(None, description="Filter by signal type (BUY/SELL)"),
    strategy_name: str = Query(None, description="Filter by strategy name"),
    start_date: datetime = Query(None, description="Filter by start date"),
    end_date: datetime = Query(None, description="Filter by end date"),
    is_successful: str = Query(None, description="Filter by success status (SUCCESS/LOSS/PENDING)"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    db: Session = Depends(get_db)
) -> SignalHistoryListResponse:
    """Get filtered signal history with pagination."""
    try:
        filters = SignalHistoryFilter(
            symbol=symbol,
            signal_type=signal_type,
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            is_successful=is_successful,
            offset=offset,
            limit=limit
        )
        
        service = SignalHistoryService(db)
        signals, total_count = service.get_signal_history(filters)
        
        return SignalHistoryListResponse(
            signals=signals,
            total_count=total_count,
            offset=offset,
            limit=limit
        )
    except Exception as e:
        logger.error(f"Error retrieving signal history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve signal history")


@router.get("/effectiveness/{symbol}/{signal_type}", response_model=SignalEffectivenessResponse)
async def get_signal_effectiveness(
    symbol: str,
    signal_type: str,
    period_days: int = Query(90, ge=1, le=365, description="Analysis period in days"),
    db: Session = Depends(get_db)
) -> SignalEffectivenessResponse:
    """Get effectiveness metrics for a specific symbol and signal type."""
    try:
        if signal_type not in ['BUY', 'SELL']:
            raise ValueError("Signal type must be 'BUY' or 'SELL'")
        
        service = SignalHistoryService(db)
        return service.calculate_effectiveness_metrics(symbol, signal_type, period_days)
    except ValueError as e:
        logger.warning(f"Invalid parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating effectiveness metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to calculate effectiveness metrics")


@router.get("/effectiveness/{symbol}", response_model=SignalEffectivenessListResponse)
async def get_all_signal_effectiveness(
    symbol: str,
    period_days: int = Query(90, ge=1, le=365, description="Analysis period in days"),
    db: Session = Depends(get_db)
) -> SignalEffectivenessListResponse:
    """Get effectiveness metrics for all signal types of a symbol."""
    try:
        service = SignalHistoryService(db)
        
        buy_effectiveness = service.calculate_effectiveness_metrics(symbol, 'BUY', period_days)
        sell_effectiveness = service.calculate_effectiveness_metrics(symbol, 'SELL', period_days)
        
        return SignalEffectivenessListResponse(
            effectiveness_metrics=[buy_effectiveness, sell_effectiveness]
        )
    except Exception as e:
        logger.error(f"Error calculating all effectiveness metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to calculate effectiveness metrics")


@router.get("/performance/{symbol}", response_model=SignalPerformanceMetrics)
async def get_performance_metrics(
    symbol: str,
    period_days: int = Query(90, ge=1, le=365, description="Analysis period in days"),
    db: Session = Depends(get_db)
) -> SignalPerformanceMetrics:
    """Get comprehensive performance metrics for a symbol."""
    try:
        service = SignalHistoryService(db)
        return service.get_performance_metrics(symbol, period_days)
    except Exception as e:
        logger.error(f"Error calculating performance metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to calculate performance metrics")


@router.get("/symbols", response_model=List[str])
async def get_tracked_symbols(
    db: Session = Depends(get_db)
) -> List[str]:
    """Get list of symbols that have historical signal data."""
    try:
        from ...models.signal_history import SignalHistory
        
        symbols = db.query(SignalHistory.symbol).distinct().all()
        return [symbol[0] for symbol in symbols]
    except Exception as e:
        logger.error(f"Error retrieving tracked symbols: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve tracked symbols")


@router.get("/strategies", response_model=List[str])
async def get_available_strategies(
    db: Session = Depends(get_db)
) -> List[str]:
    """Get list of available trading strategies."""
    try:
        from ...models.signal_history import SignalHistory
        
        strategies = db.query(SignalHistory.strategy_name).distinct().all()
        return [strategy[0] for strategy in strategies if strategy[0]]
    except Exception as e:
        logger.error(f"Error retrieving available strategies: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve available strategies")


@router.delete("/{signal_id}")
async def delete_signal_record(
    signal_id: int,
    db: Session = Depends(get_db)
) -> dict:
    """Delete a signal history record."""
    try:
        from ...models.signal_history import SignalHistory
        
        signal = db.query(SignalHistory).filter(SignalHistory.id == signal_id).first()
        if not signal:
            raise HTTPException(status_code=404, detail="Signal record not found")
        
        db.delete(signal)
        db.commit()
        
        logger.info(f"Deleted signal record {signal_id}")
        return {"message": "Signal record deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting signal record: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete signal record")