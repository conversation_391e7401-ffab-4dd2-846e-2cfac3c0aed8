import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { LoadingState } from '@/components/ui/LoadingState'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { apiClient, ApiError } from '@/lib/api'
import type { ScreenerItem } from '@trading-agent/shared-types'

export function ScreenerPage() {
  const { t } = useTranslation('screener')
  const [results, setResults] = useState<ScreenerItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const navigate = useNavigate()

  const fetchResults = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const screenerResults = await apiClient.getScreenerResults()
      setResults(screenerResults)
      setLastUpdated(new Date())
    } catch (err) {
      if (err instanceof ApiError) {
        if (err.status === 0) {
          setError(err.message) // Network errors already have user-friendly messages
        } else {
          setError(err.message || t('errors.fetchFailed'))
        }
      } else {
        setError(t('errors.fetchFailed'))
      }
    } finally {
      setLoading(false)
    }
  }

  const handleStockClick = (symbol: string) => {
    navigate(`/analysis?stock=${encodeURIComponent(symbol)}`)
  }

  const handleRetry = () => {
    fetchResults()
  }

  useEffect(() => {
    fetchResults()
  }, [])

  const renderContent = () => {
    if (loading) {
      return (
        <LoadingState 
          message={t('loading.results')} 
          className="justify-center py-8"
        />
      )
    }

    if (error) {
      return (
        <ErrorMessage 
          message={error}
          title={t('errors.loadFailed')}
          onRetry={handleRetry}
        />
      )
    }

    if (results.length === 0) {
      return (
        <p className="text-muted-foreground text-center py-8">
          {t('results.noStocks')}
        </p>
      )
    }

    return (
      <div className="space-y-2">
        {results.map((item) => (
          <div
            key={item.symbol}
            className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <div className="flex-1">
              <button
                onClick={() => handleStockClick(item.symbol)}
                className="text-left w-full group"
                aria-label={t('results.analyzeStock', { symbol: item.symbol, company: item.companyName })}
              >
                <p className="font-medium text-primary group-hover:underline">
                  {item.symbol}
                </p>
                <p className="text-sm text-muted-foreground">
                  {item.companyName}
                </p>
              </button>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleStockClick(item.symbol)}
              aria-label={t('results.analyze', { symbol: item.symbol })}
            >
              {t('results.analyzeButton')}
            </Button>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('description')}
          </p>
          {lastUpdated && (
            <p className="text-xs text-muted-foreground mt-1">
              {t('lastUpdated')}: {lastUpdated.toLocaleString()}
            </p>
          )}
        </div>
        <Button onClick={fetchResults} disabled={loading}>
          {loading ? t('loading.refreshing') : t('actions.refresh')}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('flaggedStocks')}</CardTitle>
        </CardHeader>
        <CardContent>
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  )
}