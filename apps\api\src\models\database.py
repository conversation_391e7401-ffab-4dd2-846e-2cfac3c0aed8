"""SQLAlchemy database models."""


from sqlalchemy import Column, Index, Integer, String

from ..core.database import Base


class ScreenerResult(Base):
    """Database model for screener results."""

    __tablename__ = "screener_results"

    id = Column(Integer, primary_key=True, index=True)
    scan_timestamp = Column(String, nullable=False, index=True)  # ISO 8601 format
    symbol = Column(String, nullable=False)
    company_name = Column(String, nullable=True)

    # Create composite index for performance
    __table_args__ = (
        Index('idx_scan_timestamp', 'scan_timestamp'),
    )

    def __repr__(self) -> str:
        """String representation of ScreenerResult."""
        return f"<ScreenerResult(symbol={self.symbol}, scan_timestamp={self.scan_timestamp})>"
