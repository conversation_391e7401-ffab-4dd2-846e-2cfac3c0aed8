# Trading Agent Web Application

A comprehensive fullstack web application for trading signal analysis and stock screening.

## Architecture

This is a **Jamstack** application built with:
- **Frontend**: React with TypeScript, Vite, Tailwind CSS
- **Backend**: Python FastAPI with SQLite database
- **Monorepo**: Managed with npm workspaces and Turborepo

## Project Structure

```
trading-agent-app/
├── apps/
│   ├── web/          # React frontend application
│   └── api/          # Python FastAPI backend
├── packages/
│   └── shared-types/ # Shared TypeScript types
└── docs/             # Project documentation
```

## Quick Start

### Prerequisites
- Node.js v18+
- Python 3.11+
- Git

### Setup
```bash
npm install
```

### Development
```bash
npm run dev    # Start both frontend and backend
npm test       # Run all tests
```

## Features

- **Single Stock Analysis**: Get trading signals for individual stocks
- **Stock Screener**: View latest flagged stocks meeting criteria
- **Real-time Charts**: Interactive charts with technical indicators

## Technology Stack

| Category | Technology | Purpose |
|----------|-----------|---------|
| Frontend Framework | React 18+ | UI development |
| Frontend Language | TypeScript 5.4+ | Type safety |
| UI Components | Radix UI | Accessible components |
| State Management | Zustand | Simple state management |
| Backend Framework | FastAPI | REST API |
| Backend Language | Python 3.11+ | Core logic |
| Database | SQLite 3+ | Data persistence |
| Testing | Vitest, RTL, Pytest, Playwright | Quality assurance |
| Build Tool | Vite | Fast bundling |
| Styling | Tailwind CSS | Utility-first CSS |

## License

Private project - All rights reserved.