"""Strategy engine for coordinating signal calculations."""

import logging
from datetime import datetime
from typing import Dict, List, Literal, Optional

import pandas as pd

from ...models.stock_data import StockData
from ...models.strategy import ChartData, MACDResult, MagicNineTurnsResult, SignalResult
from src.features.strategy.calculators.bollinger_bands import BollingerBandsCalculator
from src.features.strategy.calculators.moving_average import MovingAverageCalculator
from src.features.strategy.calculators.rsi import RSICalculator
# Using standard Python exceptions

logger = logging.getLogger(__name__)


class StrategyEngine:
    """Orchestrates multiple trading strategies to generate unified signals."""

    def __init__(self) -> None:
        """Initialize the strategy engine."""
        self.min_data_points = 30  # Minimum data points required for analysis
        
        # Strategy registry for dynamic access
        self.strategies = {
            'macd': None,  # Will be initialized when needed
            'magic_nine': None,  # Will be initialized when needed
            'rsi': RSICalculator(),
            'bollinger_bands': BollingerBandsCalculator(),
            'moving_average': MovingAverageCalculator()
        }

    def calculate_signal(self, stock_data: StockData) -> SignalResult:
        """
        Calculate unified trading signal from stock data.

        Args:
            stock_data: Stock data with daily prices

        Returns:
            SignalResult with calculated indicators and signal

        Raises:
            ValueError: If insufficient data or invalid input
        """
        # Validate input data
        self._validate_input_data(stock_data)

        logger.info(f"Calculating signal for {stock_data.symbol} with {len(stock_data.daily_prices)} data points")

        # Import calculators here to avoid circular imports
        from .calculators.macd import MACDCalculator
        from .calculators.magic_nine_turns import MagicNineTurnsCalculator

        try:
            # Initialize calculators
            magic_nine_calc = MagicNineTurnsCalculator()
            macd_calc = MACDCalculator()

            # Calculate indicators
            magic_nine_result = magic_nine_calc.calculate(stock_data.daily_prices)
            macd_result = macd_calc.calculate(stock_data.daily_prices)

            # Determine unified signal
            signal = self._determine_unified_signal(magic_nine_result, macd_result)

            # Build chart data
            chart_data = ChartData(
                daily_prices=stock_data.daily_prices,
                magic_nine_sequence=magic_nine_result.sequence,
                macd_line=macd_result.macd_line,
                signal_line=macd_result.signal_line,
                divergence_points=macd_result.divergence_points
            )

            # Create result
            result = SignalResult(
                symbol=stock_data.symbol,
                last_scan_date=datetime.now().isoformat(),
                signal=signal,
                chart_data=chart_data
            )

            logger.info(f"Signal calculation completed for {stock_data.symbol}: {signal}")
            return result

        except Exception as e:
            logger.error(f"Error calculating signal for {stock_data.symbol}: {str(e)}")
            raise

    def _validate_input_data(self, stock_data: StockData) -> None:
        """
        Validate input stock data.

        Args:
            stock_data: Stock data to validate

        Raises:
            ValueError: If data is invalid or insufficient
        """
        if not stock_data.daily_prices:
            raise ValueError("Stock data cannot be empty")

        if len(stock_data.daily_prices) < self.min_data_points:
            raise ValueError(
                f"Insufficient data points. Need at least {self.min_data_points}, "
                f"got {len(stock_data.daily_prices)}"
            )

        # Validate data is sorted by date
        dates = [price.date for price in stock_data.daily_prices]
        if dates != sorted(dates):
            raise ValueError("Daily prices must be sorted by date in ascending order")

    def calculate_signals(self, data: pd.DataFrame, strategies: Optional[List[str]] = None) -> Dict:
        """Calculate signals using specified or all available strategies.
        
        Args:
            data: DataFrame with OHLCV data and datetime index
            strategies: List of strategy names to use (default: all strategies)
            
        Returns:
            Dictionary containing unified signal and individual strategy results
            
        Raises:
            InvalidParameterError: If data is insufficient or invalid
        """
        if len(data) < self.min_data_points:
            raise InvalidParameterError(
                f"Insufficient data points. Need at least {self.min_data_points}, got {len(data)}"
            )
        
        # Use all strategies if none specified
        if strategies is None:
            strategies = ['macd', 'magic_nine']
        
        # Validate strategies
        invalid_strategies = [s for s in strategies if s not in self.strategies]
        if invalid_strategies:
            raise ValueError(f"Invalid strategies: {invalid_strategies}")
        
        try:
            results = {}
            
            # Calculate signals for each requested strategy
            if 'macd' in strategies:
                from .calculators.macd import MACDCalculator
                macd_calc = MACDCalculator()
                results['macd'] = macd_calc.calculate_macd_with_signal(data)
            
            if 'magic_nine' in strategies:
                from .calculators.magic_nine_turns import MagicNineTurnsCalculator
                magic_nine_calc = MagicNineTurnsCalculator()
                results['magic_nine'] = magic_nine_calc.calculate_turns(data)
            
            if 'rsi' in strategies:
                results['rsi'] = self._calculate_rsi_result(data)
            
            if 'bollinger_bands' in strategies:
                results['bollinger_bands'] = self._calculate_bollinger_result(data)
            
            if 'moving_average' in strategies:
                results['moving_average'] = self._calculate_moving_average_result(data)
            
            # Determine unified signal based on strategy results
            unified_signal = self._determine_unified_signal_multi(results)
            
            return {
                'signal': unified_signal,
                'strategies': results,
                'timestamp': data.index[-1],
                'price': data['close'].iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"Error calculating signals: {str(e)}")
            raise InvalidParameterError(f"Signal calculation failed: {str(e)}")

    def _calculate_rsi_result(self, data: pd.DataFrame) -> Dict:
        """Calculate RSI result in consistent format.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            Dictionary with RSI calculation results
        """
        current_rsi = self.strategies['rsi'].get_current_rsi(data)
        signal_strength = self.strategies['rsi'].get_signal_strength(current_rsi) if current_rsi else "Unknown"
        
        return {
            'rsi_value': current_rsi,
            'signal_strength': signal_strength,
            'overbought_threshold': self.strategies['rsi'].overbought_threshold,
            'oversold_threshold': self.strategies['rsi'].oversold_threshold
        }
    
    def _calculate_bollinger_result(self, data: pd.DataFrame) -> Dict:
        """Calculate Bollinger Bands result in consistent format.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            Dictionary with Bollinger Bands calculation results
        """
        current_position = self.strategies['bollinger_bands'].get_current_position(data)
        
        return {
            'current_position': current_position,
            'period': self.strategies['bollinger_bands'].period,
            'std_dev': self.strategies['bollinger_bands'].std_dev
        }
    
    def _calculate_moving_average_result(self, data: pd.DataFrame) -> Dict:
        """Calculate Moving Average result in consistent format.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            Dictionary with Moving Average calculation results
        """
        current_values = self.strategies['moving_average'].get_current_values(data)
        trend_direction = self.strategies['moving_average'].get_trend_direction(data)
        
        return {
            'current_values': current_values,
            'trend_direction': trend_direction,
            'short_period': self.strategies['moving_average'].short_period,
            'long_period': self.strategies['moving_average'].long_period,
            'ma_type': self.strategies['moving_average'].ma_type
        }

    def _determine_unified_signal(
        self,
        magic_nine_result: MagicNineTurnsResult,
        macd_result: MACDResult
    ) -> Literal['SELL_CANDIDATE', 'HOLD', 'NO_SIGNAL']:
        """
        Determine unified trading signal from indicator results.

        Args:
            magic_nine_result: Magic Nine Turns calculation result
            macd_result: MACD calculation result

        Returns:
            Trading signal: 'SELL_CANDIDATE', 'HOLD', or 'NO_SIGNAL'
        """
        # Check for sell signal conditions
        has_magic_nine_sell = (
            magic_nine_result.is_complete and
            magic_nine_result.direction == 'UP'
        )

        has_macd_divergence = len(macd_result.divergence_points) > 0

        # Unified signal logic
        if has_magic_nine_sell and has_macd_divergence:
            return 'SELL_CANDIDATE'
        elif has_magic_nine_sell or has_macd_divergence:
            return 'HOLD'
        else:
            return 'NO_SIGNAL'
    
    def _determine_unified_signal_multi(self, results: Dict) -> str:
        """Determine unified signal from multiple strategy results.
        
        Args:
            results: Dictionary of strategy results
            
        Returns:
            Unified signal: 'BUY', 'SELL', 'HOLD', or 'NO_SIGNAL'
        """
        buy_signals = 0
        sell_signals = 0
        total_strategies = len(results)
        
        # Analyze each strategy result
        for strategy_name, result in results.items():
            if strategy_name == 'macd':
                signal = result.get('signal', 'HOLD')
                if signal in ['BUY', 'BULLISH_DIVERGENCE']:
                    buy_signals += 1
                elif signal in ['SELL', 'BEARISH_DIVERGENCE']:
                    sell_signals += 1
            
            elif strategy_name == 'magic_nine':
                if result.get('has_sell_signal', False):
                    sell_signals += 2  # Higher weight for Magic Nine
                elif result.get('has_buy_signal', False):
                    buy_signals += 1
            
            elif strategy_name == 'rsi':
                rsi_value = result.get('rsi_value')
                if rsi_value:
                    if rsi_value <= 30:
                        buy_signals += 1
                    elif rsi_value >= 70:
                        sell_signals += 1
            
            elif strategy_name == 'bollinger_bands':
                position = result.get('current_position', {})
                if position:
                    pos_desc = position.get('position', '')
                    if 'Below Lower Band' in pos_desc:
                        buy_signals += 1
                    elif 'Above Upper Band' in pos_desc:
                        sell_signals += 1
            
            elif strategy_name == 'moving_average':
                trend_direction = result.get('trend_direction', '')
                current_values = result.get('current_values', {})
                if current_values:
                    if 'Strong Bullish' in trend_direction or 'Bullish' in trend_direction:
                        buy_signals += 1
                    elif 'Strong Bearish' in trend_direction or 'Bearish' in trend_direction:
                        sell_signals += 1
        
        # Determine unified signal based on vote count
        if sell_signals >= 2:  # Strong sell consensus
            return 'SELL'
        elif buy_signals >= 2:  # Strong buy consensus
            return 'BUY'
        elif sell_signals > buy_signals:
            return 'SELL_CANDIDATE'
        elif buy_signals > sell_signals:
            return 'BUY_CANDIDATE'
        else:
            return 'HOLD'

    def get_available_strategies(self) -> List[str]:
        """Get list of available strategy names.
        
        Returns:
            List of strategy names
        """
        return list(self.strategies.keys())
    
    def get_strategy_descriptions(self) -> Dict[str, str]:
        """Get descriptions of available strategies.
        
        Returns:
            Dictionary mapping strategy names to descriptions
        """
        return {
            'macd': 'Moving Average Convergence Divergence - trend following momentum indicator',
            'magic_nine': 'Magic Nine Turns - pattern recognition for trend reversal points',
            'rsi': 'Relative Strength Index - momentum oscillator for overbought/oversold conditions',
            'bollinger_bands': 'Bollinger Bands - volatility indicator with dynamic support/resistance levels',
            'moving_average': 'Moving Average - trend following indicator using price crossovers and MA crossovers'
        }
    
    def generate_signals_for_strategy(self, data: pd.DataFrame, strategy_name: str) -> List[Dict]:
        """Generate signals for a specific strategy.
        
        Args:
            data: DataFrame with OHLCV data
            strategy_name: Name of the strategy to use
            
        Returns:
            List of signal dictionaries
            
        Raises:
            InvalidParameterError: If strategy is invalid or data insufficient
        """
        if strategy_name not in self.strategies:
            raise InvalidParameterError(f"Unknown strategy: {strategy_name}")
        
        calculator = self.strategies[strategy_name]
        
        # Generate signals using the calculator's generate_signals method
        if hasattr(calculator, 'generate_signals'):
            return calculator.generate_signals(data)
        else:
            raise InvalidParameterError(f"Strategy {strategy_name} does not support signal generation")
