"""Strategy engine for coordinating technical indicator calculations."""

import logging
from datetime import datetime
from typing import Literal

from ...models.stock_data import StockData
from ...models.strategy import ChartData, MACDResult, MagicNineTurnsResult, SignalResult

logger = logging.getLogger(__name__)


class StrategyEngine:
    """Main strategy engine that orchestrates indicator calculations."""

    def __init__(self) -> None:
        """Initialize the strategy engine."""
        self.min_data_points = 30  # Minimum data points required for analysis

    def calculate_signal(self, stock_data: StockData) -> SignalResult:
        """
        Calculate unified trading signal from stock data.

        Args:
            stock_data: Stock data with daily prices

        Returns:
            SignalResult with calculated indicators and signal

        Raises:
            ValueError: If insufficient data or invalid input
        """
        # Validate input data
        self._validate_input_data(stock_data)

        logger.info(f"Calculating signal for {stock_data.symbol} with {len(stock_data.daily_prices)} data points")

        # Import calculators here to avoid circular imports
        from .calculators.macd import MACDCalculator
        from .calculators.magic_nine_turns import MagicNineTurnsCalculator

        try:
            # Initialize calculators
            magic_nine_calc = MagicNineTurnsCalculator()
            macd_calc = MACDCalculator()

            # Calculate indicators
            magic_nine_result = magic_nine_calc.calculate(stock_data.daily_prices)
            macd_result = macd_calc.calculate(stock_data.daily_prices)

            # Determine unified signal
            signal = self._determine_unified_signal(magic_nine_result, macd_result)

            # Build chart data
            chart_data = ChartData(
                daily_prices=stock_data.daily_prices,
                magic_nine_sequence=magic_nine_result.sequence,
                macd_line=macd_result.macd_line,
                signal_line=macd_result.signal_line,
                divergence_points=macd_result.divergence_points
            )

            # Create result
            result = SignalResult(
                symbol=stock_data.symbol,
                last_scan_date=datetime.now().isoformat(),
                signal=signal,
                chart_data=chart_data
            )

            logger.info(f"Signal calculation completed for {stock_data.symbol}: {signal}")
            return result

        except Exception as e:
            logger.error(f"Error calculating signal for {stock_data.symbol}: {str(e)}")
            raise

    def _validate_input_data(self, stock_data: StockData) -> None:
        """
        Validate input stock data.

        Args:
            stock_data: Stock data to validate

        Raises:
            ValueError: If data is invalid or insufficient
        """
        if not stock_data.daily_prices:
            raise ValueError("Stock data cannot be empty")

        if len(stock_data.daily_prices) < self.min_data_points:
            raise ValueError(
                f"Insufficient data points. Need at least {self.min_data_points}, "
                f"got {len(stock_data.daily_prices)}"
            )

        # Validate data is sorted by date
        dates = [price.date for price in stock_data.daily_prices]
        if dates != sorted(dates):
            raise ValueError("Daily prices must be sorted by date in ascending order")

    def _determine_unified_signal(
        self,
        magic_nine_result: MagicNineTurnsResult,
        macd_result: MACDResult
    ) -> Literal['SELL_CANDIDATE', 'HOLD', 'NO_SIGNAL']:
        """
        Determine unified trading signal from indicator results.

        Args:
            magic_nine_result: Magic Nine Turns calculation result
            macd_result: MACD calculation result

        Returns:
            Trading signal: 'SELL_CANDIDATE', 'HOLD', or 'NO_SIGNAL'
        """
        # Check for sell signal conditions
        has_magic_nine_sell = (
            magic_nine_result.is_complete and
            magic_nine_result.direction == 'UP'
        )

        has_macd_divergence = len(macd_result.divergence_points) > 0

        # Unified signal logic
        if has_magic_nine_sell and has_macd_divergence:
            return 'SELL_CANDIDATE'
        elif has_magic_nine_sell or has_macd_divergence:
            return 'HOLD'
        else:
            return 'NO_SIGNAL'
