{"name": "@trading-agent/web", "version": "1.0.0", "description": "React frontend for trading agent application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@trading-agent/shared-types": "*", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "i18next": "^25.3.6", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.540.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.6.1", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "tailwind-merge": "^2.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.4.5", "vite": "^5.0.8", "vitest": "^1.0.4"}}