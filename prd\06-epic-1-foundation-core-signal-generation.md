### Epic 1: Foundation & Core Signal Generation

This epic lays the groundwork for the entire application. It establishes the project structure and backend services, and crucially, it implements and validates the core data pipeline and proprietary trading signal logic. By the end of this epic, we will have a functional, testable API that can deliver the core value proposition, even without a user interface.

#### Story 1.1: Project Scaffolding

**As a** Developer, **I want** a new monorepo project initialized with backend and frontend placeholders, **so that** I have a consistent and clean structure for development.
**Acceptance Criteria:**

1. A new Git repository is created.
2. A monorepo structure is set up with `/apps/backend` and `/apps/frontend` directories.
3. The backend app is initialized with a basic Python (e.g., FastAPI) "hello world" setup.
4. The frontend app is initialized with a placeholder using a framework starter kit (e.g., `create-react-app`).
5. A root-level script (e.g., `npm install`) successfully installs all dependencies for both apps.

#### Story 1.2: Data Ingestion Service

**As a** System, **I want** to fetch historical daily stock data for a specific stock code from `akshare`, **so that** the data is available for the strategy analysis engine.
**Acceptance Criteria:**

1. A backend API endpoint (e.g., `GET /api/data/{stock_code}`) is created.
2. When called, the endpoint successfully fetches historical daily data (e.g., open, high, low, close, volume) for the given stock code from the `akshare` library.
3. The data is correctly parsed and returned in a JSON format.
4. The endpoint handles errors gracefully if the stock code is invalid or `akshare` is unavailable.
5. Unit tests verify the data fetching and parsing logic.

#### Story 1.3: Core Strategy Calculation

**As a** System, **I want** to apply the "Magic Nine Turns" and "MACD with top divergence" calculations to a given set of stock data, **so that** the raw indicator results are generated for signal processing.
**Acceptance Criteria:**

1. A backend service/module is created that takes a time-series of stock data as input.
2. The service correctly calculates the "Magic Nine Turns" sequence according to its technical definition.
3. The service correctly calculates MACD values and programmatically identifies top divergence events.
4. The service outputs the calculated results for both indicators in a structured format.
5. Unit tests for both indicator calculations exist and pass using known inputs and expected outputs.

#### Story 1.4: Unified Signal API Endpoint

**As a** System, **I want** a single API endpoint that orchestrates the data fetching and strategy calculations to return a unified trading signal, **so that** the frontend has a simple, clean interface to consume.
**Acceptance Criteria:**

1. A new backend API endpoint (e.g., `GET /api/signal/{stock_code}`) is created.
2. The endpoint internally calls the data ingestion service and the strategy calculation service.
3. The endpoint returns a simple JSON response indicating the final, combined signal (e.g., `{ "signal": "SELL_CANDIDATE", "details": {...} }`).
4. The JSON response also includes the necessary data points for the frontend to render a chart (e.g., prices, indicator values).
5. Integration tests confirm the end-to-end flow from request to a valid signal response.

***
