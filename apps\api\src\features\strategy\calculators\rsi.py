"""RSI (Relative Strength Index) calculator for trading signals."""

import logging
from typing import Dict, List, Optional

import pandas as pd

from src.shared.exceptions import InvalidParameterError

logger = logging.getLogger(__name__)


class RSICalculator:
    """Calculator for RSI (Relative Strength Index) trading signals.
    
    RSI is a momentum oscillator that measures the speed and magnitude of price changes.
    It oscillates between 0 and 100, with readings above 70 typically considered overbought
    and readings below 30 considered oversold.
    """
    
    def __init__(self, period: int = 14, overbought_threshold: float = 70.0, oversold_threshold: float = 30.0):
        """Initialize RSI calculator.
        
        Args:
            period: Period for RSI calculation (default: 14)
            overbought_threshold: RSI level considered overbought (default: 70)
            oversold_threshold: RSI level considered oversold (default: 30)
        """
        if period < 2:
            raise InvalidParameterError("RSI period must be at least 2")
        if not (0 < oversold_threshold < overbought_threshold < 100):
            raise InvalidParameterError("Invalid RSI thresholds")
            
        self.period = period
        self.overbought_threshold = overbought_threshold
        self.oversold_threshold = oversold_threshold
    
    def calculate_rsi(self, prices: pd.Series) -> pd.Series:
        """Calculate RSI values.
        
        Args:
            prices: Series of closing prices
            
        Returns:
            Series of RSI values
            
        Raises:
            InvalidParameterError: If insufficient data points
        """
        if len(prices) < self.period + 1:
            raise InvalidParameterError(
                f"Need at least {self.period + 1} data points for RSI calculation, got {len(prices)}"
            )
        
        # Calculate price changes
        delta = prices.diff()
        
        # Separate gains and losses
        gains = delta.where(delta > 0, 0)
        losses = -delta.where(delta < 0, 0)
        
        # Calculate average gains and losses using Wilder's smoothing
        avg_gains = gains.rolling(window=self.period, min_periods=self.period).mean()
        avg_losses = losses.rolling(window=self.period, min_periods=self.period).mean()
        
        # Apply Wilder's smoothing for subsequent values
        for i in range(self.period, len(gains)):
            avg_gains.iloc[i] = (avg_gains.iloc[i-1] * (self.period - 1) + gains.iloc[i]) / self.period
            avg_losses.iloc[i] = (avg_losses.iloc[i-1] * (self.period - 1) + losses.iloc[i]) / self.period
        
        # Calculate RS and RSI
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def generate_signals(self, data: pd.DataFrame) -> List[Dict]:
        """Generate RSI-based trading signals.
        
        Args:
            data: DataFrame with 'close' column and datetime index
            
        Returns:
            List of signal dictionaries with timestamp, signal type, and metadata
            
        Raises:
            InvalidParameterError: If data format is invalid or insufficient
        """
        if 'close' not in data.columns:
            raise InvalidParameterError("Data must contain 'close' column")
        
        if len(data) < self.period + 1:
            raise InvalidParameterError(
                f"Need at least {self.period + 1} data points for RSI signals, got {len(data)}"
            )
        
        try:
            # Calculate RSI
            rsi_values = self.calculate_rsi(data['close'])
            
            signals = []
            
            for i in range(1, len(rsi_values)):
                current_rsi = rsi_values.iloc[i]
                previous_rsi = rsi_values.iloc[i-1]
                
                # Skip if RSI values are NaN
                if pd.isna(current_rsi) or pd.isna(previous_rsi):
                    continue
                
                signal_type = None
                confidence = 0.0
                
                # Generate buy signal when RSI crosses above oversold threshold
                if previous_rsi <= self.oversold_threshold and current_rsi > self.oversold_threshold:
                    signal_type = "BUY"
                    # Higher confidence when RSI was deeper in oversold territory
                    confidence = min(0.9, 0.5 + (self.oversold_threshold - min(previous_rsi, 20)) / 20)
                
                # Generate sell signal when RSI crosses below overbought threshold
                elif previous_rsi >= self.overbought_threshold and current_rsi < self.overbought_threshold:
                    signal_type = "SELL"
                    # Higher confidence when RSI was deeper in overbought territory
                    confidence = min(0.9, 0.5 + (max(previous_rsi, 80) - self.overbought_threshold) / 20)
                
                if signal_type:
                    signals.append({
                        'timestamp': data.index[i],
                        'signal_type': signal_type,
                        'strategy': 'RSI',
                        'confidence': confidence,
                        'price': data['close'].iloc[i],
                        'rsi_value': current_rsi,
                        'rsi_previous': previous_rsi,
                        'metadata': {
                            'rsi_period': self.period,
                            'overbought_threshold': self.overbought_threshold,
                            'oversold_threshold': self.oversold_threshold,
                            'rsi_current': float(current_rsi),
                            'rsi_previous': float(previous_rsi)
                        }
                    })
            
            logger.info(f"Generated {len(signals)} RSI signals")
            return signals
            
        except Exception as e:
            logger.error(f"Error generating RSI signals: {str(e)}")
            raise InvalidParameterError(f"Failed to generate RSI signals: {str(e)}")
    
    def get_current_rsi(self, data: pd.DataFrame) -> Optional[float]:
        """Get the current RSI value.
        
        Args:
            data: DataFrame with 'close' column
            
        Returns:
            Current RSI value or None if insufficient data
        """
        try:
            if len(data) < self.period + 1:
                return None
            
            rsi_values = self.calculate_rsi(data['close'])
            current_rsi = rsi_values.iloc[-1]
            
            return float(current_rsi) if not pd.isna(current_rsi) else None
            
        except Exception as e:
            logger.error(f"Error calculating current RSI: {str(e)}")
            return None
    
    def get_signal_strength(self, rsi_value: float) -> str:
        """Determine signal strength based on RSI value.
        
        Args:
            rsi_value: Current RSI value
            
        Returns:
            Signal strength description
        """
        if rsi_value >= 80:
            return "Extremely Overbought"
        elif rsi_value >= self.overbought_threshold:
            return "Overbought"
        elif rsi_value <= 20:
            return "Extremely Oversold"
        elif rsi_value <= self.oversold_threshold:
            return "Oversold"
        elif 40 <= rsi_value <= 60:
            return "Neutral"
        elif rsi_value > 60:
            return "Bullish"
        else:
            return "Bearish"
    
    def generate_signals(self, data: pd.DataFrame) -> List[Dict]:
        """Generate RSI signals for historical data.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            List of signal dictionaries with timestamps
        """
        if len(data) < self.period:
            return []
        
        signals = []
        rsi_values = self.calculate_rsi(data)
        
        for i in range(len(rsi_values)):
            if pd.isna(rsi_values.iloc[i]):
                continue
                
            rsi_value = rsi_values.iloc[i]
            timestamp = data.index[i + self.period - 1]  # Adjust for RSI calculation lag
            price = data['close'].iloc[i + self.period - 1]
            
            signal_type = 'HOLD'
            if rsi_value <= self.oversold_threshold:
                signal_type = 'BUY'
            elif rsi_value >= self.overbought_threshold:
                signal_type = 'SELL'
            
            signals.append({
                'timestamp': timestamp,
                'signal': signal_type,
                'price': price,
                'rsi_value': rsi_value,
                'signal_strength': self.get_signal_strength(rsi_value),
                'strategy': 'rsi'
            })
        
        return signals