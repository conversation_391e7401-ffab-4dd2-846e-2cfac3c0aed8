import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { InteractiveChart } from '../InteractiveChart'
import type { SignalResult } from '@trading-agent/shared-types'

// Mock echarts-for-react
vi.mock('echarts-for-react', () => {
  return {
    default: vi.fn(({ option, onChartReady, ...props }) => {
      // Create a mock DOM element that simulates the chart
      const mockChartInstance = {
        getDom: () => {
          const mockDom = document.createElement('div')
          mockDom.setAttribute = vi.fn()
          mockDom.addEventListener = vi.fn()
          return mockDom
        },
        dispatchAction: vi.fn(),
        resize: vi.fn()
      }
      
      // Simulate chart ready callback
      if (onChartReady) {
        setTimeout(() => onChartReady(mockChartInstance), 0)
      }
      
      return (
        <div
          data-testid="interactive-chart"
          data-chart-options={JSON.stringify(option)}
          {...props}
        />
      )
    })
  }
})

const mockSignalResult: SignalResult = {
  symbol: 'TEST.SZ',
  lastScanDate: '2023-12-01T10:00:00Z',
  signal: 'SELL_CANDIDATE',
  chartData: {
    dailyPrices: [
      {
        date: '2023-11-27',
        open: 100.0,
        high: 105.0,
        low: 95.0,
        close: 102.0,
        volume: 1000000
      },
      {
        date: '2023-11-28',
        open: 102.0,
        high: 108.0,
        low: 101.0,
        close: 106.0,
        volume: 1200000
      },
      {
        date: '2023-11-29',
        open: 106.0,
        high: 110.0,
        low: 104.0,
        close: 108.0,
        volume: 1100000
      }
    ],
    magicNineSequence: [null, 1, 2],
    macdLine: [0.5, 0.8, 1.2],
    signalLine: [0.3, 0.6, 1.0],
    divergencePoints: [
      {
        date: '2023-11-29',
        type: 'TOP'
      }
    ]
  }
}

describe('InteractiveChart', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the chart component', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    expect(chart).toBeInTheDocument()
  })

  it('displays stock symbol in chart title', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    expect(chartOptions.title.text).toBe('TEST.SZ - Stock Analysis')
  })

  it('configures candlestick chart with price data', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    const priceSeriesData = chartOptions.series[0]
    expect(priceSeriesData.name).toBe('Price')
    expect(priceSeriesData.type).toBe('candlestick')
    expect(priceSeriesData.data).toHaveLength(3)
    expect(priceSeriesData.data[0]).toEqual([
      '2023-11-27',
      100.0,
      105.0, // high
      95.0,  // low
      102.0, // close
      1000000
    ])
  })

  it('configures Magic Nine indicator overlay', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    const magicNineSeries = chartOptions.series.find((s: any) => s.name === 'Magic Nine')
    expect(magicNineSeries).toBeDefined()
    expect(magicNineSeries.type).toBe('line')
    expect(magicNineSeries.data).toHaveLength(2) // Filters out null values
  })

  it('configures MACD and Signal line overlays', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    const macdSeries = chartOptions.series.find((s: any) => s.name === 'MACD')
    const signalSeries = chartOptions.series.find((s: any) => s.name === 'Signal')
    
    expect(macdSeries).toBeDefined()
    expect(macdSeries.type).toBe('line')
    expect(macdSeries.data).toHaveLength(3)
    
    expect(signalSeries).toBeDefined()
    expect(signalSeries.type).toBe('line')
    expect(signalSeries.data).toHaveLength(3)
  })

  it('configures divergence point markers', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    const priceSeriesData = chartOptions.series[0]
    expect(priceSeriesData.markPoint.data).toHaveLength(1)
    expect(priceSeriesData.markPoint.data[0].value).toBe('TOP')
  })

  it('enables interactive features with dataZoom', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    expect(chartOptions.dataZoom).toHaveLength(2)
    expect(chartOptions.dataZoom[0].type).toBe('inside')
    expect(chartOptions.dataZoom[1].type).toBe('slider')
  })

  it('includes toolbox with interactive controls', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    expect(chartOptions.toolbox.feature).toHaveProperty('brush')
    expect(chartOptions.toolbox.feature).toHaveProperty('saveAsImage')
    expect(chartOptions.toolbox.feature).toHaveProperty('restore')
    expect(chartOptions.toolbox.feature).toHaveProperty('dataView')
  })

  it('applies custom className', () => {
    render(<InteractiveChart result={mockSignalResult} className="custom-class" />)
    
    const container = screen.getByTestId('interactive-chart').parentElement
    expect(container).toHaveClass('custom-class')
  })

  it('provides accessible description for screen readers', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const description = screen.getByText(/Chart showing stock data for TEST.SZ/)
    expect(description).toBeInTheDocument()
    expect(description).toHaveClass('sr-only')
    expect(description).toHaveAttribute('aria-live', 'polite')
  })

  it('includes keyboard shortcuts information in accessible description', () => {
    render(<InteractiveChart result={mockSignalResult} />)
    
    const description = screen.getByText(/Use keyboard shortcuts: R to reset zoom/)
    expect(description).toBeInTheDocument()
  })

  it('handles empty data gracefully', () => {
    const emptyResult: SignalResult = {
      ...mockSignalResult,
      chartData: {
        dailyPrices: [],
        magicNineSequence: [],
        macdLine: [],
        signalLine: [],
        divergencePoints: []
      }
    }

    render(<InteractiveChart result={emptyResult} />)
    
    const chart = screen.getByTestId('interactive-chart')
    expect(chart).toBeInTheDocument()
    
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    expect(chartOptions.series[0].data).toHaveLength(0)
  })

  it('filters null values from indicator data', () => {
    const resultWithNulls: SignalResult = {
      ...mockSignalResult,
      chartData: {
        ...mockSignalResult.chartData,
        dailyPrices: [
          ...mockSignalResult.chartData.dailyPrices,
          {
            date: '2023-11-30',
            open: 108.0,
            high: 112.0,
            low: 107.0,
            close: 110.0,
            volume: 900000
          }
        ],
        magicNineSequence: [null, null, 5, null],
        macdLine: [1.0, null, 2.0, 3.0],
        signalLine: [null, 0.5, null, 1.5]
      }
    }

    render(<InteractiveChart result={resultWithNulls} />)
    
    const chart = screen.getByTestId('interactive-chart')
    const chartOptions = JSON.parse(chart.getAttribute('data-chart-options') || '{}')
    
    const magicNineSeries = chartOptions.series.find((s: any) => s.name === 'Magic Nine')
    const macdSeries = chartOptions.series.find((s: any) => s.name === 'MACD')
    const signalSeries = chartOptions.series.find((s: any) => s.name === 'Signal')
    
    expect(magicNineSeries.data).toHaveLength(1) // Only non-null value
    expect(macdSeries.data).toHaveLength(3) // Three non-null values
    expect(signalSeries.data).toHaveLength(2) // Two non-null values
  })
})