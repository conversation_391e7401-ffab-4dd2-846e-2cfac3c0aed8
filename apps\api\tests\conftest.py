"""
Test configuration and fixtures.
"""
import pytest
from fastapi.testclient import TestClient

from src.main import app


@pytest.fixture
def client():
    """Test client for FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_stock_data():
    """Mock stock data for testing."""
    return {
        "symbol": "000001.SZ",
        "lastScanDate": "2025-08-17T10:00:00Z",
        "signal": "NO_SIGNAL",
        "chartData": {
            "dailyPrices": [
                {
                    "date": "2025-08-17",
                    "open": 100.0,
                    "high": 105.0,
                    "low": 98.0,
                    "close": 103.0,
                    "volume": 1000000
                }
            ],
            "magicNineSequence": [1, 2, None, None],
            "macdLine": [0.5, 0.6, 0.4],
            "signalLine": [0.4, 0.5, 0.3],
            "divergencePoints": []
        }
    }


@pytest.fixture
def mock_screener_data():
    """Mock screener data for testing."""
    return [
        {
            "symbol": "000001.SZ",
            "companyName": "Ping An Bank Co., Ltd."
        },
        {
            "symbol": "000002.SZ",
            "companyName": "China Vanke Co., Ltd."
        }
    ]
