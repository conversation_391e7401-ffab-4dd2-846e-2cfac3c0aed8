### 4. Technical Assumptions

#### Repository Structure: Monorepo

* **Decision:** A **Monorepo** structure is recommended.
* **Rationale:** This will simplify development for the MVP by keeping the frontend and backend code in a single repository, making it easier to share code (like data types) and manage dependencies.

#### Service Architecture

* **Decision:** A **Monolith** architecture for the backend.
* **Rationale:** A single Python service is sufficient to handle the defined MVP scope and simplifies deployment and development compared to a microservices or serverless approach at this early stage.

#### Testing Requirements

* **Decision:** A minimum of **Unit and Integration tests** are required.
* **Rationale:** Unit tests will ensure the accuracy of the indicator calculations. Integration tests are critical to verify the data pipeline from `akshare` through the strategy engine to the API response.

#### Additional Technical Assumptions and Requests

* **Backend:** The backend will be built using **Python** (e.g., FastAPI or Flask) to leverage the required `akshare` and `Tushare` data libraries.
* **Database:** The MVP will use **SQLite** to minimize setup complexity, with a planned migration to **PostgreSQL** post-MVP.
* **Frontend:** The specific frontend framework (**React, Vue, etc.**) is still to be determined. The application will require a robust charting library (e.g., ECharts, Chart.js).

***
