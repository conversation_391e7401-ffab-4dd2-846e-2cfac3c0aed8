import { create } from 'zustand'
import type { SignalResult } from '@trading-agent/shared-types'

interface AnalysisState {
  currentResult: SignalResult | null
  loading: boolean
  error: string | null
  setResult: (result: SignalResult) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
}

export const useAnalysisStore = create<AnalysisState>((set) => ({
  currentResult: null,
  loading: false,
  error: null,
  setResult: (result) => set({ currentResult: result, error: null }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error, loading: false }),
  clearError: () => set({ error: null }),
}))