import { useMemo, useCallback, useRef } from 'react'
import ReactECharts from 'echarts-for-react'
import type { SignalResult, SignalHistoryRecord } from '@trading-agent/shared-types'

export interface InteractiveChartProps {
  /** SignalResult containing chart data for visualization */
  result: SignalResult
  /** Additional CSS classes for styling */
  className?: string
  /** Optional signal history data to overlay on the chart */
  signalHistory?: SignalHistoryRecord[]
  /** Whether to show signal effectiveness indicators */
  showSignalEffectiveness?: boolean
}

/**
 * Interactive financial chart component for displaying stock price data with technical indicators
 * Supports zooming, panning, and responsive design across desktop and mobile
 * Now includes signal history overlay functionality
 */
export function InteractiveChart({ 
  result, 
  className = '', 
  signalHistory = [], 
  showSignalEffectiveness = true 
}: InteractiveChartProps) {
  const chartRef = useRef<ReactECharts>(null)
  
  // Transform data for ECharts consumption
  const chartData = useMemo(() => {
    const { chartData: data } = result
    
    // Check if chartData exists and has required properties
    if (!data || !data.dailyPrices || !Array.isArray(data.dailyPrices)) {
      return null
    }
    
    const { dailyPrices, magicNineSequence, macdLine, signalLine, divergencePoints } = data
    
    // Prepare price data for candlestick chart
    // ECharts candlestick format: [date, open, high, low, close, volume]
    const priceData = dailyPrices
      .filter(price => {
        // Validate price data
        const { open, high, low, close } = price
        return (
          open > 0 && high > 0 && low > 0 && close > 0 && // All prices must be positive
          high >= Math.max(open, close) && // High must be >= max(open, close)
          low <= Math.min(open, close) // Low must be <= min(open, close)
        )
      })
      .map(price => [
        price.date,
        price.open,
        price.high, // Fixed: high comes before low
        price.low,  // Fixed: low comes after high
        price.close,
        price.volume
      ])
    
    // Prepare indicator data aligned with dates
    const dates = dailyPrices.map(price => price.date)
    
    const magicNineData = dates.map((date, index) => [
      date,
      magicNineSequence[index]
    ]).filter(([, value]) => value !== null)
    
    const macdData = dates.map((date, index) => [
      date,
      macdLine[index]
    ]).filter(([, value]) => value !== null)
    
    const signalData = dates.map((date, index) => [
      date,
      signalLine[index]
    ]).filter(([, value]) => value !== null)
    
    // Prepare divergence markers
    const divergenceData = divergencePoints.map(point => ({
      name: point.type,
      coord: [point.date, 0], // Position will be adjusted based on price scale
      value: point.type,
      itemStyle: {
        color: '#ff6b6b'
      }
    }))
    
    // Process signal history for chart overlay
    const signalMarkers = signalHistory
      .filter(signal => {
        // Only show signals that have dates within our price data range
        const signalDate = signal.triggerDate
        return dates.includes(signalDate)
      })
      .map(signal => {
        // Find the corresponding price for positioning
        const pricePoint = dailyPrices.find(p => p.date === signal.triggerDate)
        const yPosition = pricePoint ? signal.triggerPrice || pricePoint.close : 0
        
        // Determine marker color based on signal type and effectiveness
        let markerColor = '#6b7280' // default gray
        if (showSignalEffectiveness && signal.isSuccessful) {
          markerColor = signal.isSuccessful === 'SUCCESS' ? '#10b981' : '#ef4444'
        } else {
          // Color by signal type if effectiveness not shown
          markerColor = signal.signalType === 'SELL' ? '#ef4444' : '#3b82f6'
        }
        
        return {
          name: `${signal.signalType} Signal`,
          coord: [signal.triggerDate, yPosition],
          value: signal.signalType,
          itemStyle: {
            color: markerColor,
            borderColor: '#ffffff',
            borderWidth: 2
          },
          symbolSize: 8,
          signal: signal // Store full signal data for tooltip
        }
      })
    
    return {
      priceData,
      dates,
      magicNineData,
      macdData,
      signalData,
      divergenceData,
      signalMarkers
    }
  }, [result, signalHistory, showSignalEffectiveness])
  
  // ECharts configuration
  const chartOptions = useMemo(() => {
    // Return empty configuration if chartData is null
    if (!chartData) {
      return {
        title: {
          text: `${result.symbol} - No Data Available`,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: []
      }
    }

    return {
      title: {
        text: `${result.symbol} - Stock Analysis`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#333',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        data: [
          'Price', 
          'Magic Nine', 
          'MACD', 
          'Signal', 
          'Divergence',
          ...(signalHistory.length > 0 ? ['Historical Signals'] : [])
        ],
        top: '10%'
      },
      grid: [
        {
          left: '10%',
          right: '8%',
          height: '50%',
          top: '20%'
        },
        {
          left: '10%',
          right: '8%',
          height: '20%',
          bottom: '10%'
        }
      ],
      xAxis: [
        {
          type: 'category',
          data: chartData.dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        },
        {
          type: 'category',
          gridIndex: 1,
          data: chartData.dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        }
      ],
      yAxis: [
        {
          scale: true,
          splitArea: {
            show: true
          }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 50,
          end: 100
        },
        {
          show: true,
          xAxisIndex: [0, 1],
          type: 'slider',
          top: '85%',
          start: 50,
          end: 100
        }
      ],
      series: [
        {
          name: 'Price',
          type: 'candlestick',
          data: chartData.priceData,
          itemStyle: {
            color: '#14b8a6', // green for up
            color0: '#ef4444', // red for down
            borderColor: '#14b8a6',
            borderColor0: '#ef4444'
          },
          markPoint: {
            label: {
              show: true,
              formatter: function(params: any) {
                if (params.data.signal) {
                  return `${params.data.signal.signalType}`
                }
                return `${params.name}: ${params.value}`
              }
            },
            data: [
              ...chartData.divergenceData,
              ...chartData.signalMarkers
            ],
            tooltip: {
              formatter: function(params: any) {
                if (params.data.signal) {
                  const signal = params.data.signal
                  let tooltip = `<strong>${signal.signalType} Signal</strong><br/>`
                  tooltip += `Date: ${signal.triggerDate}<br/>`
                  tooltip += `Price: $${signal.triggerPrice?.toFixed(2) || 'N/A'}<br/>`
                  if (signal.strategyName) {
                    tooltip += `Strategy: ${signal.strategyName}<br/>`
                  }
                  if (signal.successStatus) {
                    tooltip += `Status: ${signal.successStatus}<br/>`
                  }
                  if (signal.actualReturn !== null && signal.actualReturn !== undefined) {
                    tooltip += `Return: ${(signal.actualReturn * 100).toFixed(2)}%<br/>`
                  }
                  return tooltip
                }
                return `Divergence: ${params.name}`
              }
            }
          }
        },
        {
          name: 'Magic Nine',
          type: 'line',
          data: chartData.magicNineData,
          smooth: false,
          lineStyle: {
            opacity: 0.8,
            width: 2,
            color: '#8b5cf6'
          },
          symbol: 'circle',
          symbolSize: 4,
          showSymbol: true
        },
        {
          name: 'MACD',
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: chartData.macdData,
          smooth: true,
          lineStyle: {
            opacity: 0.8,
            width: 2,
            color: '#3b82f6'
          },
          symbol: 'none'
        },
        {
          name: 'Signal',
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: chartData.signalData,
          smooth: true,
          lineStyle: {
            opacity: 0.8,
            width: 2,
            color: '#f59e0b'
          },
          symbol: 'none'
        }
      ],
      toolbox: {
        feature: {
          brush: {
            type: ['lineX', 'clear']
          },
          saveAsImage: {
            title: 'Save'
          },
          restore: {
            title: 'Reset Zoom'
          },
          dataView: {
            title: 'Data View',
            readOnly: true
          }
        }
      }
    }
  }, [result, chartData])
  
  // Event handlers for accessibility
  const onChartReady = useCallback((chartInstance: any) => {
    // Add keyboard support for accessibility
    const chartDom = chartInstance.getDom()
    chartDom.setAttribute('role', 'img')
    chartDom.setAttribute('aria-label', `Interactive stock chart for ${result.symbol} showing price data and technical indicators`)
    chartDom.setAttribute('tabindex', '0')
    
    // Add keyboard event listeners for accessibility
    chartDom.addEventListener('keydown', (e: KeyboardEvent) => {
      switch (e.key) {
        case 'r':
        case 'R':
          // Reset zoom
          chartInstance.dispatchAction({
            type: 'dataZoom',
            start: 0,
            end: 100
          })
          e.preventDefault()
          break
        case '+':
        case '=':
          // Zoom in
          chartInstance.dispatchAction({
            type: 'dataZoom',
            start: 25,
            end: 75
          })
          e.preventDefault()
          break
        case '-':
          // Zoom out
          chartInstance.dispatchAction({
            type: 'dataZoom',
            start: 0,
            end: 100
          })
          e.preventDefault()
          break
      }
    })
  }, [result.symbol])
  
  const chartStyle = {
    height: '500px',
    width: '100%'
  }
  
  // Handle case when chart data is not available
  if (!chartData) {
    return (
      <div className={`w-full ${className}`}>
        <div className="bg-yellow-50 p-8 rounded-lg border border-yellow-200 text-center">
          <div className="text-yellow-800 mb-2">
            <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              Chart Data Unavailable
            </h3>
            <p className="text-yellow-700">
              Technical chart data is not available for {result.symbol} at the moment.
              The trading signal analysis is still available above.
            </p>
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className={`w-full ${className}`}>
      <ReactECharts
        ref={chartRef}
        option={chartOptions}
        style={chartStyle}
        notMerge={true}
        lazyUpdate={true}
        onChartReady={onChartReady}
        opts={{
          renderer: 'canvas'
        }}
      />
      
      {/* Screen reader accessible data summary */}
      <div className="sr-only" aria-live="polite">
        Chart showing stock data for {result.symbol} with {result.chartData?.dailyPrices?.length || 0} days of price data,
        Magic Nine indicator with {result.chartData?.magicNineSequence?.filter(v => v !== null).length || 0} data points,
        MACD indicator, {result.chartData?.divergencePoints?.length || 0} divergence markers
        {signalHistory.length > 0 && `, and ${signalHistory.length} historical trading signals`}.
        Use keyboard shortcuts: R to reset zoom, + to zoom in, - to zoom out.
      </div>
    </div>
  )
}