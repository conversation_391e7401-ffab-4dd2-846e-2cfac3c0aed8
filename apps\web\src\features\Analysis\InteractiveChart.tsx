import { useMemo, useCallback, useRef } from 'react'
import ReactECharts from 'echarts-for-react'
import type { SignalResult } from '@trading-agent/shared-types'
import type { ComputedSignal } from '@/types/signal'
import { SignalOverlay } from '@/components/Chart/SignalOverlay'

export interface InteractiveChartProps {
  /** The signal result data to display */
  result: SignalResult
  /** Additional CSS classes for styling */
  className?: string
  signals?: ComputedSignal[]
  onSignalHover?: (signal: ComputedSignal | null) => void
  onSignalClick?: (signal: ComputedSignal) => void
}

/**
 * Interactive financial chart component for displaying stock price data with technical indicators
 * Supports zooming, panning, and responsive design across desktop and mobile
 */
export function InteractiveChart({ 
  result, 
  className = '',
  signals = [], 
  onSignalHover,
  onSignalClick 
}: InteractiveChartProps) {
  const chartRef = useRef<ReactECharts>(null)
  
  // Transform data for ECharts consumption
  const chartData = useMemo(() => {
    const { chartData: data } = result
    
    // Check if chartData exists and has required properties
    if (!data || !data.dailyPrices || !Array.isArray(data.dailyPrices)) {
      return null
    }
    
    const { dailyPrices, magicNineSequence, macdLine, signalLine, divergencePoints } = data
    
    // Prepare price data for candlestick chart
    // ECharts candlestick format: [date, open, high, low, close, volume]
    const priceData = dailyPrices
      .filter(price => {
        // Validate price data
        const { open, high, low, close } = price
        return (
          open > 0 && high > 0 && low > 0 && close > 0 && // All prices must be positive
          high >= Math.max(open, close) && // High must be >= max(open, close)
          low <= Math.min(open, close) // Low must be <= min(open, close)
        )
      })
      .map(price => [
        price.date,
        price.open,
        price.high, // Fixed: high comes before low
        price.low,  // Fixed: low comes after high
        price.close,
        price.volume
      ])
    
    // Prepare indicator data aligned with dates
    const dates = dailyPrices.map(price => price.date)
    
    const magicNineData = dates.map((date, index) => [
      date,
      magicNineSequence[index]
    ]).filter(([, value]) => value !== null)
    
    const macdData = dates.map((date, index) => [
      date,
      macdLine[index]
    ]).filter(([, value]) => value !== null)
    
    const signalData = dates.map((date, index) => [
      date,
      signalLine[index]
    ]).filter(([, value]) => value !== null)
    
    // Prepare divergence markers
    const divergenceData = divergencePoints.map(point => ({
      name: point.type,
      coord: [point.date, 0], // Position will be adjusted based on price scale
      value: point.type,
      itemStyle: {
        color: '#ff6b6b'
      }
    }))
    

    
    return {
      priceData,
      dates,
      magicNineData,
      macdData,
      signalData,
      divergenceData
    }
  }, [result])
  
  // ECharts configuration
  const chartOptions = useMemo(() => {
    // Return empty configuration if chartData is null
    if (!chartData) {
      return {
        title: {
          text: `${result.symbol} - No Data Available`,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: []
      }
    }

    return {
      title: {
        text: `${result.symbol} - Stock Analysis`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#333',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        data: [
          'Price', 
          'Magic Nine', 
          'MACD', 
          'Signal', 
          'Divergence'
        ],
        top: '10%'
      },
      grid: [
        {
          left: '10%',
          right: '8%',
          height: '50%',
          top: '20%'
        },
        {
          left: '10%',
          right: '8%',
          height: '20%',
          bottom: '10%'
        }
      ],
      xAxis: [
        {
          type: 'category',
          data: chartData.dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        },
        {
          type: 'category',
          gridIndex: 1,
          data: chartData.dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        }
      ],
      yAxis: [
        {
          scale: true,
          splitArea: {
            show: true
          }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 50,
          end: 100
        },
        {
          show: true,
          xAxisIndex: [0, 1],
          type: 'slider',
          top: '85%',
          start: 50,
          end: 100
        }
      ],
      series: [
        {
          name: 'Price',
          type: 'candlestick',
          data: chartData.priceData,
          itemStyle: {
            color: '#14b8a6', // green for up
            color0: '#ef4444', // red for down
            borderColor: '#14b8a6',
            borderColor0: '#ef4444'
          },
          markPoint: {
            label: {
              show: true,
              formatter: function(params: any) {
                if (params.data.signal) {
                  return `${params.data.signal.signalType}`
                }
                if (params.data.analysisResult) {
                  return `${params.data.analysisResult}`
                }
                return `${params.name}: ${params.value}`
              }
            },
            data: [
              ...chartData.divergenceData,
              // Add analysis result marker at the latest price point
              ...(chartData.priceData.length > 0 ? [{
                name: 'Analysis Result',
                coord: [chartData.dates.length - 1, chartData.priceData[chartData.priceData.length - 1][2]], // Use close price
                value: result.signal,
                analysisResult: result.signal,
                symbol: result.signal === 'SELL_CANDIDATE' ? 'triangle' : 
                        result.signal === 'HOLD' ? 'circle' : 'diamond',
                symbolSize: 20,
                itemStyle: {
                  color: result.signal === 'SELL_CANDIDATE' ? '#ef4444' : 
                         result.signal === 'HOLD' ? '#f59e0b' : '#6b7280'
                }
              }] : [])
            ],
            tooltip: {
              formatter: function(params: any) {
                if (params.data.analysisResult) {
                  return `Analysis Result: ${params.data.analysisResult}`
                }
                return `Divergence: ${params.name}`
              }
            }
          }
        },
        {
          name: 'Magic Nine',
          type: 'line',
          data: chartData.magicNineData,
          smooth: false,
          lineStyle: {
            opacity: 0.8,
            width: 2,
            color: '#8b5cf6'
          },
          symbol: 'circle',
          symbolSize: 4,
          showSymbol: true
        },
        {
          name: 'MACD',
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: chartData.macdData,
          smooth: true,
          lineStyle: {
            opacity: 0.8,
            width: 2,
            color: '#3b82f6'
          },
          symbol: 'none'
        },
        {
          name: 'Signal',
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: chartData.signalData,
          smooth: true,
          lineStyle: {
            opacity: 0.8,
            width: 2,
            color: '#f59e0b'
          },
          symbol: 'none'
        }
      ],
      toolbox: {
        feature: {
          brush: {
            type: ['lineX', 'clear']
          },
          saveAsImage: {
            title: 'Save'
          },
          restore: {
            title: 'Reset Zoom'
          },
          dataView: {
            title: 'Data View',
            readOnly: true
          }
        }
      }
    }
  }, [result, chartData])
  
  // Event handlers for accessibility
  const onChartReady = useCallback((chartInstance: any) => {
    // Add keyboard support for accessibility
    const chartDom = chartInstance.getDom()
    chartDom.setAttribute('role', 'img')
    chartDom.setAttribute('aria-label', `Interactive stock chart for ${result.symbol} showing price data and technical indicators`)
    chartDom.setAttribute('tabindex', '0')
    
    // Add keyboard event listeners for accessibility
    chartDom.addEventListener('keydown', (e: KeyboardEvent) => {
      switch (e.key) {
        case 'r':
        case 'R':
          // Reset zoom
          chartInstance.dispatchAction({
            type: 'dataZoom',
            start: 0,
            end: 100
          })
          e.preventDefault()
          break
        case '+':
        case '=':
          // Zoom in
          chartInstance.dispatchAction({
            type: 'dataZoom',
            start: 25,
            end: 75
          })
          e.preventDefault()
          break
        case '-':
          // Zoom out
          chartInstance.dispatchAction({
            type: 'dataZoom',
            start: 0,
            end: 100
          })
          e.preventDefault()
          break
      }
    })
  }, [result.symbol])
  
  const chartStyle = {
    height: '500px',
    width: '100%'
  }
  
  // Handle case when chart data is not available
  if (!chartData) {
    return (
      <div className={`w-full ${className}`}>
        <div className="bg-yellow-50 p-8 rounded-lg border border-yellow-200 text-center">
          <div className="text-yellow-800 mb-2">
            <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              Chart Data Unavailable
            </h3>
            <p className="text-yellow-700">
              Technical chart data is not available for {result.symbol} at the moment.
              The trading signal analysis is still available above.
            </p>
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className={`w-full ${className}`}>
      <div className="relative">
        <ReactECharts
          ref={chartRef}
          option={chartOptions}
          style={chartStyle}
          notMerge={true}
          lazyUpdate={true}
          onChartReady={onChartReady}
          opts={{
            renderer: 'canvas'
          }}
        />
        
        {/* Signal Overlay */}
        {signals.length > 0 && chartData && (
          <SignalOverlay
            signals={signals}
            chartWidth={800}
            chartHeight={500}
            xScale={(date: Date) => {
              // Convert date to x-coordinate based on chart data range
              const dateStr = date.toISOString().split('T')[0]
              const dateIndex = chartData.dates.indexOf(dateStr)
              if (dateIndex === -1) return 0
              
              // Calculate position within chart area (10% left margin, 8% right margin)
              const chartAreaWidth = 800 * 0.82 // 82% of total width
              const leftMargin = 800 * 0.1 // 10% left margin
              const position = (dateIndex / (chartData.dates.length - 1)) * chartAreaWidth + leftMargin
              return Math.max(leftMargin, Math.min(position, 800 * 0.92))
            }}
            yScale={(price: number) => {
              // Convert price to y-coordinate based on price data range
              if (!chartData.priceData.length) return 250
              
              // Find min and max prices from candlestick data
              let minPrice = Infinity
              let maxPrice = -Infinity
              
              chartData.priceData.forEach(([, , high, low]) => {
                minPrice = Math.min(minPrice, low as number)
                maxPrice = Math.max(maxPrice, high as number)
              })
              
              // Chart area is top 50% (20% from top, 50% height)
              const chartAreaHeight = 500 * 0.5 // 50% of total height
              const topMargin = 500 * 0.2 // 20% from top
              
              // Invert y-coordinate (higher prices at top)
              const priceRange = maxPrice - minPrice
              if (priceRange === 0) return topMargin + chartAreaHeight / 2
              
              const normalizedPrice = (price - minPrice) / priceRange
              const yPosition = topMargin + chartAreaHeight * (1 - normalizedPrice)
              return Math.max(topMargin, Math.min(yPosition, topMargin + chartAreaHeight))
            }}
            onSignalHover={onSignalHover}
            onSignalClick={onSignalClick}
          />
        )}
      </div>
      
      {/* Screen reader accessible data summary */}
      <div className="sr-only" aria-live="polite">
        Chart showing stock data for {result.symbol} with {result.chartData?.dailyPrices?.length || 0} days of price data,
        Magic Nine indicator with {result.chartData?.magicNineSequence?.filter(v => v !== null).length || 0} data points,
        MACD indicator, and {result.chartData?.divergencePoints?.length || 0} divergence markers.
        Use keyboard shortcuts: R to reset zoom, + to zoom in, - to zoom out.
      </div>
    </div>
  )
}