### 5. API Specification

#### REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Trading Agent API
  version: 1.0.0
  description: API for the trading signal analysis and stock screening application.
servers:
  - url: http://localhost:8000
    description: Local Development Server
paths:
  /api/signal/{stock_code}:
    get:
      summary: Get a trading signal for a single stock
      parameters:
        - name: stock_code
          in: path
          required: true
          schema:
            type: string
          description: The stock code to analyze (e.g., 000001.SZ)
      responses:
        '200':
          description: Successful analysis result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SignalResult'
        '404':
          description: Stock code not found or data unavailable
  /api/screener/results:
    get:
      summary: Get the latest list of stocks flagged by the screener
      responses:
        '200':
          description: A list of stocks that meet the signal criteria
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ScreenerItem'
components:
  schemas:
    DailyPrice:
      type: object
      properties:
        date: { type: string, format: date }
        open: { type: number }
        high: { type: number }
        low: { type: number }
        close: { type: number }
        volume: { type: integer }
    SignalResult:
      type: object
      properties:
        symbol: { type: string }
        lastScanDate: { type: string, format: date-time }
        signal: { type: string, enum: [SELL_CANDIDATE, HOLD, NO_SIGNAL] }
        chartData:
          type: object
          properties:
            dailyPrices:
              type: array
              items: { $ref: '#/components/schemas/DailyPrice' }
            magicNineSequence:
              type: array
              items: { type: integer, nullable: true }
            macdLine:
              type: array
              items: { type: number, nullable: true }
            signalLine:
              type: array
              items: { type: number, nullable: true }
            divergencePoints:
              type: array
              items:
                type: object
                properties:
                  date: { type: string, format: date }
                  type: { type: string, enum: [TOP] }
    ScreenerItem:
      type: object
      properties:
        symbol: { type: string }
        companyName: { type: string }
```

***
