{"name": "trading-agent-app", "version": "1.0.0", "description": "A comprehensive trading agent web application", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "install-deps": "npm install && cd apps/api && pip install -r requirements.txt"}, "devDependencies": {"turbo": "^2.0.14", "@playwright/test": "^1.40.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "packageManager": "npm@10.0.0"}