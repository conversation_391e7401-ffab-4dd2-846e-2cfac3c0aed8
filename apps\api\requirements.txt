# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Data handling
pydantic==2.5.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
# sqlite3 - Built-in with Python, no need to install

# Data source
akshare==1.17.35

# Scheduling
apscheduler==3.10.4

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
ruff==0.1.6
mypy==1.7.1

# CORS (already included with FastAPI)
# python-cors - not needed, using fastapi.middleware.cors