"""FastAPI router for data endpoints."""

import logging
from typing import Optional

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse

from ...models.stock_data import StockData
from .service import DataIngestionService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize service
data_service = DataIngestionService()


@router.get("/data/{stock_code}", response_model=StockData)
async def get_stock_data(
    stock_code: str,
    start_date: Optional[str] = Query(
        None,
        description="Start date in YYYY-MM-DD format (defaults to 90 days ago)",
        pattern=r"^\d{4}-\d{2}-\d{2}$"
    ),
    end_date: Optional[str] = Query(
        None,
        description="End date in YYYY-MM-DD format (defaults to today)",
        pattern=r"^\d{4}-\d{2}-\d{2}$"
    )
) -> StockData:
    """
    Fetch historical daily stock data for a specific stock code.

    Args:
        stock_code: The stock symbol/code (e.g., "000001")
        start_date: Start date in YYYY-MM-DD format (optional)
        end_date: End date in YYYY-MM-DD format (optional)

    Returns:
        StockData: Historical daily stock data in JSON format

    Raises:
        HTTPException 404: Stock code not found or no data available
        HTTPException 500: Internal server error or akshare service unavailable
    """
    # Validate stock code format (Chinese stock codes are typically 6 digits)
    if not stock_code or len(stock_code) != 6 or not stock_code.isdigit():
        raise HTTPException(
            status_code=404,
            detail=f"Invalid stock code format: {stock_code}. Expected 6-digit code."
        )

    try:
        logger.info(f"Fetching data for stock code: {stock_code}")

        # Fetch stock data
        stock_data = data_service.fetch_stock_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )

        logger.info(f"Successfully fetched {len(stock_data.daily_prices)} records for {stock_code}")

        return stock_data

    except ValueError as e:
        logger.warning(f"Invalid stock code or data not found: {e}")
        raise HTTPException(
            status_code=404,
            detail=f"Stock code not found or data unavailable: {str(e)}"
        )

    except RuntimeError as e:
        logger.error(f"Service error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Data service unavailable: {str(e)}"
        )

    except Exception as e:
        logger.error(f"Unexpected error fetching stock data: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error occurred while fetching stock data"
        )


@router.get("/data/{stock_code}/health")
async def health_check_stock_data(stock_code: str) -> JSONResponse:
    """
    Health check endpoint to verify if stock data is available.

    Args:
        stock_code: The stock symbol/code to check

    Returns:
        JSON response with availability status
    """
    try:
        # Fetch minimal data (just 1 day) to check availability
        from datetime import datetime

        today = datetime.now().strftime('%Y-%m-%d')

        stock_data = data_service.fetch_stock_data(
            stock_code=stock_code,
            start_date=today,
            end_date=today
        )

        return JSONResponse(
            status_code=200,
            content={
                "status": "available",
                "stock_code": stock_code,
                "data_available": len(stock_data.daily_prices) > 0
            }
        )

    except Exception as e:
        return JSONResponse(
            status_code=404,
            content={
                "status": "unavailable",
                "stock_code": stock_code,
                "data_available": False,
                "error": str(e)
            }
        )
