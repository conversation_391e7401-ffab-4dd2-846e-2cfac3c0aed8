"""Magic Nine Turns (TD Sequential) technical indicator calculator."""

import logging
from typing import Literal, Optional

from ....models.stock_data import DailyPrice
from ....models.strategy import MagicNineTurnsResult

logger = logging.getLogger(__name__)


class MagicNineTurnsCalculator:
    """Calculator for Magic Nine Turns (TD Sequential) indicator."""

    def __init__(self, lookback_period: int = 4):
        """
        Initialize Magic Nine Turns calculator.

        Args:
            lookback_period: Number of periods to look back for comparison (default: 4)
        """
        self.lookback_period = lookback_period
        self.max_count = 9

    def calculate(self, daily_prices: list[DailyPrice]) -> MagicNineTurnsResult:
        """
        Calculate Magic Nine Turns sequence.

        The Magic Nine Turns indicator counts consecutive closes that are:
        - Higher than the close 4 periods ago (UP count)
        - Lower than the close 4 periods ago (DOWN count)

        The count resets when the direction changes and can go up to 9.
        A completed 9-count indicates potential trend exhaustion.

        Args:
            daily_prices: List of daily price data, must be sorted by date

        Returns:
            MagicNineTurnsResult with sequence values and completion status

        Raises:
            ValueError: If insufficient data points
        """
        if len(daily_prices) < self.lookback_period + 1:
            raise ValueError(
                f"Need at least {self.lookback_period + 1} data points for Magic Nine Turns, "
                f"got {len(daily_prices)}"
            )

        logger.debug(f"Calculating Magic Nine Turns for {len(daily_prices)} data points")

        # Initialize sequence array with None values
        sequence: list[Optional[int]] = [None] * len(daily_prices)

        current_count = 0
        current_direction: Literal['UP', 'DOWN', 'NONE'] = 'NONE'
        is_complete = False

        # Start calculation from lookback_period index
        for i in range(self.lookback_period, len(daily_prices)):
            current_close = daily_prices[i].close
            lookback_close = daily_prices[i - self.lookback_period].close

            # Determine direction for this period
            if current_close > lookback_close:
                period_direction = 'UP'
            elif current_close < lookback_close:
                period_direction = 'DOWN'
            else:
                # Equal closes - maintain current direction but don't increment
                sequence[i] = current_count if current_count > 0 else None
                continue

            # Check if direction changed
            if period_direction != current_direction:
                # Direction changed - reset count
                current_direction = period_direction  # type: ignore
                current_count = 1
            else:
                # Same direction - increment count
                current_count = min(current_count + 1, self.max_count)

            # Set sequence value
            sequence[i] = current_count

            # Check for completion (count reached 9)
            if current_count == self.max_count:
                is_complete = True
                logger.debug(f"Magic Nine Turns completed at index {i} with direction {current_direction}")

        result = MagicNineTurnsResult(
            sequence=sequence,
            current_count=current_count,
            is_complete=is_complete,
            direction=current_direction
        )

        logger.debug(f"Magic Nine Turns calculation completed: count={current_count}, direction={current_direction}, complete={is_complete}")
        return result

    def _get_sequence_summary(self, sequence: list[Optional[int]]) -> str:
        """
        Get a summary string of the sequence for debugging.

        Args:
            sequence: The calculated sequence

        Returns:
            Summary string showing last 10 values
        """
        last_values = sequence[-10:] if len(sequence) >= 10 else sequence
        return f"Last values: {last_values}"
