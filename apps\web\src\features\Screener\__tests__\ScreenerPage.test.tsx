import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { ScreenerPage } from '../ScreenerPage'
import * as api from '@/lib/api'

// Mock the API module
vi.mock('@/lib/api', () => {
  // Define ApiError inside the mock factory
  class ApiError extends Error {
    constructor(message: string, public statusCode: number) {
      super(message)
      this.name = 'ApiError'
    }
  }
  
  return {
    apiClient: {
      getScreenerResults: vi.fn(),
    },
    ApiError: ApiError,
  }
})

// Mock react-router-dom navigate
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate
  }
})

const mockScreenerResults = [
  {
    symbol: 'AAPL',
    companyName: 'Apple Inc.'
  },
  {
    symbol: 'GOOGL',
    companyName: 'Alphabet Inc.'
  }
]

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('ScreenerPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('renders loading state initially', () => {
    vi.mocked(api.apiClient.getScreenerResults).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    )

    renderWithRouter(<ScreenerPage />)
    
    expect(screen.getByText('Loading screener results...')).toBeInTheDocument()
  })

  it('displays screener results when API call succeeds', async () => {
    vi.mocked(api.apiClient.getScreenerResults).mockResolvedValue(mockScreenerResults)

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText('Apple Inc.')).toBeInTheDocument()
      expect(screen.getByText('AAPL')).toBeInTheDocument()
      expect(screen.getByText('Alphabet Inc.')).toBeInTheDocument()
      expect(screen.getByText('GOOGL')).toBeInTheDocument()
    })

    // Check that Analyze buttons are present
    const analyzeButtons = screen.getAllByText('Analyze')
    expect(analyzeButtons).toHaveLength(2)
  })

  it('displays last updated timestamp', async () => {
    vi.mocked(api.apiClient.getScreenerResults).mockResolvedValue(mockScreenerResults)

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText(/Last updated:/)).toBeInTheDocument()
    })
  })

  it('handles API error gracefully', async () => {
    const errorMessage = 'Failed to fetch screener results'
    vi.mocked(api.apiClient.getScreenerResults).mockRejectedValue(
      new api.ApiError(errorMessage, 500)
    )

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('handles network error gracefully', async () => {
    vi.mocked(api.apiClient.getScreenerResults).mockRejectedValue(
      new Error('Network error')
    )

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText('An unexpected error occurred while fetching results')).toBeInTheDocument()
    })
  })

  it('displays empty state when no results', async () => {
    vi.mocked(api.apiClient.getScreenerResults).mockResolvedValue([])

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText('No stocks flagged currently')).toBeInTheDocument()
    })
  })

  it('navigates to analysis page when stock code is clicked', async () => {
    vi.mocked(api.apiClient.getScreenerResults).mockResolvedValue(mockScreenerResults)

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText('AAPL')).toBeInTheDocument()
    })

    const stockCodeButton = screen.getByText('AAPL')
    fireEvent.click(stockCodeButton)

    expect(mockNavigate).toHaveBeenCalledWith('/analysis?stock=AAPL')
  })

  it('has refresh functionality', async () => {
    vi.mocked(api.apiClient.getScreenerResults).mockResolvedValue(mockScreenerResults)

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText('AAPL')).toBeInTheDocument()
    })

    // Find and click refresh button
    const refreshButton = screen.getByRole('button', { name: /refresh/i })
    fireEvent.click(refreshButton)

    // API should be called again
    expect(api.apiClient.getScreenerResults).toHaveBeenCalledTimes(2)
  })

  it('displays flagged stocks card title', async () => {
    vi.mocked(api.apiClient.getScreenerResults).mockResolvedValue(mockScreenerResults)

    renderWithRouter(<ScreenerPage />)

    await waitFor(() => {
      expect(screen.getByText('Flagged Stocks')).toBeInTheDocument()
    })
  })
})