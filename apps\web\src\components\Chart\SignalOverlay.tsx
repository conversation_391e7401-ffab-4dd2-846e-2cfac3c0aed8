import React from 'react';
import { SignalMarker } from './SignalMarker';
import { ComputedSignal } from '../../types/signal';

interface SignalOverlayProps {
  signals: ComputedSignal[];
  chartWidth: number;
  chartHeight: number;
  xScale: (date: Date) => number;
  yScale: (price: number) => number;
  onSignalHover?: (signal: ComputedSignal | null) => void;
  onSignalClick?: (signal: ComputedSignal) => void;
  showTooltips?: boolean;
  filterByType?: 'buy' | 'sell' | 'all';
  minConfidence?: number;
}

export const SignalOverlay: React.FC<SignalOverlayProps> = ({
  signals,
  chartWidth,
  chartHeight,
  xScale,
  yScale,
  onSignalHover,
  onSignalClick,
  showTooltips = true,
  filterByType = 'all',
  minConfidence = 0
}) => {
  // Filter signals based on type and confidence
  const filteredSignals = signals.filter(signal => {
    const typeMatch = filterByType === 'all' || signal.signal_type === filterByType;
    const confidenceMatch = signal.confidence >= minConfidence;
    return typeMatch && confidenceMatch;
  });

  const handleMouseEnter = (signal: ComputedSignal) => {
    if (onSignalHover) {
      onSignalHover(signal);
    }
  };

  const handleMouseLeave = () => {
    if (onSignalHover) {
      onSignalHover(null);
    }
  };

  const handleClick = (signal: ComputedSignal) => {
    if (onSignalClick) {
      onSignalClick(signal);
    }
  };

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      width={chartWidth}
      height={chartHeight}
      style={{ zIndex: 10 }}
    >
      <g className="signal-overlay">
        {filteredSignals.map((signal, index) => {
          const signalDate = new Date(signal.timestamp);
          const x = xScale(signalDate);
          const y = yScale(signal.price);

          // Only render if the signal is within the visible chart area
          if (x < 0 || x > chartWidth || y < 0 || y > chartHeight) {
            return null;
          }

          return (
            <SignalMarker
              key={`${signal.symbol}-${signal.timestamp}-${index}`}
              signal={signal}
              x={x}
              y={y}
              onMouseEnter={() => handleMouseEnter(signal)}
              onMouseLeave={handleMouseLeave}
              onClick={() => handleClick(signal)}
              showTooltip={showTooltips}
            />
          );
        })}
      </g>
    </svg>
  );
};

export default SignalOverlay;