import React from 'react';
import { SignalMarker } from './SignalMarker';
import { ComputedSignal } from '../../types/signal';

interface SignalOverlayProps {
  signals: ComputedSignal[];
  chartWidth: number;
  chartHeight: number;
  xScale: (date: Date) => number;
  yScale: (price: number) => number;
  onSignalHover?: (signal: ComputedSignal | null) => void;
  onSignalClick?: (signal: ComputedSignal) => void;
  showTooltips?: boolean;
  filterByType?: 'buy' | 'sell' | 'all';
  minConfidence?: number;
}

export const SignalOverlay: React.FC<SignalOverlayProps> = ({
  signals,
  chartWidth,
  chartHeight,
  xScale,
  yScale,
  onSignalHover,
  onSignalClick,
  showTooltips = true,
  filterByType = 'all',
  minConfidence = 0
}) => {
  // Debug logging for SignalOverlay
  console.log('SignalOverlay - Received signals:', signals)
  console.log('SignalOverlay - Chart dimensions:', { chartWidth, chartHeight })
  console.log('SignalOverlay - Filter options:', { filterByType, minConfidence })
  
  // Filter signals based on type and confidence
  const filteredSignals = signals.filter(signal => {
    const typeMatch = filterByType === 'all' || signal.signal_type === filterByType;
    const confidenceMatch = signal.confidence >= minConfidence;
    return typeMatch && confidenceMatch;
  });
  
  // Debug logging for filtered signals
  console.log('SignalOverlay - Filtered signals:', filteredSignals)
  console.log('SignalOverlay - Filtered signals count:', filteredSignals.length)
  console.log('SignalOverlay - xScale function:', typeof xScale)
  console.log('SignalOverlay - yScale function:', typeof yScale)
  
  // Debug signal positioning with more detail
  filteredSignals.forEach(signal => {
    const signalDate = new Date(signal.timestamp)
    console.log(`Processing signal ${signal.id}:`, {
      timestamp: signal.timestamp,
      parsedDate: signalDate,
      price: signal.price,
      type: signal.signal_type,
      strategy: signal.strategy
    })
    
    const x = xScale(signalDate)
    const y = yScale(signal.price)
    console.log(`Signal ${signal.id} positioning:`, {
      x, y,
      dateString: signalDate.toISOString().split('T')[0]
    })
    
    // Check if signal is within chart bounds
    const isVisible = x >= 0 && x <= chartWidth && y >= 0 && y <= chartHeight
    console.log(`Signal ${signal.id} visibility:`, {
      isVisible,
      xInBounds: x >= 0 && x <= chartWidth,
      yInBounds: y >= 0 && y <= chartHeight,
      bounds: { x: [0, chartWidth], y: [0, chartHeight] },
      actual: { x, y }
    })
  })

  const handleMouseEnter = (signal: ComputedSignal) => {
    if (onSignalHover) {
      onSignalHover(signal);
    }
  };

  const handleMouseLeave = () => {
    if (onSignalHover) {
      onSignalHover(null);
    }
  };

  const handleClick = (signal: ComputedSignal) => {
    if (onSignalClick) {
      onSignalClick(signal);
    }
  };

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      width={chartWidth}
      height={chartHeight}
      style={{ zIndex: 10 }}
    >
      <g className="signal-overlay">
        {filteredSignals.map((signal, index) => {
          const signalDate = new Date(signal.timestamp);
          const x = xScale(signalDate);
          const y = yScale(signal.price);
          
          // Debug logging for signal positioning
          console.log(`SignalOverlay - Signal ${index}:`, {
            signal: signal.signal_type,
            timestamp: signal.timestamp,
            price: signal.price,
            x, y,
            chartWidth, chartHeight,
            withinBounds: x >= 0 && x <= chartWidth && y >= 0 && y <= chartHeight
          });

          // Only render if the signal is within the visible chart area
          if (x < 0 || x > chartWidth || y < 0 || y > chartHeight) {
            console.log(`SignalOverlay - Signal ${index} out of bounds, skipping render`);
            return null;
          }

          return (
            <SignalMarker
              key={`${signal.symbol}-${signal.timestamp}-${index}`}
              signal={signal}
              x={x}
              y={y}
              onMouseEnter={() => handleMouseEnter(signal)}
              onMouseLeave={handleMouseLeave}
              onClick={() => handleClick(signal)}
              showTooltip={showTooltips}
            />
          );
        })}
      </g>
    </svg>
  );
};

export default SignalOverlay;