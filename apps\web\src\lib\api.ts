import type {
  SignalResult,
  ScreenerItem,
  SignalEffectivenessListResponse,
  SignalType,
} from '@trading-agent/shared-types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// Transform snake_case API response to camelCase for frontend
function transformSignalResult(apiResponse: any): SignalResult {
  return {
    symbol: apiResponse.symbol,
    lastScanDate: apiResponse.last_scan_date,
    signal: apiResponse.signal,
    chartData: {
      dailyPrices: apiResponse.chart_data.daily_prices,
      magicNineSequence: apiResponse.chart_data.magic_nine_sequence,
      macdLine: apiResponse.chart_data.macd_line,
      signalLine: apiResponse.chart_data.signal_line,
      divergencePoints: apiResponse.chart_data.divergence_points
    }
  }
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: Response
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      })

      if (!response.ok) {
        let errorMessage = `API request failed: ${response.status} ${response.statusText}`
        
        // Try to get more detailed error info from response
        try {
          const errorData = await response.json()
          if (errorData?.detail) {
            errorMessage = errorData.detail
          }
        } catch {
          // If error response is not JSON, stick with default message
        }
        
        const error = new ApiError(errorMessage, response.status)
        throw error
      }

      const data = await response.json()
      return data
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      // Handle network errors, timeouts, etc.
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new ApiError('Network error. Please check your connection.', 0)
      }
      
      throw new ApiError('An unexpected error occurred.', 0)
    }
  }

  async getSignal(stockCode: string): Promise<SignalResult> {
    const response = await this.request<any>(`/api/signals/${stockCode}`)
    return transformSignalResult(response)
  }

  // Alias for better readability in components
  async getSignalForStock(stockCode: string): Promise<SignalResult> {
    return this.getSignal(stockCode)
  }

  async getScreenerResults(): Promise<ScreenerItem[]> {
    const response = await this.request<ScreenerItem[]>('/api/screener/results');
    return response || [];
  }



  async getSignalEffectiveness(
    filter: { symbol?: string; signalType?: SignalType; periodDays?: number; limit?: number }
  ): Promise<SignalEffectivenessListResponse> {
    const params = new URLSearchParams();
    if (filter.symbol) params.append('symbol', filter.symbol);
    if (filter.signalType) params.append('signal_type', filter.signalType);
    if (filter.periodDays) params.append('period_days', filter.periodDays.toString());
    if (filter.limit) params.append('limit', filter.limit.toString());
    
    const url = `/api/signals/effectiveness${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await this.request<SignalEffectivenessListResponse>(url);
    return response || { effectivenessMetrics: [] };
  }



  async post<T>(endpoint: string, data: any): Promise<{ success: boolean; data?: T; error?: string }> {
    try {
      const response = await this.request<T>(endpoint, {
        method: 'POST',
        body: JSON.stringify(data),
      });
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

}

export const apiClient = new ApiClient()