import { test, expect } from '@playwright/test'

test.describe('Stock Analysis Workflow', () => {
  test('should complete full stock analysis workflow', async ({ page }) => {
    // Start both servers before navigating
    await page.goto('/')
    
    // Navigate to Analysis page
    await expect(page).toHaveTitle(/Trading Agent/)
    await page.getByRole('link', { name: 'Analysis' }).click()
    
    // Check initial page state
    await expect(page.getByRole('heading', { name: 'Stock Analysis' })).toBeVisible()
    await expect(page.getByText('Enter a stock code to get trading signals and technical analysis')).toBeVisible()
    await expect(page.getByRole('heading', { name: 'Analyze Stock' })).toBeVisible()
    await expect(page.getByRole('heading', { name: 'Analysis Results' })).toBeVisible()
    
    // Check form elements
    const stockInput = page.getByRole('textbox', { name: /stock code/i })
    const analyzeButton = page.getByRole('button', { name: 'Analyze stock' })
    
    await expect(stockInput).toBeVisible()
    await expect(analyzeButton).toBeVisible()
    await expect(analyzeButton).toBeDisabled() // Should be disabled when empty
    
    // Enter a stock code
    await stockInput.fill('000001.SZ')
    await expect(analyzeButton).toBeEnabled()
    
    // Submit the form and check loading state
    await analyzeButton.click()
    
    // Check loading state appears
    await expect(page.getByText('Analyzing stock data...')).toBeVisible()
    await expect(analyzeButton).toBeDisabled() // Should be disabled during loading
    
    // Wait for API response and check results
    // This will depend on whether the backend is running and has data
    // We'll check for either success or error response
    
    // Wait for loading to complete (either success or error)
    await expect(page.getByText('Analyzing stock data...')).not.toBeVisible({ timeout: 10000 })
    
    // Check that button is enabled again after completion
    await expect(analyzeButton).toBeEnabled()
    
    // The result will depend on backend availability - check for either results or error
    const hasResults = await page.getByText('000001.SZ').isVisible()
    const hasError = await page.getByRole('alert').isVisible()
    
    expect(hasResults || hasError).toBe(true)
    
    if (hasResults) {
      // If we got results, check they're properly displayed via SignalDisplay component
      await expect(page.getByText('000001.SZ')).toBeVisible()
      await expect(page.getByText('Stock Symbol')).toBeVisible()
      await expect(page.getByText('Last Analysis')).toBeVisible()
      
      // Check that signal is displayed prominently with accessibility
      const signalAlert = page.getByRole('alert')
      await expect(signalAlert).toBeVisible()
      
      // Check that Trading Signal label is present
      await expect(page.getByText('Trading Signal')).toBeVisible()
      
      // Check data summary information is displayed
      await expect(page.getByText('Analysis Data Summary')).toBeVisible()
      await expect(page.getByText(/Daily Prices:/)).toBeVisible()
      await expect(page.getByText(/Divergence Points:/)).toBeVisible()
      await expect(page.getByText(/MACD Data:/)).toBeVisible()
      
      // Check interactive chart is displayed
      await expect(page.getByText('Price Chart & Technical Indicators')).toBeVisible()
      
      // Chart should be rendered with ECharts canvas
      const chartCanvas = page.locator('canvas').first()
      await expect(chartCanvas).toBeVisible()
      
      // Chart should have accessible description
      const chartDescription = page.locator('.sr-only', { hasText: 'Chart showing stock data' })
      await expect(chartDescription).toBeVisible()
    }
    
    if (hasError) {
      // If we got an error, check error handling
      await expect(page.getByRole('alert')).toBeVisible()
      await expect(page.getByText('Analysis Failed')).toBeVisible()
      
      // Check retry button if available
      const retryButton = page.getByRole('button', { name: 'Try Again' })
      if (await retryButton.isVisible()) {
        // Test retry functionality
        await retryButton.click()
        await expect(page.getByRole('alert')).not.toBeVisible()
        await expect(page.getByText('Enter a stock code above to see analysis results')).toBeVisible()
      }
    }
  })

  test('should handle form validation', async ({ page }) => {
    await page.goto('/')
    await page.getByRole('link', { name: 'Analysis' }).click()
    
    const stockInput = page.getByRole('textbox', { name: /stock code/i })
    const analyzeButton = page.getByRole('button', { name: 'Analyze stock' })
    
    // Test invalid format
    await stockInput.fill('invalid')
    await stockInput.press('Enter')
    
    // Should show validation error
    await expect(page.getByText('Please enter a valid stock code format (e.g., 000001.SZ)')).toBeVisible()
    await expect(analyzeButton).toBeDisabled()
    
    // Test valid format
    await stockInput.fill('000001.SZ')
    
    // Error should clear when valid input is entered
    await expect(page.getByText('Please enter a valid stock code format (e.g., 000001.SZ)')).not.toBeVisible()
    await expect(analyzeButton).toBeEnabled()
  })

  test('should handle keyboard navigation', async ({ page }) => {
    await page.goto('/')
    await page.getByRole('link', { name: 'Analysis' }).click()
    
    const stockInput = page.getByRole('textbox', { name: /stock code/i })
    
    // Focus the input and enter a stock code
    await stockInput.focus()
    await stockInput.fill('000001.SZ')
    
    // Submit with Enter key
    await stockInput.press('Enter')
    
    // Should trigger analysis
    await expect(page.getByText('Analyzing stock data...')).toBeVisible()
  })

  test('should navigate between views', async ({ page }) => {
    await page.goto('/')
    
    // Should start on Analysis page
    await expect(page.getByRole('heading', { name: 'Stock Analysis' })).toBeVisible()
    
    // Navigate to Screener
    await page.getByRole('link', { name: 'Screener' }).click()
    await expect(page.getByRole('heading', { name: 'Stock Screener' })).toBeVisible()
    
    // Navigate back to Analysis
    await page.getByRole('link', { name: 'Analysis' }).click()
    await expect(page.getByRole('heading', { name: 'Stock Analysis' })).toBeVisible()
  })

  test('should display signal results with proper visual hierarchy', async ({ page }) => {
    await page.goto('/')
    await page.getByRole('link', { name: 'Analysis' }).click()
    
    const stockInput = page.getByRole('textbox', { name: /stock code/i })
    const analyzeButton = page.getByRole('button', { name: 'Analyze stock' })
    
    // Enter stock code and submit
    await stockInput.fill('000001.SZ')
    await analyzeButton.click()
    
    // Wait for loading to complete
    await expect(page.getByText('Analyzing stock data...')).not.toBeVisible({ timeout: 10000 })
    
    // Check if we got results (depends on backend availability)
    const hasResults = await page.getByText('000001.SZ').isVisible()
    
    if (hasResults) {
      // Test signal display visual hierarchy and accessibility
      const signalAlert = page.getByRole('alert')
      await expect(signalAlert).toBeVisible()
      
      // Check that signal has proper prominence (large text/bold styling)
      const signalHeading = page.getByRole('heading', { level: 2 })
      await expect(signalHeading).toBeVisible()
      
      // Check signal badges are present (should have 2 instances of signal text)
      const tradingSignalText = page.getByText('Trading Signal')
      await expect(tradingSignalText).toBeVisible()
      
      // Check summary details layout
      await expect(page.getByText('Stock Symbol')).toBeVisible()
      await expect(page.getByText('Last Analysis')).toBeVisible()
      
      // Check responsive grid layout elements
      const summaryCards = page.locator('.bg-white.p-4.rounded-lg')
      await expect(summaryCards).toHaveCount(2) // Stock symbol + last analysis cards
      
      // Check data summary section
      await expect(page.getByText('Analysis Data Summary')).toBeVisible()
      const dataSummary = page.locator('.bg-gray-50.p-4.rounded-lg')
      await expect(dataSummary).toBeVisible()
      
      // Check accessibility - signal should have proper ARIA attributes
      await expect(signalAlert).toHaveAttribute('aria-live', 'polite')
      await expect(signalAlert).toHaveAttribute('aria-label', /.+/) // Should have descriptive label
    }
  })

  test('should handle responsive design on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/')
    await page.getByRole('link', { name: 'Analysis' }).click()
    
    // Check that layout adapts to mobile
    await expect(page.getByRole('heading', { name: 'Stock Analysis' })).toBeVisible()
    
    const stockInput = page.getByRole('textbox', { name: /stock code/i })
    const analyzeButton = page.getByRole('button', { name: 'Analyze stock' })
    
    // Form should still be usable on mobile
    await expect(stockInput).toBeVisible()
    await expect(analyzeButton).toBeVisible()
    
    // Submit a request
    await stockInput.fill('000001.SZ')
    await analyzeButton.click()
    
    // Wait for response
    await expect(page.getByText('Analyzing stock data...')).not.toBeVisible({ timeout: 10000 })
    
    // If results are available, check mobile layout
    const hasResults = await page.getByText('000001.SZ').isVisible()
    
    if (hasResults) {
      // Check that signal display works well on mobile
      const signalAlert = page.getByRole('alert')
      await expect(signalAlert).toBeVisible()
      
      // Check that summary cards stack properly on mobile (should use grid-cols-1)
      await expect(page.getByText('Stock Symbol')).toBeVisible()
      await expect(page.getByText('Last Analysis')).toBeVisible()
      
      // Check that chart is responsive on mobile
      if (hasResults) {
        await expect(page.getByText('Price Chart & Technical Indicators')).toBeVisible()
        const chartCanvas = page.locator('canvas').first()
        await expect(chartCanvas).toBeVisible()
        
        // Chart should maintain its aspect ratio on mobile
        const chartContainer = chartCanvas.locator('..')
        await expect(chartContainer).toBeVisible()
      }
    }
  })

  test('should display interactive chart with proper functionality', async ({ page }) => {
    await page.goto('/')
    await page.getByRole('link', { name: 'Analysis' }).click()
    
    const stockInput = page.getByRole('textbox', { name: /stock code/i })
    const analyzeButton = page.getByRole('button', { name: 'Analyze stock' })
    
    // Enter stock code and submit
    await stockInput.fill('000001.SZ')
    await analyzeButton.click()
    
    // Wait for loading to complete
    await expect(page.getByText('Analyzing stock data...')).not.toBeVisible({ timeout: 10000 })
    
    // Check if we got results (depends on backend availability)
    const hasResults = await page.getByText('000001.SZ').isVisible()
    
    if (hasResults) {
      // Check interactive chart section
      await expect(page.getByText('Price Chart & Technical Indicators')).toBeVisible()
      
      // Chart should be rendered
      const chartCanvas = page.locator('canvas').first()
      await expect(chartCanvas).toBeVisible()
      
      // Chart should have proper ARIA attributes for accessibility
      const chartContainer = page.locator('[role="img"]')
      await expect(chartContainer).toHaveAttribute('aria-label', /Interactive stock chart/)
      await expect(chartContainer).toHaveAttribute('tabindex', '0')
      
      // Check that chart container is focusable for keyboard navigation
      await chartContainer.focus()
      
      // Test keyboard shortcuts (if implemented)
      await chartContainer.press('r') // Reset zoom
      
      // Check screen reader accessible description
      const chartDescription = page.locator('.sr-only', { hasText: 'Chart showing stock data' })
      await expect(chartDescription).toBeVisible()
      await expect(chartDescription).toContainText('Use keyboard shortcuts')
      
      // Chart should maintain visibility and not cause layout shifts
      await expect(chartCanvas).toBeVisible()
      
      // Test chart responsiveness by resizing viewport
      await page.setViewportSize({ width: 800, height: 600 })
      await expect(chartCanvas).toBeVisible()
      
      await page.setViewportSize({ width: 1200, height: 800 })
      await expect(chartCanvas).toBeVisible()
    }
  })
})