"""Tests for ScreeningService."""

from datetime import datetime
from unittest.mock import Mock, patch

import pytest

from src.features.screener.screening_service import ScreeningService
from src.models.strategy import ChartData, SignalResult


class TestScreeningService:
    """Test cases for ScreeningService."""

    @pytest.fixture
    def screening_service(self) -> ScreeningService:
        """Create ScreeningService instance for testing."""
        return ScreeningService(max_concurrent=2, rate_limit_delay=0.01)

    @pytest.fixture
    def mock_signal_result_sell(self) -> SignalResult:
        """Mock SignalResult with SELL_CANDIDATE signal."""
        return SignalResult(
            symbol="000001.SZ",
            last_scan_date=datetime.now().isoformat(),
            signal="SELL_CANDIDATE",
            chart_data=ChartData(
                daily_prices=[],
                magic_nine_sequence=[],
                macd_line=[],
                signal_line=[],
                divergence_points=[]
            )
        )

    @pytest.fixture
    def mock_signal_result_hold(self) -> SignalResult:
        """Mock SignalResult with HOLD signal."""
        return SignalResult(
            symbol="000002.SZ",
            last_scan_date=datetime.now().isoformat(),
            signal="HOLD",
            chart_data=ChartData(
                daily_prices=[],
                magic_nine_sequence=[],
                macd_line=[],
                signal_line=[],
                divergence_points=[]
            )
        )

    def test_get_csi_300_stock_list(self, screening_service: ScreeningService) -> None:
        """Test getting CSI 300 stock list."""
        stock_list = screening_service.get_csi_300_stock_list()

        assert isinstance(stock_list, list)
        assert len(stock_list) > 0
        assert "000001.SZ" in stock_list
        assert "600000.SH" in stock_list

    def test_get_company_name(self, screening_service: ScreeningService) -> None:
        """Test company name lookup."""
        # Known stock
        name = screening_service._get_company_name("000001.SZ")
        assert name == "Ping An Bank Co., Ltd."

        # Unknown stock should return stock code
        name = screening_service._get_company_name("UNKNOWN.XX")
        assert name == "UNKNOWN.XX"

    @pytest.mark.asyncio
    async def test_process_single_stock_sell_candidate(self, screening_service: ScreeningService, mock_signal_result_sell: SignalResult) -> None:
        """Test processing single stock that returns SELL_CANDIDATE."""
        with patch.object(screening_service.signal_service, 'get_signal_for_stock', return_value=mock_signal_result_sell):
            result = await screening_service._process_single_stock("000001.SZ")

            assert result is not None
            assert result['symbol'] == "000001.SZ"
            assert result['companyName'] == "Ping An Bank Co., Ltd."

    @pytest.mark.asyncio
    async def test_process_single_stock_hold(self, screening_service: ScreeningService, mock_signal_result_hold: SignalResult) -> None:
        """Test processing single stock that returns HOLD (no signal)."""
        with patch.object(screening_service.signal_service, 'get_signal_for_stock', return_value=mock_signal_result_hold):
            result = await screening_service._process_single_stock("000002.SZ")

            assert result is None

    @pytest.mark.asyncio
    async def test_process_single_stock_value_error(self, screening_service: ScreeningService) -> None:
        """Test processing single stock with ValueError (invalid data)."""
        with patch.object(screening_service.signal_service, 'get_signal_for_stock', side_effect=ValueError("Invalid stock data")):
            result = await screening_service._process_single_stock("INVALID.XX")

            assert result is None

    @pytest.mark.asyncio
    async def test_process_single_stock_runtime_error(self, screening_service: ScreeningService) -> None:
        """Test processing single stock with RuntimeError (service unavailable)."""
        with patch.object(screening_service.signal_service, 'get_signal_for_stock', side_effect=RuntimeError("Service unavailable")):
            result = await screening_service._process_single_stock("000001.SZ")

            assert result is None

    @pytest.mark.asyncio
    async def test_process_single_stock_unexpected_error(self, screening_service: ScreeningService) -> None:
        """Test processing single stock with unexpected error."""
        with patch.object(screening_service.signal_service, 'get_signal_for_stock', side_effect=Exception("Unexpected error")):
            result = await screening_service._process_single_stock("000001.SZ")

            assert result is None

    @pytest.mark.asyncio
    async def test_run_screening_with_mixed_results(self, screening_service: ScreeningService, mock_signal_result_sell: SignalResult, mock_signal_result_hold: SignalResult) -> None:
        """Test running screening with mixed results."""
        stock_list = ["000001.SZ", "000002.SZ", "INVALID.XX"]

        def mock_get_signal(stock_code: str) -> SignalResult:
             if stock_code == "000001.SZ":
                 return mock_signal_result_sell  # Return SELL_CANDIDATE signal
             elif stock_code == "000002.SZ":
                 return mock_signal_result_hold  # Return HOLD signal
             else:
                 raise ValueError("Invalid stock")

        with patch.object(screening_service.signal_service, 'get_signal_for_stock', side_effect=mock_get_signal), \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository context manager
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 1
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            result = await screening_service.run_screening(stock_list)

            assert result['total_stocks_processed'] == 3
            assert result['signals_found'] == 1  # Only 000001.SZ returns SELL_CANDIDATE
            assert result['results_saved'] == 1
            assert result['errors_encountered'] == 0  # Errors are handled gracefully, not counted as exceptions
            assert len(result['results']) == 1
            assert result['results'][0]['symbol'] == "000001.SZ"

    @pytest.mark.asyncio
    async def test_run_screening_default_stock_list(self, screening_service: ScreeningService) -> None:
        """Test running screening with default CSI 300 stock list."""
        with patch.object(screening_service, 'get_csi_300_stock_list', return_value=["000001.SZ"]), \
             patch.object(screening_service.signal_service, 'get_signal_for_stock', side_effect=ValueError("No data")), \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository context manager
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 0
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            result = await screening_service.run_screening()

            assert result['total_stocks_processed'] == 1
            assert result['signals_found'] == 0
            assert result['errors_encountered'] == 0  # Errors are handled gracefully

    @pytest.mark.asyncio
    async def test_run_screening_critical_error(self, screening_service: ScreeningService, mock_signal_result_sell: SignalResult) -> None:
        """Test running screening with critical error in repository."""
        with patch.object(screening_service.signal_service, 'get_signal_for_stock', return_value=mock_signal_result_sell), \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository context manager that raises error during save
            mock_repo = Mock()
            mock_repo.save_screening_results.side_effect = Exception("Database error")
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            with pytest.raises(RuntimeError, match="Screening process failed"):
                await screening_service.run_screening(["000001.SZ"])

    def test_get_latest_screening_results_success(self, screening_service: ScreeningService) -> None:
        """Test getting latest screening results successfully."""
        mock_results = [
            {'symbol': '000001.SZ', 'companyName': 'Ping An Bank Co., Ltd.'}
        ]

        with patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:
            mock_repo = Mock()
            mock_repo.get_latest_screening_results.return_value = mock_results
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            results = screening_service.get_latest_screening_results()

            assert results == mock_results

    def test_get_latest_screening_results_error(self, screening_service: ScreeningService) -> None:
        """Test getting latest screening results with error."""
        with patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:
            mock_repo = Mock()
            mock_repo.get_latest_screening_results.side_effect = Exception("Database error")
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            with pytest.raises(RuntimeError, match="Failed to retrieve screening results"):
                screening_service.get_latest_screening_results()

    def test_get_screening_stats_success(self, screening_service: ScreeningService) -> None:
        """Test getting screening statistics successfully."""
        mock_history = [
            {'scan_timestamp': '2023-01-01T10:00:00', 'result_count': 5}
        ]
        mock_results = [
            {'symbol': '000001.SZ', 'companyName': 'Ping An Bank Co., Ltd.'}
        ]

        with patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:
            mock_repo = Mock()
            mock_repo.get_screening_history.return_value = mock_history
            mock_repo.get_latest_screening_results.return_value = mock_results
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            stats = screening_service.get_screening_stats()

            assert stats['latest_scan_count'] == 1
            assert stats['recent_scans'] == mock_history
            assert stats['total_recent_scans'] == 1
            assert stats['last_scan_timestamp'] == '2023-01-01T10:00:00'

    def test_get_screening_stats_empty_history(self, screening_service: ScreeningService) -> None:
        """Test getting screening statistics with empty history."""
        with patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:
            mock_repo = Mock()
            mock_repo.get_screening_history.return_value = []
            mock_repo.get_latest_screening_results.return_value = []
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            stats = screening_service.get_screening_stats()

            assert stats['latest_scan_count'] == 0
            assert stats['recent_scans'] == []
            assert stats['total_recent_scans'] == 0
            assert stats['last_scan_timestamp'] is None
