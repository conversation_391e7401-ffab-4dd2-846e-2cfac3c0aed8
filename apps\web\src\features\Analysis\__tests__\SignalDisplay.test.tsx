import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { SignalDisplay } from '../SignalDisplay'
import type { SignalResult } from '@trading-agent/shared-types'

const createMockSignalResult = (signal: 'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL'): SignalResult => ({
  symbol: '000001.SZ',
  lastScanDate: '2025-08-18T10:30:00Z',
  signal,
  chartData: {
    dailyPrices: [
      { date: '2025-08-18', open: 10, high: 12, low: 9, close: 11, volume: 1000 },
      { date: '2025-08-17', open: 9, high: 11, low: 8, close: 10, volume: 1200 }
    ],
    magicNineSequence: [1, 2, null, 3],
    macdLine: [0.1, 0.2, 0.15, null],
    signalLine: [0.05, 0.1, 0.12, null],
    divergencePoints: [{ date: '2025-08-18', type: 'TOP' }]
  }
})

describe('SignalDisplay', () => {
  describe('Signal Type Rendering', () => {
    it('renders SELL_CANDIDATE signal with correct styling and accessibility', () => {
      const result = createMockSignalResult('SELL_CANDIDATE')
      render(<SignalDisplay result={result} />)

      // Check main signal display
      const signalElement = screen.getByRole('alert')
      expect(signalElement).toHaveAttribute('aria-label', 'Sell candidate signal - consider selling')
      expect(signalElement).toHaveClass('bg-red-50', 'border-red-200')

      // Check signal text
      expect(screen.getAllByText('SELL CANDIDATE')).toHaveLength(2) // Main title + badge

      // Check warning icon
      expect(screen.getByText('⚠️')).toBeInTheDocument()
    })

    it('renders HOLD signal with correct styling and accessibility', () => {
      const result = createMockSignalResult('HOLD')
      render(<SignalDisplay result={result} />)

      const signalElement = screen.getByRole('alert')
      expect(signalElement).toHaveAttribute('aria-label', 'Hold signal - maintain position')
      expect(signalElement).toHaveClass('bg-green-50', 'border-green-200')

      expect(screen.getAllByText('HOLD')).toHaveLength(2)

      // Check checkmark icon
      expect(screen.getByText('✅')).toBeInTheDocument()
    })

    it('renders NO_SIGNAL with correct styling and accessibility', () => {
      const result = createMockSignalResult('NO_SIGNAL')
      render(<SignalDisplay result={result} />)

      const signalElement = screen.getByRole('alert')
      expect(signalElement).toHaveAttribute('aria-label', 'No signal - no clear trading recommendation')
      expect(signalElement).toHaveClass('bg-gray-50', 'border-gray-200')

      expect(screen.getAllByText('NO SIGNAL')).toHaveLength(2)

      // Check neutral icon
      expect(screen.getByText('➖')).toBeInTheDocument()
    })
  })

  describe('Summary Details Display', () => {
    it('displays stock symbol correctly', () => {
      const result = createMockSignalResult('HOLD')
      render(<SignalDisplay result={result} />)

      expect(screen.getByText('000001.SZ')).toBeInTheDocument()
      expect(screen.getByText('Stock Symbol')).toBeInTheDocument()
    })

    it('formats and displays last scan date correctly', () => {
      const result = createMockSignalResult('HOLD')
      render(<SignalDisplay result={result} />)

      // Check that date is formatted (exact format may vary by locale)
      expect(screen.getByText('Last Analysis')).toBeInTheDocument()
      // The formatted date should contain month, day, and year
      const dateElement = screen.getByText(/Aug.*18.*2025/)
      expect(dateElement).toBeInTheDocument()
    })

    it('displays chart data summary correctly', () => {
      const result = createMockSignalResult('SELL_CANDIDATE')
      render(<SignalDisplay result={result} />)

      expect(screen.getByText('Analysis Data Summary')).toBeInTheDocument()
      expect(screen.getByText('2 data points')).toBeInTheDocument() // dailyPrices.length
      expect(screen.getByText('1')).toBeInTheDocument() // divergencePoints.length
      expect(screen.getByText('3 values')).toBeInTheDocument() // macdLine non-null values
    })
  })

  describe('Accessibility Features', () => {
    it('has proper ARIA attributes', () => {
      const result = createMockSignalResult('SELL_CANDIDATE')
      render(<SignalDisplay result={result} />)

      const alertElement = screen.getByRole('alert')
      expect(alertElement).toHaveAttribute('aria-live', 'polite')
      expect(alertElement).toHaveAttribute('aria-label')
    })

    it('uses semantic HTML structure', () => {
      const result = createMockSignalResult('HOLD')
      render(<SignalDisplay result={result} />)

      // Check for proper heading hierarchy
      const mainHeading = screen.getByRole('heading', { level: 2, name: 'HOLD' })
      expect(mainHeading).toBeInTheDocument()

      const subHeadings = screen.getAllByRole('heading', { level: 3 })
      expect(subHeadings.length).toBeGreaterThan(0)
    })

    it('has proper color contrast classes for accessibility', () => {
      const result = createMockSignalResult('SELL_CANDIDATE')
      const { container } = render(<SignalDisplay result={result} />)

      // Check that contrast-appropriate color classes are used
      const redTextElements = container.querySelectorAll('.text-red-700')
      expect(redTextElements.length).toBeGreaterThan(0)
    })
  })

  describe('Responsive Design', () => {
    it('uses responsive grid classes', () => {
      const result = createMockSignalResult('HOLD')
      const { container } = render(<SignalDisplay result={result} />)

      // Check for responsive grid classes
      const gridElements = container.querySelectorAll('.md\\:grid-cols-2, .md\\:grid-cols-3')
      expect(gridElements.length).toBeGreaterThan(0)
    })
  })

  describe('Data Handling', () => {
    it('handles empty chart data gracefully', () => {
      const result: SignalResult = {
        symbol: 'TEST.SZ',
        lastScanDate: '2025-08-18T10:30:00Z',
        signal: 'NO_SIGNAL',
        chartData: {
          dailyPrices: [],
          magicNineSequence: [],
          macdLine: [],
          signalLine: [],
          divergencePoints: []
        }
      }

      render(<SignalDisplay result={result} />)

      expect(screen.getByText('0 data points')).toBeInTheDocument()
      expect(screen.getByText('0')).toBeInTheDocument() // divergence points
      expect(screen.getByText('0 values')).toBeInTheDocument() // MACD values
    })

    it('handles null values in MACD data correctly', () => {
      const result = createMockSignalResult('HOLD')
      // Modify to have more null values
      result.chartData.macdLine = [0.1, null, null, 0.2, null]

      render(<SignalDisplay result={result} />)

      // Should count only non-null values (2 in this case)
      expect(screen.getByText('2 values')).toBeInTheDocument()
    })
  })
})