"""Scheduler for automated stock screening tasks."""

import asyncio
import logging
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any

from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from .screening_service import ScreeningService

logger = logging.getLogger(__name__)


class ScreeningScheduler:
    """Scheduler for automated stock screening operations."""

    def __init__(self, timezone: str = "Asia/Shanghai"):
        """
        Initialize the screening scheduler.

        Args:
            timezone: Timezone for scheduling (default: Asia/Shanghai for Chinese markets)
        """
        self.scheduler = AsyncIOScheduler(timezone=timezone)
        self.screening_service = ScreeningService()
        self.timezone = timezone
        self.is_running = False
        self.last_job_result: dict[str, Any] | None = None
        self.job_history: list[dict[str, Any]] = []

        # Configure event listeners
        self.scheduler.add_listener(self._job_executed_listener, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._job_error_listener, EVENT_JOB_ERROR)

    def _job_executed_listener(self, event: Any) -> None:
        """Handle successful job execution."""
        logger.info(f"Scheduled screening job completed successfully at {datetime.now()}")

        # Store job execution info
        job_info = {
            'job_id': event.job_id,
            'scheduled_time': event.scheduled_run_time.isoformat(),
            'execution_time': datetime.now().isoformat(),
            'status': 'success',
            'result': self.last_job_result
        }

        self.job_history.append(job_info)

        # Keep only last 10 job results
        if len(self.job_history) > 10:
            self.job_history = self.job_history[-10:]

    def _job_error_listener(self, event: Any) -> None:
        """Handle job execution errors."""
        logger.error(f"Scheduled screening job failed at {datetime.now()}: {event.exception}")

        # Store error info
        error_info = {
            'job_id': event.job_id,
            'scheduled_time': event.scheduled_run_time.isoformat(),
            'execution_time': datetime.now().isoformat(),
            'status': 'error',
            'error': str(event.exception),
            'traceback': event.traceback
        }

        self.job_history.append(error_info)

        # Keep only last 10 job results
        if len(self.job_history) > 10:
            self.job_history = self.job_history[-10:]

    async def _run_screening_job(self) -> dict[str, Any]:
        """Execute the screening job with error handling and retry logic."""
        job_start_time = datetime.now()
        logger.info(f"Starting scheduled screening job at {job_start_time}")

        max_retries = 3
        retry_delay = 60  # seconds

        for attempt in range(max_retries):
            try:
                # Run the screening process
                result = await self.screening_service.run_screening()

                # Store the result
                self.last_job_result = {
                    **result,
                    'job_start_time': job_start_time.isoformat(),
                    'job_end_time': datetime.now().isoformat(),
                    'attempt': attempt + 1,
                    'status': 'completed'
                }

                logger.info(
                    f"Scheduled screening completed successfully: "
                    f"{result['signals_found']} signals found from {result['total_stocks_processed']} stocks"
                )

                return self.last_job_result

            except Exception as e:
                attempt_num = attempt + 1
                logger.error(f"Screening job attempt {attempt_num}/{max_retries} failed: {str(e)}")

                if attempt_num < max_retries:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    # All retries exhausted
                    error_result = {
                        'job_start_time': job_start_time.isoformat(),
                        'job_end_time': datetime.now().isoformat(),
                        'status': 'failed',
                        'error': str(e),
                        'total_attempts': max_retries
                    }

                    self.last_job_result = error_result
                    logger.error(f"Screening job failed after {max_retries} attempts")
                    raise e  # Re-raise the original exception

        # This should never be reached, but added for type safety
        raise RuntimeError("Screening job completed without returning a result")

    def schedule_daily_screening(self, hour: int = 15, minute: int = 30) -> Any:
        """
        Schedule daily screening at specified time.

        Args:
            hour: Hour to run (0-23, default: 15 for 3:30 PM)
            minute: Minute to run (0-59, default: 30)
        """
        trigger = CronTrigger(hour=hour, minute=minute, timezone=self.timezone)

        job = self.scheduler.add_job(
            self._run_screening_job,
            trigger=trigger,
            id='daily_screening',
            name='Daily Stock Screening',
            replace_existing=True,
            max_instances=1,  # Prevent overlapping runs
            coalesce=True,    # Combine multiple missed runs into one
            misfire_grace_time=300  # Allow 5 minutes grace time for missed jobs
        )

        logger.info(f"Daily screening scheduled at {hour:02d}:{minute:02d} {self.timezone}")
        return job

    def schedule_custom_screening(self, cron_expression: str, job_id: str = "custom_screening") -> Any:
        """
        Schedule screening with custom cron expression.

        Args:
            cron_expression: Cron expression (e.g., "0 15 * * 1-5" for weekdays at 3 PM)
            job_id: Unique job identifier
        """
        trigger = CronTrigger.from_crontab(cron_expression, timezone=self.timezone)

        job = self.scheduler.add_job(
            self._run_screening_job,
            trigger=trigger,
            id=job_id,
            name=f'Custom Stock Screening ({job_id})',
            replace_existing=True,
            max_instances=1,
            coalesce=True,
            misfire_grace_time=300
        )

        logger.info(f"Custom screening scheduled with expression: {cron_expression}")
        return job

    async def run_immediate_screening(self) -> dict[str, Any]:
        """Run screening immediately (not scheduled)."""
        logger.info("Running immediate screening job")
        try:
            return await self._run_screening_job()
        except Exception as e:
            # For immediate screening, return error result instead of raising
            # This maintains backward compatibility with existing API consumers
            logger.error(f"Immediate screening failed: {str(e)}")
            return {
                'job_start_time': datetime.now().isoformat(),
                'job_end_time': datetime.now().isoformat(),
                'status': 'failed',
                'error': str(e),
                'total_attempts': 3
            }

    def start_scheduler(self) -> None:
        """Start the scheduler."""
        if not self.is_running:
            self.scheduler.start()
            self.is_running = True
            logger.info("Screening scheduler started")
        else:
            logger.warning("Scheduler is already running")

    def stop_scheduler(self, wait: bool = True) -> None:
        """
        Stop the scheduler.

        Args:
            wait: Whether to wait for currently executing jobs to finish
        """
        if self.is_running:
            self.scheduler.shutdown(wait=wait)
            self.is_running = False
            logger.info("Screening scheduler stopped")
        else:
            logger.warning("Scheduler is not running")

    def get_scheduler_status(self) -> dict[str, Any]:
        """Get current scheduler status and job information."""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })

        return {
            'scheduler_running': self.is_running,
            'timezone': self.timezone,
            'active_jobs': jobs,
            'last_job_result': self.last_job_result,
            'job_history_count': len(self.job_history),
            'recent_jobs': self.job_history[-3:] if self.job_history else []  # Last 3 jobs
        }

    def get_job_history(self, limit: int = 10) -> list[dict[str, Any]]:
        """
        Get job execution history.

        Args:
            limit: Maximum number of history entries to return

        Returns:
            List of job execution records
        """
        return self.job_history[-limit:] if self.job_history else []

    def remove_job(self, job_id: str) -> bool:
        """
        Remove a scheduled job.

        Args:
            job_id: Job identifier to remove

        Returns:
            True if job was removed, False if job not found
        """
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Job {job_id} removed successfully")
            return True
        except Exception as e:
            logger.warning(f"Failed to remove job {job_id}: {str(e)}")
            return False

    @asynccontextmanager
    async def managed_scheduler(self) -> AsyncGenerator["ScreeningScheduler", None]:
        """Context manager for scheduler lifecycle."""
        try:
            self.start_scheduler()
            yield self
        finally:
            self.stop_scheduler()


# Global scheduler instance
_scheduler_instance: ScreeningScheduler | None = None


def get_scheduler() -> ScreeningScheduler:
    """Get global scheduler instance (singleton pattern)."""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = ScreeningScheduler()
    return _scheduler_instance


async def initialize_default_schedule() -> None:
    """Initialize default daily screening schedule."""
    scheduler = get_scheduler()

    # Schedule daily screening at 15:30 (3:30 PM) China time
    # This is after market close for Chinese stock markets
    scheduler.schedule_daily_screening(hour=15, minute=30)

    if not scheduler.is_running:
        scheduler.start_scheduler()

    logger.info("Default screening schedule initialized")
