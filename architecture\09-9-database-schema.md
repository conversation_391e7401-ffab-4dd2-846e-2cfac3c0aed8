### 9. Database Schema

```sql
-- Table to store the results of each daily screener run
CREATE TABLE screener_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scan_timestamp TEXT NOT NULL, -- ISO 8601 format timestamp
    symbol TEXT NOT NULL,
    company_name TEXT
);

-- Index to quickly retrieve the results from the latest scan
CREATE INDEX idx_scan_timestamp ON screener_results (scan_timestamp);
```

***
