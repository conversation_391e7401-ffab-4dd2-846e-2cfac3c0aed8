"""Pydantic schemas for signal computation API."""

from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class SignalComputationRequest(BaseModel):
    """Request model for signal computation."""
    
    stock_code: str = Field(..., description="Stock symbol to analyze")
    start_date: Optional[str] = Field(None, description="Start date in YYYY-MM-DD format")
    end_date: Optional[str] = Field(None, description="End date in YYYY-MM-DD format")
    strategies: Optional[List[str]] = Field(
        default=['macd', 'magic_nine'],
        description="List of strategies to compute signals for"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "stock_code": "000001.SZ",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "strategies": ["macd", "magic_nine"]
            }
        }


class ComputedSignal(BaseModel):
    """Model for a computed trading signal."""
    
    timestamp: str = Field(..., description="Signal timestamp")
    price: float = Field(..., description="Price at signal time")
    signal_type: str = Field(..., description="BUY or SELL signal")
    strategy: str = Field(..., description="Strategy that generated the signal")
    confidence: float = Field(..., description="Signal confidence score (0-1)")
    return_pct: Optional[float] = Field(None, description="Actual return percentage")
    holding_period_days: Optional[int] = Field(None, description="Holding period in days")
    is_successful: Optional[bool] = Field(None, description="Whether signal was profitable")
    forward_returns: Optional[Dict[str, float]] = Field(
        None, description="Forward returns for different periods"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "timestamp": "2024-06-15",
                "price": 12.45,
                "signal_type": "BUY",
                "strategy": "MACD",
                "confidence": 0.75,
                "return_pct": 5.2,
                "holding_period_days": 10,
                "is_successful": True,
                "forward_returns": {
                    "return_5d": 2.1,
                    "return_10d": 5.2,
                    "return_20d": 8.7
                }
            }
        }


class SignalComputationResponse(BaseModel):
    """Response model for signal computation."""
    
    stock_code: str = Field(..., description="Stock symbol analyzed")
    computation_time: str = Field(..., description="When signals were computed")
    total_signals: int = Field(..., description="Total number of signals computed")
    strategies_used: List[str] = Field(..., description="Strategies used for computation")
    signals: List[ComputedSignal] = Field(..., description="List of computed signals")
    
    class Config:
        schema_extra = {
            "example": {
                "stock_code": "000001.SZ",
                "computation_time": "2024-12-20T10:30:00",
                "total_signals": 15,
                "strategies_used": ["macd", "magic_nine"],
                "signals": [
                    {
                        "timestamp": "2024-06-15",
                        "price": 12.45,
                        "signal_type": "BUY",
                        "strategy": "MACD",
                        "confidence": 0.75,
                        "return_pct": 5.2,
                        "holding_period_days": 10,
                        "is_successful": True
                    }
                ]
            }
        }


class EffectivenessMetrics(BaseModel):
    """Model for signal effectiveness metrics."""
    
    success_rate: float = Field(..., description="Percentage of successful signals")
    average_return: float = Field(..., description="Average return percentage")
    max_drawdown: float = Field(..., description="Maximum drawdown percentage")
    total_signals: int = Field(..., description="Total number of signals analyzed")
    profitable_signals: int = Field(..., description="Number of profitable signals")
    average_holding_period: float = Field(..., description="Average holding period in days")
    
    class Config:
        schema_extra = {
            "example": {
                "success_rate": 65.5,
                "average_return": 3.2,
                "max_drawdown": 8.7,
                "total_signals": 42,
                "profitable_signals": 28,
                "average_holding_period": 12.5
            }
        }


class EffectivenessResponse(BaseModel):
    """Response model for effectiveness analysis."""
    
    stock_code: str = Field(..., description="Stock symbol analyzed")
    analysis_period: Dict[str, Optional[str]] = Field(
        ..., description="Start and end dates of analysis"
    )
    computation_time: str = Field(..., description="When analysis was computed")
    metrics: EffectivenessMetrics = Field(..., description="Effectiveness metrics")
    
    class Config:
        schema_extra = {
            "example": {
                "stock_code": "000001.SZ",
                "analysis_period": {
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31"
                },
                "computation_time": "2024-12-20T10:30:00",
                "metrics": {
                    "success_rate": 65.5,
                    "average_return": 3.2,
                    "max_drawdown": 8.7,
                    "total_signals": 42,
                    "profitable_signals": 28,
                    "average_holding_period": 12.5
                }
            }
        }


class StrategyComparison(BaseModel):
    """Model for comparing different strategies."""
    
    strategy_name: str = Field(..., description="Name of the strategy")
    metrics: EffectivenessMetrics = Field(..., description="Strategy effectiveness metrics")
    
    class Config:
        schema_extra = {
            "example": {
                "strategy_name": "MACD",
                "metrics": {
                    "success_rate": 68.2,
                    "average_return": 4.1,
                    "max_drawdown": 6.3,
                    "total_signals": 22,
                    "profitable_signals": 15,
                    "average_holding_period": 11.2
                }
            }
        }


class StrategyComparisonResponse(BaseModel):
    """Response model for strategy comparison."""
    
    stock_code: str = Field(..., description="Stock symbol analyzed")
    analysis_period: Dict[str, Optional[str]] = Field(
        ..., description="Start and end dates of analysis"
    )
    computation_time: str = Field(..., description="When comparison was computed")
    strategies: List[StrategyComparison] = Field(..., description="Strategy comparison results")
    
    class Config:
        schema_extra = {
            "example": {
                "stock_code": "000001.SZ",
                "analysis_period": {
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31"
                },
                "computation_time": "2024-12-20T10:30:00",
                "strategies": [
                    {
                        "strategy_name": "MACD",
                        "metrics": {
                            "success_rate": 68.2,
                            "average_return": 4.1,
                            "total_signals": 22
                        }
                    },
                    {
                        "strategy_name": "Magic Nine",
                        "metrics": {
                            "success_rate": 62.8,
                            "average_return": 2.9,
                            "total_signals": 20
                        }
                    }
                ]
            }
        }


class AvailableStrategiesResponse(BaseModel):
    """Response model for available strategies."""
    
    strategies: List[str] = Field(..., description="List of available strategy names")
    descriptions: Dict[str, str] = Field(..., description="Strategy descriptions")
    
    class Config:
        schema_extra = {
            "example": {
                "strategies": ["macd", "magic_nine", "rsi", "bollinger_bands"],
                "descriptions": {
                    "macd": "Moving Average Convergence Divergence indicator",
                    "magic_nine": "Magic Nine Turns pattern recognition",
                    "rsi": "Relative Strength Index momentum oscillator",
                    "bollinger_bands": "Bollinger Bands volatility indicator"
                }
            }
        }