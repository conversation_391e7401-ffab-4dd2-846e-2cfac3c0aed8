import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'


interface StockInputFormProps {
  onSubmit: (stockCode: string) => void
  loading?: boolean
  disabled?: boolean
  initialValue?: string
}

const STOCK_CODE_PATTERN = /^[0-9]{6}$/

function validateStockCode(code: string, t: (key: string) => string): { isValid: boolean; error?: string } {
  if (!code.trim()) {
    return { isValid: false, error: t('stockInput.validation.required') }
  }
  
  if (!STOCK_CODE_PATTERN.test(code.trim())) {
    return { 
      isValid: false, 
      error: t('stockInput.validation.invalid') 
    }
  }
  
  return { isValid: true }
}

export function StockInputForm({ onSubmit, loading = false, disabled = false, initialValue }: StockInputFormProps) {
  const { t } = useTranslation('analysis')
  const [stockCode, setStockCode] = useState(initialValue || '')
  const [error, setError] = useState<string>('')

  // Update stockCode when initialValue changes
  React.useEffect(() => {
    if (initialValue !== undefined) {
      setStockCode(initialValue)
    }
  }, [initialValue])

  const handleSubmit = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault()
    }
    
    const validation = validateStockCode(stockCode, t)
    if (!validation.isValid) {
      setError(validation.error || t('stockInput.validation.invalid'))
      return
    }
    
    setError('')
    onSubmit(stockCode.trim())
  }

  const handleButtonClick = () => {
    handleSubmit()
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setStockCode(value)
    
    // Clear error when user starts typing
    if (error) {
      setError('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e as any)
    }
  }

  const isFormDisabled = loading || disabled
  const isSubmitDisabled = isFormDisabled

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex space-x-2">
        <div className="flex-1">
          <Input
            type="text"
            placeholder={t('stockInput.placeholder')}
            value={stockCode}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={isFormDisabled}
            aria-label={t('stockInput.label')}
            aria-describedby={error ? "stock-code-error" : undefined}
            aria-invalid={!!error}
            className={error ? "border-destructive focus-visible:ring-destructive" : ""}
          />
          {error && (
            <p 
              id="stock-code-error" 
              className="text-sm text-destructive mt-1"
              role="alert"
              aria-live="polite"
            >
              {error}
            </p>
          )}
        </div>
        <Button 
          type="button"
          onClick={handleButtonClick}
          disabled={isSubmitDisabled}
          aria-label={loading ? t('loading.calculating') : t('stockInput.submit')}
        >
          {loading ? t('loading.calculating') : t('stockInput.submit')}
        </Button>
      </div>
    </form>
  )
}