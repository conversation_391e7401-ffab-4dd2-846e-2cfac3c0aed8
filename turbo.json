{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "lint": {"outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}}}