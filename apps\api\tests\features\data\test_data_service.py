"""Unit tests for DataIngestionService."""

from unittest.mock import patch

import pandas as pd
import pytest

from src.features.data.service import DataIngestionService
from src.models.stock_data import DailyPrice, StockData


class TestDataIngestionService:
    """Test cases for DataIngestionService."""

    @pytest.fixture
    def service(self):
        """Create a DataIngestionService instance for testing."""
        return DataIngestionService()

    @pytest.fixture
    def mock_akshare_data(self):
        """Create mock akshare data for testing."""
        # Create mock DataFrame that matches akshare structure
        data = {
            'col0': ['2024-01-02', '2024-01-03', '2024-01-04'],  # Date
            'col1': ['000001', '000001', '000001'],  # Symbol
            'col2': [9.39, 9.19, 9.19],  # Open
            'col3': [9.21, 9.20, 9.11],  # Close
            'col4': [9.42, 9.22, 9.19],  # High
            'col5': [9.21, 9.15, 9.08],  # Low
            'col6': [1158366, 733610, 864194],  # Volume
            'col7': [1075742252.45, 673673600, 787470100],  # Amount
            'col8': [2.24, 0.76, 1.20],  # Amplitude
            'col9': [-1.92, -0.11, -0.98],  # Change pct
            'col10': [-0.18, -0.01, -0.09],  # Change amount
            'col11': [0.60, 0.38, 0.45]  # Turnover
        }
        return pd.DataFrame(data)

    def test_fetch_stock_data_success(self, service, mock_akshare_data):
        """Test successful stock data fetching."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            result = service.fetch_stock_data('000001', '2024-01-01', '2024-01-05')

            assert isinstance(result, StockData)
            assert result.symbol == '000001'
            assert len(result.daily_prices) == 3

            # Verify first record
            first_price = result.daily_prices[0]
            assert first_price.date == '2024-01-02'
            assert first_price.open == 9.39
            assert first_price.close == 9.21
            assert first_price.high == 9.42
            assert first_price.low == 9.21
            assert first_price.volume == 1158366

    def test_fetch_stock_data_default_dates(self, service, mock_akshare_data):
        """Test stock data fetching with default dates."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data) as mock_ak:
            result = service.fetch_stock_data('000001')

            assert isinstance(result, StockData)
            assert result.symbol == '000001'

            # Verify akshare was called with default date range (90 days)
            call_args = mock_ak.call_args
            assert call_args[1]['symbol'] == '000001'
            assert call_args[1]['period'] == 'daily'
            # Dates should be in YYYYMMDD format
            assert len(call_args[1]['start_date']) == 8
            assert len(call_args[1]['end_date']) == 8

    def test_fetch_stock_data_empty_result(self, service):
        """Test handling of empty akshare result."""
        empty_df = pd.DataFrame()

        with patch('akshare.stock_zh_a_hist', return_value=empty_df):
            with pytest.raises(ValueError, match="No data found for stock code"):
                service.fetch_stock_data('999999')

    def test_fetch_stock_data_none_result(self, service):
        """Test handling of None akshare result."""
        with patch('akshare.stock_zh_a_hist', return_value=None):
            with pytest.raises(ValueError, match="No data found for stock code"):
                service.fetch_stock_data('999999')

    def test_fetch_stock_data_akshare_exception(self, service):
        """Test handling of akshare exceptions."""
        with patch('akshare.stock_zh_a_hist', side_effect=Exception("Network error")):
            with pytest.raises(RuntimeError, match="Data service unavailable"):
                service.fetch_stock_data('000001')

    def test_parse_akshare_data_success(self, service, mock_akshare_data):
        """Test successful parsing of akshare data."""
        result = service._parse_akshare_data(mock_akshare_data)

        assert len(result) == 3
        assert all(isinstance(price, DailyPrice) for price in result)

        # Verify sorting by date
        dates = [price.date for price in result]
        assert dates == sorted(dates)

        # Verify first record values
        first_price = result[0]
        assert first_price.date == '2024-01-02'
        assert first_price.open == 9.39
        assert first_price.close == 9.21
        assert first_price.high == 9.42
        assert first_price.low == 9.21
        assert first_price.volume == 1158366

    def test_parse_akshare_data_validation_error(self, service):
        """Test parsing with invalid data that fails validation."""
        # Create invalid data (negative volume)
        invalid_data = {
            'col0': ['2024-01-02'],  # Date
            'col1': ['000001'],  # Symbol
            'col2': [9.39],  # Open
            'col3': [9.21],  # Close
            'col4': [9.42],  # High
            'col5': [9.21],  # Low
            'col6': [-1000],  # Volume (invalid - negative)
            'col7': [1075742252.45],  # Amount
            'col8': [2.24],  # Amplitude
            'col9': [-1.92],  # Change pct
            'col10': [-0.18],  # Change amount
            'col11': [0.60]  # Turnover
        }
        invalid_df = pd.DataFrame(invalid_data)

        with pytest.raises(ValueError, match="Data parsing failed"):
            service._parse_akshare_data(invalid_df)

    def test_parse_akshare_data_parsing_exception(self, service):
        """Test parsing with malformed data."""
        # Create malformed data
        malformed_data = {
            'col0': ['invalid-date'],  # Invalid date
            'col1': ['000001'],
            'col2': ['not-a-number'],  # Invalid number
            'col3': [9.21],
            'col4': [9.42],
            'col5': [9.21],
            'col6': [1158366],
            'col7': [1075742252.45],
            'col8': [2.24],
            'col9': [-1.92],
            'col10': [-0.18],
            'col11': [0.60]
        }
        malformed_df = pd.DataFrame(malformed_data)

        with pytest.raises(ValueError, match="Data parsing failed"):
            service._parse_akshare_data(malformed_df)

    def test_date_format_conversion(self, service, mock_akshare_data):
        """Test date format conversion from YYYY-MM-DD to YYYYMMDD."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data) as mock_ak:
            service.fetch_stock_data('000001', '2024-01-01', '2024-01-05')

            call_args = mock_ak.call_args
            # Verify dates were converted to YYYYMMDD format
            assert call_args[1]['start_date'] == '20240101'
            assert call_args[1]['end_date'] == '20240105'

    @pytest.mark.parametrize("stock_code,start_date,end_date", [
        ('000001', '2024-01-01', '2024-01-05'),
        ('000002', '2023-12-01', '2023-12-31'),
        ('600000', '2024-06-01', '2024-06-30'),
    ])
    def test_fetch_stock_data_various_inputs(self, service, mock_akshare_data, stock_code, start_date, end_date):
        """Test stock data fetching with various valid inputs."""
        with patch('akshare.stock_zh_a_hist', return_value=mock_akshare_data):
            result = service.fetch_stock_data(stock_code, start_date, end_date)

            assert isinstance(result, StockData)
            assert result.symbol == stock_code
            assert len(result.daily_prices) == 3
