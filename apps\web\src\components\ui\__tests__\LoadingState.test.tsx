import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { LoadingState } from '../LoadingState'

describe('LoadingState', () => {
  it('renders with default props', () => {
    render(<LoadingState />)
    
    const loadingState = screen.getByRole('status')
    expect(loadingState).toBeInTheDocument()
    expect(loadingState).toHaveAttribute('aria-label', 'Loading...')
    expect(loadingState).toHaveAttribute('aria-live', 'polite')
    
    // Find the visible loading text (not the sr-only one)
    const visibleText = screen.getByText('Loading...', { selector: '.text-sm' })
    expect(visibleText).toBeInTheDocument()
  })

  it('renders with custom message', () => {
    render(<LoadingState message="Analyzing stock data..." />)
    
    const loadingState = screen.getByRole('status')
    expect(loadingState).toHaveAttribute('aria-label', 'Analyzing stock data...')
    expect(screen.getByText('Analyzing stock data...')).toBeInTheDocument()
  })

  it('passes size prop to LoadingSpinner', () => {
    render(<LoadingState size="lg" />)
    
    // Check that the spinner has the large size class
    const spinnerContainer = screen.getByRole('status')
    const spinner = spinnerContainer.querySelector('.animate-spin')
    expect(spinner).toHaveClass('h-8 w-8')
  })

  it('applies custom className', () => {
    render(<LoadingState className="justify-center" />)
    
    expect(screen.getByRole('status')).toHaveClass('justify-center')
  })

  it('has proper accessibility attributes', () => {
    const message = "Processing your request..."
    render(<LoadingState message={message} />)
    
    const loadingState = screen.getByRole('status')
    expect(loadingState).toHaveAttribute('aria-live', 'polite')
    expect(loadingState).toHaveAttribute('aria-label', message)
  })
})