### 4. Data Models

#### StockData

* **Purpose:** To hold the raw, time-series historical data for a single stock, fetched from the `akshare` data source.
* **TypeScript Interface**
  ```typescript
  interface DailyPrice {
    date: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
  }

  interface StockData {
    symbol: string;
    dailyPrices: DailyPrice[];
  }
  ```

#### SignalResult

* **Purpose:** To encapsulate the complete, processed analysis result for a single stock.
* **TypeScript Interface**
  ```typescript
  type Signal = 'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL';

  interface SignalResult {
    symbol: string;
    lastScanDate: string;
    signal: Signal;
    chartData: {
      dailyPrices: DailyPrice[];
      magicNineSequence: (number | null)[];
      macdLine: (number | null)[];
      signalLine: (number | null)[];
      divergencePoints: { date: string; type: 'TOP' }[];
    }
  }
  ```

***
