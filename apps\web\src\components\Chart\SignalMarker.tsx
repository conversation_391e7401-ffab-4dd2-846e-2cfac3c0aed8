import React, { useState } from 'react';
import { ComputedSignal } from '../../types/signal';
import { SignalTooltip } from './SignalTooltip';

interface SignalMarkerProps {
  signal: ComputedSignal;
  x: number;
  y: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onClick?: () => void;
  showTooltip?: boolean;
  size?: number;
}

export const SignalMarker: React.FC<SignalMarkerProps> = ({
  signal,
  x,
  y,
  onMouseEnter,
  onMouseLeave,
  onClick,
  showTooltip = true,
  size = 8
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const isBuySignal = signal.signal_type === 'buy';
  const isSellSignal = signal.signal_type === 'sell';

  // Color based on signal type and confidence
  const getSignalColor = () => {
    const baseColor = isBuySignal ? '#10B981' : isSellSignal ? '#EF4444' : '#6B7280';
    const opacity = Math.max(0.6, signal.confidence);
    return baseColor + Math.round(opacity * 255).toString(16).padStart(2, '0');
  };

  // Shape based on signal type
  const renderSignalShape = () => {
    const color = getSignalColor();
    const strokeColor = isBuySignal ? '#059669' : isSellSignal ? '#DC2626' : '#4B5563';
    const strokeWidth = isHovered ? 2 : 1;
    const currentSize = isHovered ? size * 1.2 : size;

    if (isBuySignal) {
      // Triangle pointing up for buy signals
      const points = [
        [x, y - currentSize],
        [x - currentSize * 0.8, y + currentSize * 0.5],
        [x + currentSize * 0.8, y + currentSize * 0.5]
      ].map(point => point.join(',')).join(' ');

      return (
        <polygon
          points={points}
          fill={color}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          className="transition-all duration-200"
        />
      );
    } else if (isSellSignal) {
      // Triangle pointing down for sell signals
      const points = [
        [x, y + currentSize],
        [x - currentSize * 0.8, y - currentSize * 0.5],
        [x + currentSize * 0.8, y - currentSize * 0.5]
      ].map(point => point.join(',')).join(' ');

      return (
        <polygon
          points={points}
          fill={color}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          className="transition-all duration-200"
        />
      );
    } else {
      // Circle for neutral/other signals
      return (
        <circle
          cx={x}
          cy={y}
          r={currentSize}
          fill={color}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          className="transition-all duration-200"
        />
      );
    }
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
    if (onMouseEnter) {
      onMouseEnter();
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (onMouseLeave) {
      onMouseLeave();
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClick) {
      onClick();
    }
  };

  return (
    <g className="signal-marker">
      {renderSignalShape()}
      
      {/* Invisible larger area for easier mouse interaction */}
      <circle
        cx={x}
        cy={y}
        r={size * 2}
        fill="transparent"
        className="pointer-events-auto cursor-pointer"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
      />

      {/* Tooltip */}
      {showTooltip && isHovered && (
        <SignalTooltip
          signal={signal}
          x={x}
          y={y}
        />
      )}
    </g>
  );
};

export default SignalMarker;