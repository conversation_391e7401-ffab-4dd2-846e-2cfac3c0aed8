"""Custom exception classes for the trading agent API."""


class TradingAgentError(Exception):
    """Base exception class for trading agent errors."""
    pass


class InvalidParameterError(TradingAgentError):
    """Raised when invalid parameters are provided to a function or method."""
    pass


class StockNotFoundError(TradingAgentError):
    """Raised when a requested stock is not found or not available."""
    pass


class DataIngestionError(TradingAgentError):
    """Raised when there are issues with data ingestion or retrieval."""
    pass


class ServiceError(TradingAgentError):
    """Raised when there are general service-level errors."""
    pass


class StrategyError(TradingAgentError):
    """Raised when there are issues with strategy calculation or execution."""
    pass


class ComputationError(TradingAgentError):
    """Raised when there are issues with signal computation."""
    pass