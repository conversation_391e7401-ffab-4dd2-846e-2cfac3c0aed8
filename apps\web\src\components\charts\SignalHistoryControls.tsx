import React, { useState, useCallback, memo, useMemo } from 'react';
import { Filter, Calendar, TrendingUp, TrendingDown, RotateCcw } from 'lucide-react';
import {
  SignalHistoryFilter,
} from '@trading-agent/shared-types';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { useDebounce, withPerformanceTracking } from '../../utils/performance';

interface SignalHistoryControlsProps {
  filter: SignalHistoryFilter;
  onFilterChange: (filter: SignalHistoryFilter) => void;
  onRefresh?: () => void;
  isLoading?: boolean;
  totalCount?: number;
}

const SignalHistoryControls: React.FC<SignalHistoryControlsProps> = memo(({
  filter,
  onFilterChange,
  onRefresh,
  isLoading = false,
  totalCount,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Debounced filter change for text inputs
  const debouncedFilterChange = useDebounce(
    (key: keyof SignalHistoryFilter, value: any) => {
      const newFilter = { ...filter };
      if (value === '' || value === undefined) {
        delete newFilter[key];
      } else {
        newFilter[key] = value;
      }
      onFilterChange(newFilter);
    },
    300
  );

  const handleFilterChange = useCallback(
    (key: keyof SignalHistoryFilter, value: any) => {
      const newFilter = { ...filter };
      if (value === '' || value === undefined) {
        delete newFilter[key];
      } else {
        newFilter[key] = value;
      }
      onFilterChange(newFilter);
    },
    [filter, onFilterChange]
  );

  // Use debounced version for text inputs
  const handleTextFilterChange = useCallback(
    (key: keyof SignalHistoryFilter, value: string) => {
      debouncedFilterChange(key, value);
    },
    [debouncedFilterChange]
  );

  const handleReset = useCallback(() => {
    onFilterChange({ limit: 50, offset: 0 });
  }, [onFilterChange]);

  // Memoize expensive calculations
  const activeFilterCount = useMemo(() => {
    const excludeKeys = ['limit', 'offset'];
    return Object.keys(filter).filter(key => !excludeKeys.includes(key)).length;
  }, [filter]);

  const formatDateForInput = useCallback((dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[0];
  }, []);

  // Memoize strategy options
  const strategyOptions = useMemo(() => [
    { value: '', label: 'All Strategies' },
    { value: 'magic_nine', label: 'Magic Nine' },
    { value: 'macd_divergence', label: 'MACD Divergence' },
    { value: 'combined', label: 'Combined Strategy' },
  ], []);

  // Memoize limit options
  const limitOptions = useMemo(() => [
    { value: 25, label: '25' },
    { value: 50, label: '50' },
    { value: 100, label: '100' },
    { value: 200, label: '200' },
  ], []);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-semibold text-gray-900">Signal History Filters</h3>
          {activeFilterCount > 0 && (
            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
              {activeFilterCount} active
            </span>
          )}
          {totalCount !== undefined && (
            <span className="text-sm text-gray-500">
              {totalCount} signals found
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-1"
          >
            <Filter className="w-4 h-4" />
            {isExpanded ? 'Hide' : 'Show'} Filters
          </Button>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading}
              className="flex items-center gap-1"
            >
              <RotateCcw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          )}
        </div>
      </div>

      {/* Quick Filter Buttons */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button
          variant={filter.signalType === 'BUY' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('signalType', filter.signalType === 'BUY' ? undefined : 'BUY')}
          className="flex items-center gap-1"
        >
          <TrendingUp className="w-4 h-4" />
          Buy Signals
        </Button>
        <Button
          variant={filter.signalType === 'SELL' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('signalType', filter.signalType === 'SELL' ? undefined : 'SELL')}
          className="flex items-center gap-1"
        >
          <TrendingDown className="w-4 h-4" />
          Sell Signals
        </Button>
        <Button
          variant={filter.isSuccessful === 'SUCCESS' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('isSuccessful', filter.isSuccessful === 'SUCCESS' ? undefined : 'SUCCESS')}
          className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
        >
          Successful
        </Button>
        <Button
          variant={filter.isSuccessful === 'LOSS' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('isSuccessful', filter.isSuccessful === 'LOSS' ? undefined : 'LOSS')}
          className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100"
        >
          Losses
        </Button>
        <Button
          variant={filter.isSuccessful === 'PENDING' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('isSuccessful', filter.isSuccessful === 'PENDING' ? undefined : 'PENDING')}
          className="bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100"
        >
          Pending
        </Button>
      </div>

      {isExpanded && (
        <div className="border-t border-gray-200 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Symbol Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Symbol
              </label>
              <Input
                type="text"
                placeholder="e.g., AAPL"
                value={filter.symbol || ''}
                onChange={(e) => handleTextFilterChange('symbol', e.target.value.toUpperCase())}
                className="w-full"
              />
            </div>

            {/* Strategy Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Strategy
              </label>
              <select
                value={filter.strategyName || ''}
                onChange={(e) => handleFilterChange('strategyName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {strategyOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Start Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Calendar className="w-4 h-4 inline mr-1" />
                Start Date
              </label>
              <Input
                type="date"
                value={formatDateForInput(filter.startDate)}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                className="w-full"
              />
            </div>

            {/* End Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Calendar className="w-4 h-4 inline mr-1" />
                End Date
              </label>
              <Input
                type="date"
                value={formatDateForInput(filter.endDate)}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Advanced Options */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Results per page
                  </label>
                  <select
                    value={filter.limit || 50}
                    onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {limitOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  disabled={activeFilterCount === 0}
                >
                  Reset All Filters
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

// Apply performance tracking wrapper
export default withPerformanceTracking(SignalHistoryControls, 'SignalHistoryControls');