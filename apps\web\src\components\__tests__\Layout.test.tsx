import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { Layout } from '../Layout'

// Test wrapper with router context
const TestWrapper = ({ children, initialEntries = ['/'] }: { 
  children: React.ReactNode
  initialEntries?: string[]
}) => (
  <MemoryRouter initialEntries={initialEntries}>
    {children}
  </MemoryRouter>
)

describe('Layout', () => {
  it('renders header with title', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test content</div>
        </Layout>
      </TestWrapper>
    )

    expect(screen.getByText('Trading Agent')).toBeInTheDocument()
  })

  it('renders navigation links', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test content</div>
        </Layout>
      </TestWrapper>
    )

    expect(screen.getByRole('link', { name: 'Analysis' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Screener' })).toBeInTheDocument()
  })

  it('renders children content', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test content</div>
        </Layout>
      </TestWrapper>
    )

    expect(screen.getByText('Test content')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test content</div>
        </Layout>
      </TestWrapper>
    )

    // Navigation should have proper ARIA labels
    expect(screen.getByRole('navigation', { name: 'Main navigation' })).toBeInTheDocument()
    expect(screen.getByRole('list')).toBeInTheDocument()
    
    // Links should have proper accessibility attributes
    const analysisLink = screen.getByRole('link', { name: 'Analysis' })
    const screenerLink = screen.getByRole('link', { name: 'Screener' })
    
    expect(analysisLink).toBeInTheDocument()
    expect(screenerLink).toBeInTheDocument()
  })

  it('shows active state for analysis page', () => {
    render(
      <TestWrapper initialEntries={['/analysis']}>
        <Layout>
          <div>Test content</div>
        </Layout>
      </TestWrapper>
    )

    const analysisLink = screen.getByRole('link', { name: 'Analysis' })
    expect(analysisLink).toHaveAttribute('aria-current', 'page')
  })

  it('shows active state for screener page', () => {
    render(
      <TestWrapper initialEntries={['/screener']}>
        <Layout>
          <div>Test content</div>
        </Layout>
      </TestWrapper>
    )

    const screenerLink = screen.getByRole('link', { name: 'Screener' })
    expect(screenerLink).toHaveAttribute('aria-current', 'page')
  })

  it('shows active state for home page (defaults to analysis)', () => {
    render(
      <TestWrapper initialEntries={['/']}>
        <Layout>
          <div>Test content</div>
        </Layout>
      </TestWrapper>
    )

    const analysisLink = screen.getByRole('link', { name: 'Analysis' })
    expect(analysisLink).toHaveAttribute('aria-current', 'page')
  })
})