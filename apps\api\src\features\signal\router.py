"""Signal analysis API endpoints."""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query

from src.features.signal.computation_service import SignalComputationService
from src.features.signal.schemas import (
    AvailableStrategiesResponse,
    EffectivenessResponse,
    SignalComputationRequest,
    SignalComputationResponse,
    StrategyComparison,
    StrategyComparisonResponse,
    EffectivenessMetrics,
)
from src.models.strategy import SignalResult
from src.features.signal.service import SignalService
from src.shared.exceptions import (
    DataIngestionError,
    InvalidParameterError,
    ServiceError,
    StockNotFoundError,
)

router = APIRouter(prefix="/signals", tags=["signal"])


@router.get("/{stock_code}", response_model=SignalResult)
async def get_signal(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    signal_service: SignalService = Depends(),
) -> SignalResult:
    """Get trading signal for a specific stock.
    
    Args:
        stock_code: Stock symbol to analyze
        start_date: Optional start date for analysis
        end_date: Optional end date for analysis
        signal_service: Injected signal service
        
    Returns:
        SignalResult containing the trading signal and analysis
        
    Raises:
        HTTPException: 400 for invalid parameters, 404 for stock not found, 500 for service errors
    """
    try:
        result = signal_service.get_signal_for_stock(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        return result
    except InvalidParameterError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except StockNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except (DataIngestionError, ServiceError) as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/compute", response_model=SignalComputationResponse)
async def compute_signals(
    request: SignalComputationRequest,
    min_confidence: Optional[float] = Query(None, ge=0.0, le=1.0, description="Minimum signal confidence threshold"),
    signal_types: Optional[List[str]] = Query(None, description="Filter by signal types (BUY, SELL)"),
    min_success_rate: Optional[float] = Query(None, ge=0.0, le=100.0, description="Minimum effectiveness threshold (%)"),
    computation_service: SignalComputationService = Depends(),
) -> SignalComputationResponse:
    """Compute historical signals for a stock using specified strategies.
    
    Args:
        request: Signal computation request parameters
        computation_service: Injected signal computation service
        
    Returns:
        SignalComputationResponse containing computed signals
        
    Raises:
        HTTPException: 400 for invalid parameters, 404 for stock not found, 500 for service errors
    """
    try:
        from datetime import datetime
        
        signals = computation_service.compute_historical_signals(
            stock_code=request.stock_code,
            start_date=request.start_date,
            end_date=request.end_date,
            strategies=request.strategies,
            min_confidence=min_confidence,
            signal_types=signal_types,
            min_success_rate=min_success_rate
        )
        
        result = SignalComputationResponse(
            stock_code=request.stock_code,
            computation_time=datetime.now().isoformat(),
            total_signals=len(signals),
            strategies_used=request.strategies or ['macd', 'magic_nine'],
            signals=signals
        )
        return result
    except InvalidParameterError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except StockNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except (DataIngestionError, ServiceError) as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/effectiveness/{stock_code}", response_model=EffectivenessResponse)
async def get_signal_effectiveness(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    strategies: Optional[List[str]] = Query(None, description="Strategies to analyze"),
    min_confidence: Optional[float] = Query(None, ge=0.0, le=1.0, description="Minimum signal confidence threshold"),
    signal_types: Optional[List[str]] = Query(None, description="Filter by signal types (BUY, SELL)"),
    min_success_rate: Optional[float] = Query(None, ge=0.0, le=100.0, description="Minimum effectiveness threshold (%)"),
    computation_service: SignalComputationService = Depends(),
) -> EffectivenessResponse:
    """Get signal effectiveness metrics for a stock.
    
    Args:
        stock_code: Stock symbol to analyze
        start_date: Optional start date for analysis
        end_date: Optional end date for analysis
        strategies: Optional list of strategies to analyze
        computation_service: Injected signal computation service
        
    Returns:
        EffectivenessResponse containing effectiveness metrics
        
    Raises:
        HTTPException: 400 for invalid parameters, 404 for stock not found, 500 for service errors
    """
    try:
        effectiveness_data = computation_service.compute_signal_effectiveness(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            strategies=strategies or ['macd', 'magic_nine'],
            min_confidence=min_confidence,
            signal_types=signal_types,
            min_success_rate=min_success_rate
        )
        result = EffectivenessResponse(**effectiveness_data)
        return result
    except InvalidParameterError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except StockNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except (DataIngestionError, ServiceError) as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compare/{stock_code}", response_model=StrategyComparisonResponse)
async def compare_strategies(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    strategies: Optional[List[str]] = Query(None, description="Strategies to compare"),
    min_confidence: Optional[float] = Query(None, ge=0.0, le=1.0, description="Minimum signal confidence threshold"),
    signal_types: Optional[List[str]] = Query(None, description="Filter by signal types (BUY, SELL)"),
    min_success_rate: Optional[float] = Query(None, ge=0.0, le=100.0, description="Minimum effectiveness threshold (%)"),
    computation_service: SignalComputationService = Depends(),
) -> StrategyComparisonResponse:
    """Compare effectiveness of different strategies for a stock.
    
    Args:
        stock_code: Stock symbol to analyze
        start_date: Optional start date for analysis
        end_date: Optional end date for analysis
        strategies: Optional list of strategies to compare
        computation_service: Injected signal computation service
        
    Returns:
        StrategyComparisonResponse containing strategy comparison results
        
    Raises:
        HTTPException: 400 for invalid parameters, 404 for stock not found, 500 for service errors
    """
    try:
        result = computation_service.compare_strategies(
            symbol=stock_code,
            start_date=start_date,
            end_date=end_date,
            strategies=strategies,
            min_confidence=min_confidence,
            signal_types=signal_types,
            min_success_rate=min_success_rate
        )
        
        return StrategyComparisonResponse(
            stock_code=result['stock_code'],
            analysis_period=result['analysis_period'],
            computation_time=result['computation_time'],
            strategies=[
                StrategyComparison(
                    strategy_name=strategy['strategy_name'],
                    metrics=EffectivenessMetrics(**strategy['metrics'])
                )
                for strategy in result['strategies']
            ]
        )
    except InvalidParameterError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except StockNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except (DataIngestionError, ServiceError) as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/compare-strategies", response_model=StrategyComparisonResponse)
async def compare_strategies_post(
    request: dict,
    computation_service: SignalComputationService = Depends(),
) -> StrategyComparisonResponse:
    """Compare effectiveness of different strategies for a stock (POST version).
    
    Args:
        request: Dictionary containing symbol and strategies
        computation_service: Injected signal computation service
        
    Returns:
        StrategyComparisonResponse containing strategy comparison results
        
    Raises:
        HTTPException: 400 for invalid parameters, 404 for stock not found, 500 for service errors
    """
    try:
        symbol = request.get('symbol')
        strategies = request.get('strategies')
        
        if not symbol:
            raise InvalidParameterError("Symbol is required")
        if not strategies or not isinstance(strategies, list):
            raise InvalidParameterError("Strategies list is required")
            
        result = computation_service.compare_strategies(
            symbol=symbol,
            start_date=None,
            end_date=None,
            strategies=strategies,
            min_confidence=None,
            signal_types=None,
            min_success_rate=None
        )
        
        return StrategyComparisonResponse(
            stock_code=result['stock_code'],
            analysis_period=result['analysis_period'],
            computation_time=result['computation_time'],
            strategies=[
                StrategyComparison(
                    strategy_name=strategy['strategy_name'],
                    metrics=EffectivenessMetrics(**strategy['metrics'])
                )
                for strategy in result['strategies']
            ]
        )
    except InvalidParameterError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except StockNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except (DataIngestionError, ServiceError) as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/strategies", response_model=AvailableStrategiesResponse)
async def get_available_strategies(
    computation_service: SignalComputationService = Depends(),
) -> AvailableStrategiesResponse:
    """Get list of available trading strategies.
    
    Args:
        computation_service: Injected signal computation service
        
    Returns:
        AvailableStrategiesResponse containing available strategies
    """
    strategies = computation_service.get_available_strategies()
    return AvailableStrategiesResponse(
        strategies=[
            {"name": strategy, "description": f"{strategy.upper()} trading strategy"}
            for strategy in strategies
        ]
    )
