"""Signal analysis router."""
import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...models.signal_history_schemas import (
    SignalEffectivenessResponse,
    SignalHistoryFilter,
    SignalHistoryResponse,
    SignalPerformanceMetrics,
)
from ...models.strategy import SignalResult
from .history_service import SignalHistoryService
from .service import SignalService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/signal/{stock_code}", response_model=SignalResult)
async def get_signal(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format")
) -> SignalResult:
    """
    Get trading signal for a single stock.

    Args:
        stock_code: The stock code to analyze (e.g., 000001.SZ)
        start_date: Optional start date for data range
        end_date: Optional end date for data range

    Returns:
        SignalResult with complete analysis data

    Raises:
        HTTPException: 400 for invalid parameters, 404 for stock not found, 500 for service errors
    """
    if not stock_code or not stock_code.strip():
        raise HTTPException(status_code=400, detail="Stock code is required")

    # Create service instance - consider dependency injection for better testability in future
    signal_service = SignalService()

    try:
        # Get signal analysis from service
        result = signal_service.get_signal_for_stock(
            stock_code=stock_code.strip(),
            start_date=start_date,
            end_date=end_date
        )

        logger.info(f"Successfully returned signal for {stock_code}: {result.signal}")
        return result

    except ValueError as e:
        # Client errors (invalid stock code, bad data)
        logger.warning(f"Client error for stock {stock_code}: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))

    except RuntimeError as e:
        # Service errors (akshare unavailable, etc.)
        logger.error(f"Service error for stock {stock_code}: {str(e)}")
        raise HTTPException(status_code=500, detail="Signal analysis service temporarily unavailable")

    except Exception as e:
        # Unexpected errors
        logger.error(f"Unexpected error for stock {stock_code}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/signal-history", response_model=dict)
async def get_signal_history(
    symbol: str = Query(..., description="Stock symbol to get history for"),
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    signal_type: Optional[str] = Query(None, description="Signal type filter (BUY, SELL)"),
    strategy_name: Optional[str] = Query(None, description="Strategy name filter"),
    is_successful: Optional[str] = Query(None, description="Success filter (SUCCESS, LOSS, PENDING)"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
) -> dict:
    """
    Get historical signal data with filtering and pagination.
    
    Args:
        symbol: Stock symbol to filter by
        start_date: Optional start date filter
        end_date: Optional end date filter
        signal_type: Optional signal type filter
        strategy_name: Optional strategy name filter
        is_successful: Optional success status filter
        page: Page number for pagination
        page_size: Number of records per page
        db: Database session
    
    Returns:
        Dictionary containing signals list, pagination info, and metadata
    """
    try:
        service = SignalHistoryService(db)
        
        # Create filter object
        filters = SignalHistoryFilter(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            signal_type=signal_type,
            strategy_name=strategy_name,
            is_successful=is_successful,
            offset=(page - 1) * page_size,
            limit=page_size
        )
        
        signals, total_count = service.get_signal_history(filters)
        
        return {
            "signals": signals,
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }
        
    except Exception as e:
        logger.error(f"Error fetching signal history for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch signal history")


@router.get("/signal-history/effectiveness", response_model=SignalEffectivenessResponse)
async def get_signal_effectiveness(
    symbol: str = Query(..., description="Stock symbol to analyze"),
    signal_type: str = Query(..., description="Signal type (BUY or SELL)"),
    period_days: int = Query(90, ge=1, le=365, description="Analysis period in days"),
    db: Session = Depends(get_db)
) -> SignalEffectivenessResponse:
    """
    Get signal effectiveness metrics for a symbol and signal type.
    
    Args:
        symbol: Stock symbol to analyze
        signal_type: Signal type to analyze (BUY or SELL)
        period_days: Number of days to analyze (default 90)
        db: Database session
    
    Returns:
        SignalEffectivenessResponse with comprehensive metrics
    """
    try:
        service = SignalHistoryService(db)
        effectiveness = service.calculate_effectiveness_metrics(symbol, signal_type, period_days)
        
        logger.info(f"Calculated effectiveness for {symbol} {signal_type}: {effectiveness.success_rate:.2%}")
        return effectiveness
        
    except Exception as e:
        logger.error(f"Error calculating effectiveness for {symbol} {signal_type}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to calculate signal effectiveness")


@router.get("/signal-history/metrics", response_model=SignalPerformanceMetrics)
async def get_performance_metrics(
    symbol: str = Query(..., description="Stock symbol to analyze"),
    timeframe: str = Query("30d", description="Timeframe (1d, 7d, 30d, 90d, 1y)"),
    db: Session = Depends(get_db)
) -> SignalPerformanceMetrics:
    """
    Get comprehensive performance metrics for a symbol.
    
    Args:
        symbol: Stock symbol to analyze
        timeframe: Analysis timeframe
        db: Database session
    
    Returns:
        SignalPerformanceMetrics with detailed performance data
    """
    try:
        service = SignalHistoryService(db)
        metrics = service.get_performance_metrics(symbol, timeframe)
        
        logger.info(f"Retrieved performance metrics for {symbol} ({timeframe})")
        return metrics
        
    except Exception as e:
        logger.error(f"Error getting performance metrics for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")


@router.get("/signal-history/strategies", response_model=dict)
async def get_available_strategies(
    db: Session = Depends(get_db)
) -> dict:
    """
    Get list of available trading strategies.
    
    Args:
        db: Database session
    
    Returns:
        Dictionary containing list of available strategies
    """
    try:
        service = SignalHistoryService(db)
        strategies = service.get_available_strategies()
        
        return {"strategies": strategies}
        
    except Exception as e:
        logger.error(f"Error fetching available strategies: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch strategies")


@router.get("/signal-history/compare", response_model=List[dict])
async def compare_strategies(
    symbol: str = Query(..., description="Stock symbol to analyze"),
    strategies: str = Query(..., description="Comma-separated list of strategies to compare"),
    timeframe: str = Query("30d", description="Timeframe for comparison"),
    db: Session = Depends(get_db)
) -> List[dict]:
    """
    Compare performance of different strategies for a symbol.
    
    Args:
        symbol: Stock symbol to analyze
        strategies: Comma-separated strategy names
        timeframe: Analysis timeframe
        db: Database session
    
    Returns:
        List of strategy comparison data
    """
    try:
        service = SignalHistoryService(db)
        strategy_list = [s.strip() for s in strategies.split(",")]
        comparison = service.compare_strategies(symbol, strategy_list, timeframe)
        
        logger.info(f"Compared {len(strategy_list)} strategies for {symbol}")
        return comparison
        
    except Exception as e:
        logger.error(f"Error comparing strategies for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to compare strategies")
