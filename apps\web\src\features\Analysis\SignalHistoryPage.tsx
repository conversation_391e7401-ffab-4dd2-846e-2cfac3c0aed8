import { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { LoadingState } from '@/components/ui/LoadingState'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { Button } from '@/components/ui/Button'
import { StockInputForm } from './StockInputForm'
import { InteractiveChart } from './InteractiveChart'
import { SignalHistoryControls, SignalEffectivenessIndicator } from '@/components/charts'
import { apiClient, ApiError } from '@/lib/api'
import { useSignalHistoryStore } from '@/stores/signalHistoryStore'
import { useDebounce, withPerformanceTracking } from '@/utils/performance'
import type { 
  SignalResult, 
  SignalHistoryFilter,
  SignalHistoryRecord,
  SignalData
} from '@trading-agent/shared-types'

const SignalHistoryPage = memo(() => {
  const { t } = useTranslation('analysis')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [result, setResult] = useState<SignalResult | null>(null)
  const [searchParams, setSearchParams] = useSearchParams()
  const [initialStock, setInitialStock] = useState<string | null>(null)
  const [currentSymbol, setCurrentSymbol] = useState<string>('')
  const [showEffectiveness, setShowEffectiveness] = useState(true)

  // Use the signal history store
  const {
    signals: signalHistory,
    effectiveness,
    performanceMetrics,
    filters,
    isLoading: historyLoading,
    fetchSignals,
    fetchEffectiveness,
    fetchPerformanceMetrics,
    setFilters,
    clearError
  } = useSignalHistoryStore()

  // Handle URL parameters for pre-populated stock code
  useEffect(() => {
    const stockParam = searchParams.get('stock')
    if (stockParam) {
      setInitialStock(stockParam)
      handleAnalyze(stockParam)
      setSearchParams({})
    }
  }, [searchParams, setSearchParams])

  const handleAnalyze = async (stockCode: string) => {
    setLoading(true)
    setError(null)
    setResult(null)
    setCurrentSymbol(stockCode.toUpperCase())
    
    try {
      // Fetch current signal analysis
      const signalResult = await apiClient.getSignalForStock(stockCode)
      setResult(signalResult)
      
      // Fetch signal history for this symbol
      await loadSignalHistory(stockCode)
    } catch (err) {
      if (err instanceof ApiError) {
        if (err.status === 404) {
          setError(t('errors.invalidSymbol'))
        } else if (err.status === 0) {
          setError(err.message)
        } else {
          setError(err.message || t('errors.fetchFailed'))
        }
      } else {
        setError(t('errors.fetchFailed'))
      }
    } finally {
      setLoading(false)
    }
  }

  const loadSignalHistory = useCallback(async (symbol: string) => {
    const upperSymbol = symbol.toUpperCase()
    
    try {
      // Update filters with the new symbol
      const newFilters = { ...filters, symbol: upperSymbol }
      setFilters(newFilters)
      
      // Load all data concurrently for better performance
      await Promise.all([
        fetchSignals(upperSymbol, newFilters),
        fetchEffectiveness(),
        fetchPerformanceMetrics(upperSymbol)
      ])
    } catch (err) {
      console.error('Failed to load signal history:', err)
      // Error is handled by the store
    }
  }, [filters, setFilters, fetchSignals, fetchEffectiveness, fetchPerformanceMetrics])

  const handleFilterChange = useCallback(async (newFilter: SignalHistoryFilter) => {
    // Convert SignalHistoryFilter to SignalFilters format
    const convertedFilters = {
      dateRange: {
        start: newFilter.startDate || filters.dateRange.start,
        end: newFilter.endDate || filters.dateRange.end
      },
      signalTypes: newFilter.signalType ? [newFilter.signalType.toLowerCase() as 'buy' | 'sell'] : filters.signalTypes,
      minConfidence: filters.minConfidence,
      minEffectiveness: filters.minEffectiveness,
      strategies: newFilter.strategyName ? [newFilter.strategyName] : filters.strategies
    }
    setFilters(convertedFilters)
    if (currentSymbol) {
      await fetchSignals(currentSymbol, convertedFilters)
    }
  }, [currentSymbol, setFilters, fetchSignals, filters])

  const handleRetry = useCallback(() => {
    setError(null)
    clearError()
  }, [clearError])

  // Convert SignalData to SignalHistoryRecord format
  const convertSignalDataToHistoryRecord = (signalData: SignalData): SignalHistoryRecord => {
    return {
      id: parseInt(signalData.id) || 0,
      symbol: signalData.symbol,
      signalType: signalData.signal_type === 'buy' ? 'BUY' : 'SELL',
      signalStrength: signalData.confidence,
      triggerPrice: signalData.price,
      triggerDate: signalData.timestamp,
      exitPrice: signalData.exit_price,
      exitDate: signalData.exit_timestamp,
      returnPercentage: signalData.profit_loss,
      strategyName: signalData.strategy_name,
      isSuccessful: signalData.effectiveness_score && signalData.effectiveness_score > 0.5 ? 'SUCCESS' : 
                   signalData.effectiveness_score !== undefined ? 'LOSS' : 'PENDING',
      createdAt: signalData.timestamp
    };
  };

  const renderChart = useCallback(() => {
    if (!result) return null

    const convertedSignals = signalHistory.map(convertSignalDataToHistoryRecord);

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">{t('chart.title')} - {currentSymbol}</h3>
          <div className="flex items-center gap-2">
            <Button
              variant={showEffectiveness ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowEffectiveness(!showEffectiveness)}
            >
              {showEffectiveness ? 'Hide' : 'Show'} Effectiveness
            </Button>
          </div>
        </div>
        
        <InteractiveChart 
          result={result} 
          signalHistory={convertedSignals}
          showSignalEffectiveness={showEffectiveness}
        />
        
        {historyLoading && (
          <div className="text-center py-2">
            <LoadingState message="Loading signal history..." className="justify-center" />
          </div>
        )}
      </div>
    )
  }, [result, currentSymbol, t, showEffectiveness, signalHistory, historyLoading])

  const renderEffectivenessMetrics = useMemo(() => {
    if (!performanceMetrics || !effectiveness) return null

    return (
      <Card>
        <CardHeader>
          <CardTitle>Signal Effectiveness Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Overall Performance</h4>
              <SignalEffectivenessIndicator
                performanceMetrics={performanceMetrics}
                compact={false}
              />
            </div>
            <div>
              <h4 className="font-medium mb-3">Effectiveness Summary</h4>
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">{effectiveness.symbol}</span>
                  <span className={`text-sm px-2 py-1 rounded ${
                    effectiveness.success_rate >= 0.6 ? 'bg-green-100 text-green-800' :
                    effectiveness.success_rate >= 0.4 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {(effectiveness.success_rate * 100).toFixed(1)}% Success
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {effectiveness.total_signals} signals • Avg Profit: {(effectiveness.average_profit * 100).toFixed(2)}%
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Best Strategy: {effectiveness.best_strategy}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }, [performanceMetrics, effectiveness])

  const renderHistoryControls = useCallback(() => {
    if (!currentSymbol) return null

    // Convert SignalFilters to SignalHistoryFilter format
    const historyFilter = {
      symbol: currentSymbol,
      start_date: filters.dateRange?.start,
      end_date: filters.dateRange?.end,
      signal_type: filters.signalTypes?.[0] as 'BUY' | 'SELL' | undefined,
      strategy_name: filters.strategies?.[0],
      min_success_rate: filters.minEffectiveness,
      limit: 50,
      offset: 0
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle>Signal History Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <SignalHistoryControls
            filter={historyFilter}
            onFilterChange={handleFilterChange}
            totalCount={signalHistory.length}
            isLoading={historyLoading}
          />
        </CardContent>
      </Card>
    )
  }, [currentSymbol, filters, handleFilterChange, signalHistory.length, historyLoading])

  const renderResults = () => {
    if (loading) {
      return (
        <LoadingState 
          message={t('loading.fetchingData')} 
          className="justify-center py-8"
        />
      )
    }

    if (error) {
      return (
        <ErrorMessage 
          message={error}
          title={t('errors.fetchFailed')}
          onRetry={handleRetry}
        />
      )
    }

    if (result) {
      return (
        <div className="space-y-6">
          {renderChart()}
          {renderEffectivenessMetrics}
          {renderHistoryControls()}
        </div>
      )
    }

    return (
      <p className="text-muted-foreground text-center py-8">
        {t('stockInput.placeholder')}
      </p>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Signal History Analysis</h1>
        <p className="text-muted-foreground mt-2">
          Analyze trading signals with historical effectiveness data and performance metrics.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('stockInput.submit')}</CardTitle>
        </CardHeader>
        <CardContent>
          <StockInputForm 
            onSubmit={handleAnalyze}
            loading={loading}
            disabled={loading}
            initialValue={initialStock || undefined}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Analysis Results</CardTitle>
        </CardHeader>
        <CardContent>
          {renderResults()}
        </CardContent>
      </Card>
    </div>
  )
});

SignalHistoryPage.displayName = 'SignalHistoryPage';

export { SignalHistoryPage };
export default withPerformanceTracking(SignalHistoryPage, 'SignalHistoryPage');