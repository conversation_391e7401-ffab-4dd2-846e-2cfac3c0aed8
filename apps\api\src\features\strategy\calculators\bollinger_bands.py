"""Bollinger Bands calculator for trading signals."""

import logging
from typing import Dict, List, Optional, Tuple

import pandas as pd

from src.shared.exceptions import InvalidParameterError

logger = logging.getLogger(__name__)


class BollingerBandsCalculator:
    """Calculator for Bollinger Bands trading signals.
    
    Bollinger Bands consist of a middle line (simple moving average) and two outer bands
    that are standard deviations away from the middle line. They help identify overbought
    and oversold conditions, as well as volatility.
    """
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        """Initialize Bollinger Bands calculator.
        
        Args:
            period: Period for moving average calculation (default: 20)
            std_dev: Number of standard deviations for bands (default: 2.0)
        """
        if period < 2:
            raise InvalidParameterError("Bollinger Bands period must be at least 2")
        if std_dev <= 0:
            raise InvalidParameterError("Standard deviation must be positive")
            
        self.period = period
        self.std_dev = std_dev
    
    def calculate_bollinger_bands(self, prices: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands.
        
        Args:
            prices: Series of closing prices
            
        Returns:
            Tuple of (upper_band, middle_band, lower_band) Series
            
        Raises:
            InvalidParameterError: If insufficient data points
        """
        if len(prices) < self.period:
            raise InvalidParameterError(
                f"Need at least {self.period} data points for Bollinger Bands, got {len(prices)}"
            )
        
        # Calculate middle band (Simple Moving Average)
        middle_band = prices.rolling(window=self.period, min_periods=self.period).mean()
        
        # Calculate standard deviation
        rolling_std = prices.rolling(window=self.period, min_periods=self.period).std()
        
        # Calculate upper and lower bands
        upper_band = middle_band + (rolling_std * self.std_dev)
        lower_band = middle_band - (rolling_std * self.std_dev)
        
        return upper_band, middle_band, lower_band
    
    def calculate_bandwidth(self, upper_band: pd.Series, lower_band: pd.Series, middle_band: pd.Series) -> pd.Series:
        """Calculate Bollinger Band Width.
        
        Args:
            upper_band: Upper Bollinger Band
            lower_band: Lower Bollinger Band
            middle_band: Middle Bollinger Band (SMA)
            
        Returns:
            Series of bandwidth values
        """
        return (upper_band - lower_band) / middle_band
    
    def calculate_percent_b(self, prices: pd.Series, upper_band: pd.Series, lower_band: pd.Series) -> pd.Series:
        """Calculate %B (Percent B) indicator.
        
        %B shows where price is relative to the bands:
        - %B = 1 when price is at upper band
        - %B = 0 when price is at lower band
        - %B > 1 when price is above upper band
        - %B < 0 when price is below lower band
        
        Args:
            prices: Series of closing prices
            upper_band: Upper Bollinger Band
            lower_band: Lower Bollinger Band
            
        Returns:
            Series of %B values
        """
        return (prices - lower_band) / (upper_band - lower_band)
    
    def generate_signals(self, data: pd.DataFrame) -> List[Dict]:
        """Generate Bollinger Bands trading signals.
        
        Args:
            data: DataFrame with 'close' column and datetime index
            
        Returns:
            List of signal dictionaries with timestamp, signal type, and metadata
            
        Raises:
            InvalidParameterError: If data format is invalid or insufficient
        """
        if 'close' not in data.columns:
            raise InvalidParameterError("Data must contain 'close' column")
        
        if len(data) < self.period:
            raise InvalidParameterError(
                f"Need at least {self.period} data points for Bollinger Bands signals, got {len(data)}"
            )
        
        try:
            # Calculate Bollinger Bands
            upper_band, middle_band, lower_band = self.calculate_bollinger_bands(data['close'])
            
            # Calculate additional indicators
            bandwidth = self.calculate_bandwidth(upper_band, lower_band, middle_band)
            percent_b = self.calculate_percent_b(data['close'], upper_band, lower_band)
            
            signals = []
            
            for i in range(1, len(data)):
                current_price = data['close'].iloc[i]
                previous_price = data['close'].iloc[i-1]
                
                current_upper = upper_band.iloc[i]
                current_lower = lower_band.iloc[i]
                current_middle = middle_band.iloc[i]
                
                previous_upper = upper_band.iloc[i-1]
                previous_lower = lower_band.iloc[i-1]
                
                current_percent_b = percent_b.iloc[i]
                previous_percent_b = percent_b.iloc[i-1]
                
                # Skip if any values are NaN
                if any(pd.isna(val) for val in [current_upper, current_lower, current_middle, 
                                               current_percent_b, previous_percent_b]):
                    continue
                
                signal_type = None
                confidence = 0.0
                
                # Buy signal: Price bounces off lower band
                if (previous_price <= previous_lower and current_price > current_lower and 
                    previous_percent_b <= 0 and current_percent_b > 0):
                    signal_type = "BUY"
                    # Higher confidence when price was further below the band
                    confidence = min(0.9, 0.6 + abs(min(previous_percent_b, -0.1)) * 2)
                
                # Sell signal: Price bounces off upper band
                elif (previous_price >= previous_upper and current_price < current_upper and 
                      previous_percent_b >= 1 and current_percent_b < 1):
                    signal_type = "SELL"
                    # Higher confidence when price was further above the band
                    confidence = min(0.9, 0.6 + (max(previous_percent_b, 1.1) - 1) * 2)
                
                # Additional buy signal: Price crosses above middle band with momentum
                elif (previous_price <= middle_band.iloc[i-1] and current_price > current_middle and 
                      current_percent_b > 0.5 and bandwidth.iloc[i] > bandwidth.iloc[i-1]):
                    signal_type = "BUY"
                    confidence = 0.4  # Lower confidence for middle band crossover
                
                # Additional sell signal: Price crosses below middle band with momentum
                elif (previous_price >= middle_band.iloc[i-1] and current_price < current_middle and 
                      current_percent_b < 0.5 and bandwidth.iloc[i] > bandwidth.iloc[i-1]):
                    signal_type = "SELL"
                    confidence = 0.4  # Lower confidence for middle band crossover
                
                if signal_type:
                    signals.append({
                        'timestamp': data.index[i],
                        'signal_type': signal_type,
                        'strategy': 'Bollinger_Bands',
                        'confidence': confidence,
                        'price': current_price,
                        'upper_band': current_upper,
                        'middle_band': current_middle,
                        'lower_band': current_lower,
                        'percent_b': current_percent_b,
                        'bandwidth': bandwidth.iloc[i],
                        'metadata': {
                            'bb_period': self.period,
                            'bb_std_dev': self.std_dev,
                            'percent_b_current': float(current_percent_b),
                            'percent_b_previous': float(previous_percent_b),
                            'bandwidth_current': float(bandwidth.iloc[i]),
                            'upper_band': float(current_upper),
                            'middle_band': float(current_middle),
                            'lower_band': float(current_lower)
                        }
                    })
            
            logger.info(f"Generated {len(signals)} Bollinger Bands signals")
            return signals
            
        except Exception as e:
            logger.error(f"Error generating Bollinger Bands signals: {str(e)}")
            raise InvalidParameterError(f"Failed to generate Bollinger Bands signals: {str(e)}")
    
    def get_current_position(self, data: pd.DataFrame) -> Optional[Dict]:
        """Get current position relative to Bollinger Bands.
        
        Args:
            data: DataFrame with 'close' column
            
        Returns:
            Dictionary with current band values and position or None if insufficient data
        """
        try:
            if len(data) < self.period:
                return None
            
            upper_band, middle_band, lower_band = self.calculate_bollinger_bands(data['close'])
            percent_b = self.calculate_percent_b(data['close'], upper_band, lower_band)
            bandwidth = self.calculate_bandwidth(upper_band, lower_band, middle_band)
            
            current_price = data['close'].iloc[-1]
            current_upper = upper_band.iloc[-1]
            current_middle = middle_band.iloc[-1]
            current_lower = lower_band.iloc[-1]
            current_percent_b = percent_b.iloc[-1]
            current_bandwidth = bandwidth.iloc[-1]
            
            if any(pd.isna(val) for val in [current_upper, current_middle, current_lower, 
                                           current_percent_b, current_bandwidth]):
                return None
            
            # Determine position
            if current_percent_b > 1:
                position = "Above Upper Band"
            elif current_percent_b < 0:
                position = "Below Lower Band"
            elif current_percent_b > 0.8:
                position = "Near Upper Band"
            elif current_percent_b < 0.2:
                position = "Near Lower Band"
            else:
                position = "Between Bands"
            
            return {
                'price': float(current_price),
                'upper_band': float(current_upper),
                'middle_band': float(current_middle),
                'lower_band': float(current_lower),
                'percent_b': float(current_percent_b),
                'bandwidth': float(current_bandwidth),
                'position': position
            }
            
        except Exception as e:
            logger.error(f"Error calculating current Bollinger Bands position: {str(e)}")
            return None
    
    def is_squeeze(self, bandwidth: pd.Series, lookback_period: int = 20) -> bool:
        """Detect Bollinger Band squeeze (low volatility period).
        
        Args:
            bandwidth: Series of bandwidth values
            lookback_period: Period to look back for comparison
            
        Returns:
            True if current bandwidth is at or near lowest in lookback period
        """
        if len(bandwidth) < lookback_period:
            return False
        
        current_bandwidth = bandwidth.iloc[-1]
        recent_bandwidth = bandwidth.iloc[-lookback_period:]
        
        # Consider it a squeeze if current bandwidth is in bottom 10% of recent values
        threshold = recent_bandwidth.quantile(0.1)
        
        return current_bandwidth <= threshold
    
    def detect_squeeze(self, data: pd.DataFrame, squeeze_threshold: float = 0.1) -> pd.Series:
        """Detect Bollinger Band squeeze conditions.
        
        Args:
            data: DataFrame with 'close' column
            squeeze_threshold: Threshold for detecting squeeze (default: 0.1)
            
        Returns:
            Boolean series indicating squeeze conditions
        """
        upper_band, middle_band, lower_band = self.calculate_bollinger_bands(data['close'])
        bandwidth = self.calculate_bandwidth(upper_band, lower_band, middle_band)
        
        # Calculate rolling minimum of bandwidth to detect squeeze
        rolling_min = bandwidth.rolling(window=20, min_periods=1).min()
        
        # Squeeze occurs when bandwidth is at or near its minimum
        squeeze_condition = bandwidth <= (rolling_min * (1 + squeeze_threshold))
        
        return squeeze_condition