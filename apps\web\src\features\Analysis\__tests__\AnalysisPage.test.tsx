import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { <PERSON><PERSON>erRouter, MemoryRouter } from 'react-router-dom'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { AnalysisPage } from '../AnalysisPage'
import * as api from '@/lib/api'
import type { SignalResult, Signal } from '@trading-agent/shared-types'

// Mock the API module
vi.mock('@/lib/api', () => ({
  apiClient: {
    getSignalForStock: vi.fn()
  }
}))

const mockAnalysisResult: SignalResult = {
  symbol: '000001',
  lastScanDate: '2024-01-15T10:30:00Z',
  signal: 'SELL_CANDIDATE' as Signal,
  chartData: {
    dailyPrices: [
      { date: '2024-01-15', open: 150, high: 155, low: 148, close: 152, volume: 1000000 }
    ],
    magicNineSequence: [1, 2, 3],
    macdLine: [0.5, 0.7, 0.9],
    signalLine: [0.4, 0.6, 0.8],
    divergencePoints: [{ date: '2024-01-15', type: 'TOP' }]
  }
}

const renderWithRouter = (component: React.ReactElement, initialEntries?: string[]) => {
  if (initialEntries) {
    return render(
      <MemoryRouter initialEntries={initialEntries}>
        {component}
      </MemoryRouter>
    )
  }
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('AnalysisPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('renders stock input form', () => {
    renderWithRouter(<AnalysisPage />)
    
    expect(screen.getByPlaceholderText('Enter stock code (e.g., 000001)')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /analyze/i })).toBeInTheDocument()
  })

  it('handles stock analysis submission', async () => {
    vi.mocked(api.apiClient.getSignalForStock).mockResolvedValue(mockAnalysisResult)

    renderWithRouter(<AnalysisPage />)

    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })

    fireEvent.change(input, { target: { value: '000001' } })
    fireEvent.click(button)

    expect(screen.getByText('Analyzing...')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText('Ping An Bank Co Ltd')).toBeInTheDocument()
      expect(screen.getByText('000001')).toBeInTheDocument()
    })

    expect(api.apiClient.getSignalForStock).toHaveBeenCalledWith('000001')
  })

  it('pre-populates stock code from URL parameter', async () => {
    vi.mocked(api.apiClient.getSignalForStock).mockResolvedValue(mockAnalysisResult)

    renderWithRouter(<AnalysisPage />, ['/analysis?stock=000001'])

    // Should automatically trigger analysis
    await waitFor(() => {
      expect(api.apiClient.getSignalForStock).toHaveBeenCalledWith('000001')
    })

    // Should display results
    await waitFor(() => {
      expect(screen.getByText('Ping An Bank Co Ltd')).toBeInTheDocument()
    })

    // Input should be pre-populated
    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)') as HTMLInputElement
    expect(input.value).toBe('000001')
  })

  it('handles API error gracefully', async () => {
    const errorMessage = 'Stock not found'
    vi.mocked(api.apiClient.getSignalForStock).mockRejectedValue(
      new Error(errorMessage)
    )

    renderWithRouter(<AnalysisPage />)

    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })

    fireEvent.change(input, { target: { value: 'INVALID' } })
    fireEvent.click(button)

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('displays loading state during analysis', async () => {
    vi.mocked(api.apiClient.getSignalForStock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    )

    renderWithRouter(<AnalysisPage />)

    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })

    fireEvent.change(input, { target: { value: '000001' } })
    fireEvent.click(button)

    expect(screen.getByText('Analyzing...')).toBeInTheDocument()
    expect(button).toBeDisabled()
  })

  it('displays signal results when analysis completes', async () => {
    vi.mocked(api.apiClient.getSignalForStock).mockResolvedValue(mockAnalysisResult)

    renderWithRouter(<AnalysisPage />)

    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })

    fireEvent.change(input, { target: { value: '000001' } })
    fireEvent.click(button)

    await waitFor(() => {
      expect(screen.getByText('BUY')).toBeInTheDocument()
      expect(screen.getByText('HOLD')).toBeInTheDocument()
      expect(screen.getByText('POSITIVE')).toBeInTheDocument()
    })
  })

  it('displays interactive chart when analysis completes', async () => {
    vi.mocked(api.apiClient.getSignalForStock).mockResolvedValue(mockAnalysisResult)

    renderWithRouter(<AnalysisPage />)

    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })

    fireEvent.change(input, { target: { value: '000001' } })
    fireEvent.click(button)

    await waitFor(() => {
      expect(screen.getByText('Price Chart')).toBeInTheDocument()
    })
  })

  it('clears URL parameters after processing', async () => {
    vi.mocked(api.apiClient.getSignalForStock).mockResolvedValue(mockAnalysisResult)

    renderWithRouter(<AnalysisPage />, ['/analysis?stock=000001'])

    await waitFor(() => {
      expect(api.apiClient.getSignalForStock).toHaveBeenCalledWith('000001')
    })

    // URL should be cleaned up (this is more of an integration test)
    // In a real app, you'd test this with actual router navigation
  })

  it('handles form submission via Enter key', async () => {
    vi.mocked(api.apiClient.getSignalForStock).mockResolvedValue(mockAnalysisResult)

    renderWithRouter(<AnalysisPage />)

    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')

    fireEvent.change(input, { target: { value: '000001' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })

    await waitFor(() => {
      expect(api.apiClient.getSignalForStock).toHaveBeenCalledWith('000001')
    })
  })

  it('validates stock code format', async () => {
    renderWithRouter(<AnalysisPage />)

    const input = screen.getByPlaceholderText('Enter stock code (e.g., 000001)')
    const button = screen.getByRole('button', { name: /analyze/i })

    // Test invalid format
    fireEvent.change(input, { target: { value: 'invalid123' } })
    fireEvent.click(button)

    expect(screen.getByText('Invalid format. Please use 6-digit format: 000001')).toBeInTheDocument()
    expect(api.apiClient.getSignalForStock).not.toHaveBeenCalled()
  })
})