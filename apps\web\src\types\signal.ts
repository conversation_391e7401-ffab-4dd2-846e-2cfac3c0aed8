// Signal-related type definitions for the frontend

export interface ComputedSignal {
  timestamp: string;
  price: number;
  signal_type: 'buy' | 'sell' | 'hold';
  strategy: string;
  confidence: number;
  symbol: string;
  return_pct?: number;
  holding_period_days?: number;
  is_successful?: boolean;
  forward_returns?: Record<string, number>;
  effectiveness?: EffectivenessMetrics;
  metadata?: Record<string, any>;
}

export interface EffectivenessMetrics {
  success_rate: number;
  average_return: number;
  max_drawdown: number;
  average_holding_period: number;
  total_signals: number;
  win_loss_ratio: number;
  sharpe_ratio?: number;
  total_return?: number;
}

export interface SignalComputationRequest {
  symbol: string;
  start_date?: string;
  end_date?: string;
  strategies?: string[];
  min_confidence?: number;
  signal_types?: ('buy' | 'sell')[];
}

export interface SignalComputationResponse {
  symbol: string;
  signals: ComputedSignal[];
  total_count: number;
  computation_time_ms: number;
  cache_hit: boolean;
  effectivenessMetrics?: EffectivenessMetrics;
}

export interface StrategyComparison {
  strategy: string;
  effectiveness: EffectivenessMetrics;
  signal_count: number;
  recent_signals: ComputedSignal[];
}

export interface StrategyComparisonResponse {
  symbol: string;
  date_range: {
    start_date: string;
    end_date: string;
  };
  comparisons: StrategyComparison[];
  overall_metrics: EffectivenessMetrics;
}

// Legacy signal types for backward compatibility
export type Signal = 'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL';

export interface DivergencePoint {
  date: string;
  type: 'TOP' | 'BOTTOM';
}

export interface ChartData {
  dailyPrices: Array<{
    date: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
  }>;
  magicNineSequence: (number | null)[];
  macdLine: (number | null)[];
  signalLine: (number | null)[];
  divergencePoints: DivergencePoint[];
}

export interface SignalResult {
  symbol: string;
  lastScanDate: string;
  signal: Signal;
  chartData: ChartData;
}

// Signal filtering and display options
export interface SignalFilterOptions {
  signalTypes: ('buy' | 'sell' | 'hold')[];
  minConfidence: number;
  maxConfidence: number;
  strategies: string[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
}

export interface SignalDisplayOptions {
  showTooltips: boolean;
  showEffectiveness: boolean;
  groupByStrategy: boolean;
  sortBy: 'timestamp' | 'confidence' | 'strategy';
  sortOrder: 'asc' | 'desc';
}