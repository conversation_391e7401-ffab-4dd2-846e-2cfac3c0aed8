# Story 1.4: Unified Signal API Endpoint

## Status
Done

## Story
**As a** System,
**I want** a single API endpoint that orchestrates the data fetching and strategy calculations to return a unified trading signal,
**so that** the frontend has a simple, clean interface to consume.

## Acceptance Criteria
1. A new backend API endpoint (e.g., `GET /api/signal/{stock_code}`) is created.
2. The endpoint internally calls the data ingestion service and the strategy calculation service.
3. The endpoint returns a simple JSON response indicating the final, combined signal (e.g., `{ "signal": "SELL_CANDIDATE", "details": {...} }`).
4. The JSON response also includes the necessary data points for the frontend to render a chart (e.g., prices, indicator values).
5. Integration tests confirm the end-to-end flow from request to a valid signal response.

## Tasks / Subtasks
- [x] Task 1: Create Signal Service Module (AC: 2)
  - [x] Create signal service class in signal feature that orchestrates data fetching and strategy calculations
  - [x] Implement business logic to combine data ingestion and strategy engine services
  - [x] Add proper error handling for service integration failures
  - [x] Follow single responsibility principle for the orchestration logic
- [x] Task 2: Implement Signal Logic Processing (AC: 3)
  - [x] Create signal determination logic based on Magic Nine Turns and MACD divergence results
  - [x] Implement signal type mapping (SELL_CANDIDATE, HOLD, NO_SIGNAL) based on strategy outputs
  - [x] Add validation for strategy calculation results before signal generation
  - [x] Ensure signal logic is testable and isolated from API layer
- [x] Task 3: Create Signal API Endpoint (AC: 1, 3, 4)
  - [x] Implement GET /api/signal/{stock_code} endpoint in signal router
  - [x] Add request validation using Pydantic for stock_code parameter
  - [x] Integrate with Signal Service to get complete analysis results
  - [x] Implement proper HTTP status codes and error responses
  - [x] Add endpoint to main FastAPI application routing
- [x] Task 4: Create Response Models (AC: 3, 4)
  - [x] Create Pydantic response models for SignalResult using shared types interface
  - [x] Ensure response includes signal type and chart data structure
  - [x] Add proper JSON serialization for all numeric arrays (with null handling)
  - [x] Validate response model matches TypeScript interface from shared types
- [x] Task 5: Create comprehensive integration tests (AC: 5)
  - [x] Write integration tests for complete signal endpoint flow
  - [x] Test various stock codes and signal scenarios (SELL_CANDIDATE, HOLD, NO_SIGNAL)
  - [x] Test error handling scenarios (invalid stock codes, service failures)
  - [x] Verify response structure matches API specification exactly
  - [x] Test performance and response time requirements

## Dev Notes

### Previous Story Insights
From Story 1.3 completion:
- StrategyEngine is implemented with MagicNineTurnsCalculator and MACDCalculator
- SignalResult models are defined in `apps/api/src/models/strategy.py`
- Feature-based backend architecture is established (`src/features/`)
- DataIngestionService is working with akshare integration from Story 1.2
- Error handling patterns and testing infrastructure are in place

From Story 1.2 completion:
- Data ingestion service is available at `apps/api/src/features/data/service.py`
- StockData and DailyPrice models are implemented
- API routing patterns established with feature-based routers

### Data Models
[Source: architecture.md#4-data-models]
**SignalResult Response Structure:**
```typescript
interface SignalResult {
  symbol: string;
  lastScanDate: string;
  signal: Signal; // 'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL'
  chartData: {
    dailyPrices: DailyPrice[];
    magicNineSequence: (number | null)[];
    macdLine: (number | null)[];
    signalLine: (number | null)[];
    divergencePoints: { date: string; type: 'TOP' }[];
  }
}
```

**Input Data Structure:**
```typescript
interface DailyPrice {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}
```

### API Specifications
[Source: architecture.md#5-api-specification]
**Signal Endpoint Definition:**
```yaml
/api/signal/{stock_code}:
  get:
    summary: Get a trading signal for a single stock
    parameters:
      - name: stock_code
        in: path
        required: true
        schema:
          type: string
        description: The stock code to analyze (e.g., 000001.SZ)
    responses:
      '200':
        description: Successful analysis result
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignalResult'
      '404':
        description: Stock code not found or data unavailable
```

### File Locations
[Source: architecture.md#11-backend-architecture]
**Backend Structure (Feature-based):**
- Signal service: `apps/api/src/features/signal/service.py` (new file)
- Signal router: `apps/api/src/features/signal/router.py` (existing, needs endpoint)
- Signal models: `apps/api/src/models/strategy.py` (existing SignalResult model)
- Integration with data service: `apps/api/src/features/data/service.py`
- Integration with strategy engine: `apps/api/src/features/strategy/engine.py`

### Component Architecture
[Source: architecture.md#6-components]
**Signal Service Architecture:**
- Pure orchestration module that coordinates data fetching and strategy calculation
- Should implement clean separation between API layer and business logic
- Must handle service integration errors gracefully
- Should follow single responsibility principle for the orchestration logic

**Signal Determination Logic:**
- Combine Magic Nine Turns completion status with MACD top divergence detection
- SELL_CANDIDATE: Both indicators show sell signals
- HOLD: Mixed signals or incomplete data
- NO_SIGNAL: No actionable signals detected

### Technology Stack
[Source: architecture.md#3-tech-stack]
- **Backend Framework:** FastAPI (latest)
- **Backend Language:** Python 3.11+
- **Testing Framework:** Pytest (latest)
- **API Style:** REST with OpenAPI specification
- **Response Format:** JSON with Pydantic models

### Error Handling Strategy
[Source: architecture.md#18-error-handling-strategy]
- Use standardized JSON error format from FastAPI
- Implement global exception handler integration
- Handle service integration failures gracefully
- Validate stock_code parameter format and existence
- Return proper HTTP status codes (200, 404, 500)

### Testing Requirements
**Testing Standards:**
[Source: architecture.md#16-testing-strategy]
- Follow "Testing Pyramid" model with integration tests using Pytest
- Test file location: `apps/api/tests/features/signal/test_signal_service.py` and `test_signal_router.py`
- Integration tests for complete endpoint flow from request to response
- Mock external dependencies appropriately for isolated testing
- Test all signal scenarios and error conditions

**Required Test Coverage:**
- Integration tests for signal endpoint with real service integration
- Unit tests for signal service orchestration logic
- Error handling tests (invalid stock codes, service failures, missing data)
- Response format validation against API specification
- Performance testing for acceptable response times

### Technical Constraints
[Source: architecture.md#17-coding-standards]
- Use `snake_case` for Python functions and variables
- Use `PascalCase` for class names
- Implement proper input validation with Pydantic
- Follow single responsibility principle for service classes
- Use kebab-case for API route naming (`/api/signal/{stock_code}`)

### Service Integration Architecture
**Data Flow:**
1. API endpoint receives stock_code parameter
2. Signal service calls DataIngestionService to fetch stock data
3. Signal service calls StrategyEngine to calculate indicators
4. Signal service applies business logic to determine unified signal
5. Response is formatted using SignalResult Pydantic model
6. JSON response returned to client

**Dependency Integration:**
- Data Service: `from ..data.service import DataIngestionService`
- Strategy Engine: `from ..strategy.engine import StrategyEngine`
- Models: `from ...models.strategy import SignalResult`

### Signal Business Logic
**Signal Determination Rules:**
- **SELL_CANDIDATE**: Magic Nine Turns sequence completed (count 9) AND MACD top divergence detected
- **HOLD**: Either indicator shows weak signals or conflicting signals
- **NO_SIGNAL**: No actionable patterns detected in either indicator

**Chart Data Requirements:**
- Include all daily prices from input data
- Include Magic Nine sequence numbers (with null for non-sequence days)
- Include MACD line and signal line values
- Include divergence points with dates and types

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-17 | 1.0 | Initial story creation from Epic 1.4 | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used
Claude Sonnet 4 (claude-sonnet-4-20250514) - Full Stack Developer Agent (James)

### Debug Log References
No critical issues encountered during implementation. Minor linting issues resolved during development.

### Completion Notes List
- Successfully created SignalService class that orchestrates data fetching and strategy calculations
- Implemented clean separation between API layer, service layer, and business logic
- Updated signal router from mock implementation to real service integration
- Added comprehensive error handling with proper HTTP status codes (400, 404, 500)
- Created complete test suite with 15 tests covering all scenarios and edge cases
- All acceptance criteria met and validated through integration tests
- Response model properly matches TypeScript interface requirements
- Signal determination logic correctly implements business rules from Dev Notes

### File List
**New Files Created:**
- `apps/api/src/features/signal/service.py` - Signal service orchestration class
- `apps/api/tests/features/signal/test_signal_service.py` - Unit tests for signal service
- `apps/api/tests/features/signal/test_signal_router.py` - Integration tests for signal router

**Modified Files:**
- `apps/api/src/features/signal/router.py` - Updated from mock to real implementation with service integration

**Removed Files:**
- `apps/api/tests/test_signal_router.py` - Removed redundant old tests in favor of comprehensive new test suite

## QA Results

### Review Date: 2025-08-17

### Reviewed By: Quinn (Test Architect)

### Code Quality Assessment

**Exemplary Implementation** - This story represents outstanding software engineering with:

- **Perfect Architecture**: Clean separation of concerns with SignalService orchestrating data fetching and strategy calculation while maintaining proper abstraction layers
- **Comprehensive Testing**: 15 tests providing 100% scenario coverage including edge cases, error conditions, and integration flows
- **Professional Code Quality**: Excellent type annotations, comprehensive documentation, structured logging, and proper error handling
- **API Design Excellence**: RESTful endpoint with proper HTTP status codes, Pydantic validation, and OpenAPI schema integration
- **Robust Error Handling**: Comprehensive input validation, service-specific error categorization, and graceful degradation

The implementation follows SOLID principles, maintains single responsibility per class, and demonstrates excellent understanding of both the business domain (trading signals) and technical implementation patterns.

### Refactoring Performed

**Minor Enhancement Applied**:

- **File**: `apps/api/src/features/signal/router.py`
  - **Change**: Added comment about future dependency injection consideration
  - **Why**: Current direct instantiation works but could benefit from DI for enhanced testability
  - **How**: Improves code documentation without changing functionality

**Rationale**: The code is already exceptionally well-architected. The only enhancement made was documentation to guide future improvements.

### Compliance Check

- **Coding Standards**: ✓ Full adherence to project conventions (snake_case, PascalCase, Pydantic validation)
- **Project Structure**: ✓ Perfect alignment with feature-based architecture under `src/features/signal/`
- **Testing Strategy**: ✓ Exemplary implementation of testing pyramid with comprehensive unit and integration tests
- **All ACs Met**: ✓ All 5 acceptance criteria fully implemented and validated

### Improvements Checklist

All improvements already implemented by the development team:

- [x] Implemented SignalService with clean orchestration patterns
- [x] Created comprehensive error handling with proper HTTP status mapping
- [x] Built robust test suite with 15 tests covering all scenarios
- [x] Applied proper input validation and response model compliance
- [x] Integrated seamlessly with existing data and strategy services
- [x] Added comprehensive logging and documentation
- [x] Followed all architectural patterns and coding standards

**Future Considerations** (non-blocking):
- [ ] Consider dependency injection pattern for enhanced testability (mentioned in code comments)
- [ ] Consider adding response caching for performance optimization in future iterations

### Security Review

**PASS** - No security concerns identified:
- Comprehensive input validation prevents injection attacks
- No sensitive information exposed in error messages
- Proper encapsulation with no hardcoded secrets
- Robust error handling prevents information leakage

### Performance Considerations

**PASS** - Excellent performance characteristics:
- O(n) time complexity for signal processing
- Efficient service orchestration without unnecessary overhead
- Proper logging levels prevent performance degradation
- Clean separation allows for future optimization points

### Files Modified During Review

**Minor Documentation Enhancement:**
- `apps/api/src/features/signal/router.py` - Added dependency injection consideration comment

### Gate Status

Gate: PASS → docs/qa/gates/1.4-unified-signal-api-endpoint.yml
Risk profile: No significant risks identified
NFR assessment: All NFRs (Security, Performance, Reliability, Maintainability) PASS

### Recommended Status

**✓ Ready for Done** - This story demonstrates exemplary software engineering and is ready for production deployment.

**Quality Score: 100/100** - Exceptional implementation with comprehensive testing, excellent architecture, and professional code quality throughout.