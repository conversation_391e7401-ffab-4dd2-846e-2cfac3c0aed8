import React from 'react';
import { Filter, TrendingUp, TrendingDown, Circle, Eye, EyeOff } from 'lucide-react';

interface SignalControlsProps {
  filterByType: 'buy' | 'sell' | 'all';
  onFilterTypeChange: (type: 'buy' | 'sell' | 'all') => void;
  minConfidence: number;
  onMinConfidenceChange: (confidence: number) => void;
  showTooltips: boolean;
  onShowTooltipsChange: (show: boolean) => void;
  signalCount: {
    total: number;
    buy: number;
    sell: number;
    filtered: number;
  };
  strategies?: string[];
  selectedStrategies?: string[];
  onStrategyToggle?: (strategy: string) => void;
}

export const SignalControls: React.FC<SignalControlsProps> = ({
  filterByType,
  onFilterTypeChange,
  minConfidence,
  onMinConfidenceChange,
  showTooltips,
  onShowTooltipsChange,
  signalCount,
  strategies = [],
  selectedStrategies = [],
  onStrategyToggle
}) => {
  const confidenceOptions = [
    { value: 0, label: 'All' },
    { value: 0.3, label: '30%+' },
    { value: 0.5, label: '50%+' },
    { value: 0.7, label: '70%+' },
    { value: 0.9, label: '90%+' }
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-center space-x-2 mb-4">
        <Filter className="w-4 h-4 text-gray-600" />
        <h3 className="text-sm font-semibold text-gray-800">Signal Controls</h3>
      </div>

      <div className="space-y-4">
        {/* Signal Type Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-2">
            Signal Type
          </label>
          <div className="flex space-x-2">
            <button
              onClick={() => onFilterTypeChange('all')}
              className={`flex items-center space-x-1 px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                filterByType === 'all'
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <Circle className="w-3 h-3" />
              <span>All ({signalCount.total})</span>
            </button>
            
            <button
              onClick={() => onFilterTypeChange('buy')}
              className={`flex items-center space-x-1 px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                filterByType === 'buy'
                  ? 'bg-green-100 text-green-700 border border-green-300'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <TrendingUp className="w-3 h-3" />
              <span>Buy ({signalCount.buy})</span>
            </button>
            
            <button
              onClick={() => onFilterTypeChange('sell')}
              className={`flex items-center space-x-1 px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                filterByType === 'sell'
                  ? 'bg-red-100 text-red-700 border border-red-300'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <TrendingDown className="w-3 h-3" />
              <span>Sell ({signalCount.sell})</span>
            </button>
          </div>
        </div>

        {/* Confidence Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-2">
            Min Confidence
          </label>
          <select
            value={minConfidence}
            onChange={(e) => onMinConfidenceChange(Number(e.target.value))}
            className="w-full px-3 py-1 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {confidenceOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Strategy Filter */}
        {strategies.length > 0 && onStrategyToggle && (
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              Strategies
            </label>
            <div className="space-y-1">
              {strategies.map((strategy) => (
                <label key={strategy} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectedStrategies.includes(strategy)}
                    onChange={() => onStrategyToggle(strategy)}
                    className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-xs text-gray-700 capitalize">
                    {strategy.replace(/_/g, ' ')}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Display Options */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-2">
            Display Options
          </label>
          <button
            onClick={() => onShowTooltipsChange(!showTooltips)}
            className={`flex items-center space-x-2 px-3 py-1 rounded-md text-xs font-medium transition-colors ${
              showTooltips
                ? 'bg-blue-100 text-blue-700 border border-blue-300'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {showTooltips ? (
              <Eye className="w-3 h-3" />
            ) : (
              <EyeOff className="w-3 h-3" />
            )}
            <span>Show Tooltips</span>
          </button>
        </div>

        {/* Signal Count Summary */}
        <div className="pt-2 border-t border-gray-200">
          <div className="text-xs text-gray-600">
            Showing <span className="font-semibold text-gray-800">{signalCount.filtered}</span> of{' '}
            <span className="font-semibold text-gray-800">{signalCount.total}</span> signals
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignalControls;