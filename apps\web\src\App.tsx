import { Routes, Route } from 'react-router-dom'
import { Layout } from '@/components/Layout'
import { AnalysisPage } from '@/features/Analysis/AnalysisPage'
// SignalHistoryPage removed - transitioning to real-time signal computation
import { ScreenerPage } from '@/features/Screener/ScreenerPage'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<AnalysisPage />} />
        <Route path="/analysis" element={<AnalysisPage />} />
        {/* SignalHistoryPage route removed - transitioning to real-time signal computation */}
        <Route path="/screener" element={<ScreenerPage />} />
      </Routes>
    </Layout>
  )
}

export default App