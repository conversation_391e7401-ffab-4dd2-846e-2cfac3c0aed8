import { Routes, Route } from 'react-router-dom'
import { Layout } from '@/components/Layout'
import { AnalysisPage } from '@/features/Analysis/AnalysisPage'
import { SignalHistoryPage } from '@/features/Analysis/SignalHistoryPage'
import { ScreenerPage } from '@/features/Screener/ScreenerPage'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<AnalysisPage />} />
        <Route path="/analysis" element={<AnalysisPage />} />
        <Route path="/signal-history" element={<SignalHistoryPage />} />
        <Route path="/screener" element={<ScreenerPage />} />
      </Routes>
    </Layout>
  )
}

export default App