"""Repository for screener results database operations."""

import logging
from datetime import datetime, timedelta
from typing import Any

from sqlalchemy import desc
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...models.database import ScreenerResult

logger = logging.getLogger(__name__)


class ScreenerRepository:
    """Repository for managing screener results in the database."""

    def __init__(self, db_session: Session | None = None):
        """Initialize repository with optional database session."""
        self.db = db_session
        self._should_close_session = db_session is None

    def __enter__(self) -> "ScreenerRepository":
        """Context manager entry."""
        if self.db is None:
            self.db = next(get_db())
        return self

    def __exit__(self, exc_type: type, exc_val: Exception, exc_tb: object) -> None:
        """Context manager exit."""
        if self._should_close_session and self.db:
            self.db.close()

    def save_screening_results(self, results: list[dict[str, Any]], scan_timestamp: str) -> int:
        """
        Save screening results to database.

        Args:
            results: List of dicts with 'symbol' and optional 'companyName'
            scan_timestamp: ISO 8601 timestamp string

        Returns:
            Number of records inserted
        """
        logger.info(f"Saving {len(results)} screening results for timestamp {scan_timestamp}")

        assert self.db is not None, "Database session is required"

        try:
            # Create ScreenerResult objects
            screener_results = []
            for result in results:
                screener_result = ScreenerResult(
                    scan_timestamp=scan_timestamp,
                    symbol=result['symbol'],
                    company_name=result.get('companyName', result.get('company_name'))
                )
                screener_results.append(screener_result)

            # Bulk insert
            self.db.add_all(screener_results)
            self.db.commit()

            logger.info(f"Successfully saved {len(screener_results)} screening results")
            return len(screener_results)

        except Exception as e:
            logger.error(f"Error saving screening results: {str(e)}")
            self.db.rollback()
            raise

    def get_latest_screening_results(self) -> list[dict[str, Any]]:
        """
        Get the most recent screening results.

        Returns:
            List of dicts with symbol and companyName
        """
        assert self.db is not None, "Database session is required"

        try:
            # Get the most recent scan timestamp
            latest_timestamp = (
                self.db.query(ScreenerResult.scan_timestamp)
                .order_by(desc(ScreenerResult.scan_timestamp))
                .first()
            )

            if not latest_timestamp:
                logger.info("No screening results found in database")
                return []

            # Get all results from the latest scan
            results = (
                self.db.query(ScreenerResult)
                .filter(ScreenerResult.scan_timestamp == latest_timestamp[0])
                .all()
            )

            # Convert to dict format
            result_dicts = []
            for result in results:
                result_dicts.append({
                    'symbol': result.symbol,
                    'companyName': result.company_name or result.symbol
                })

            logger.info(f"Retrieved {len(result_dicts)} latest screening results")
            return result_dicts

        except Exception as e:
            logger.error(f"Error retrieving latest screening results: {str(e)}")
            raise

    def cleanup_old_results(self, days_to_keep: int = 30) -> int:
        """
        Remove screening results older than specified days.

        Args:
            days_to_keep: Number of days to keep results (default: 30)

        Returns:
            Number of records deleted
        """
        assert self.db is not None, "Database session is required"

        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_iso = cutoff_date.isoformat()

            # Count records to be deleted
            count_query = (
                self.db.query(ScreenerResult)
                .filter(ScreenerResult.scan_timestamp < cutoff_iso)
            )
            count_to_delete = count_query.count()

            if count_to_delete == 0:
                logger.info("No old screening results to clean up")
                return 0

            # Delete old records
            deleted_count = count_query.delete()
            self.db.commit()

            logger.info(f"Cleaned up {deleted_count} old screening results (older than {days_to_keep} days)")
            return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up old screening results: {str(e)}")
            self.db.rollback()
            raise

    def get_screening_history(self, limit: int = 10) -> list[dict[str, Any]]:
        """
        Get screening history with timestamps and counts.

        Args:
            limit: Maximum number of historical runs to return

        Returns:
            List of dicts with scan_timestamp and result_count
        """
        assert self.db is not None, "Database session is required"

        try:
            # Get unique timestamps with counts
            from sqlalchemy import func

            history = (
                self.db.query(
                    ScreenerResult.scan_timestamp,
                    func.count(ScreenerResult.id).label('result_count')
                )
                .group_by(ScreenerResult.scan_timestamp)
                .order_by(desc(ScreenerResult.scan_timestamp))
                .limit(limit)
                .all()
            )

            result_list = []
            for timestamp, count in history:
                result_list.append({
                    'scan_timestamp': timestamp,
                    'result_count': count
                })

            logger.info(f"Retrieved {len(result_list)} screening history entries")
            return result_list

        except Exception as e:
            logger.error(f"Error retrieving screening history: {str(e)}")
            raise
