"""Integration tests for complete screening workflow."""

from datetime import datetime
from typing import Any
from unittest.mock import Mock, patch

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from src.features.screener.router import router
from src.features.screener.scheduler import ScreeningScheduler
from src.features.screener.screening_service import ScreeningService
from src.models.strategy import ChartData, SignalResult


class TestScreenerIntegration:
    """Integration test cases for complete screening workflow."""

    @pytest.fixture
    def mock_signal_result_sell(self) -> SignalResult:
        """Mock SignalResult with SELL_CANDIDATE signal."""
        return SignalResult(
            symbol="000001.SZ",
            last_scan_date=datetime.now().isoformat(),
            signal="SELL_CANDIDATE",
            chart_data=ChartData(
                daily_prices=[],
                magic_nine_sequence=[],
                macd_line=[],
                signal_line=[],
                divergence_points=[]
            )
        )

    @pytest.fixture
    def mock_signal_result_hold(self) -> SignalResult:
        """Mock SignalResult with HOLD signal."""
        return SignalResult(
            symbol="000002.SZ",
            last_scan_date=datetime.now().isoformat(),
            signal="HOLD",
            chart_data=ChartData(
                daily_prices=[],
                magic_nine_sequence=[],
                macd_line=[],
                signal_line=[],
                divergence_points=[]
            )
        )

    @pytest.mark.asyncio
    async def test_complete_screening_workflow(self, mock_signal_result_sell: SignalResult, mock_signal_result_hold: SignalResult) -> None:
        """Test complete end-to-end screening workflow."""
        # Mock stock list
        test_stocks = ["000001.SZ", "000002.SZ", "000003.SZ"]

        # Mock signal service responses
        def mock_get_signal(stock_code: str) -> SignalResult:
            if stock_code == "000001.SZ":
                return mock_signal_result_sell  # Should be included in results
            elif stock_code == "000002.SZ":
                return mock_signal_result_hold  # Should not be included
            else:
                raise ValueError("Invalid stock data")  # Should be handled gracefully

        screening_service = ScreeningService()

        with patch.object(screening_service.signal_service, 'get_signal_for_stock', side_effect=mock_get_signal), \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository context manager
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 1
            mock_repo.cleanup_old_results.return_value = 5
            mock_repo.get_latest_screening_results.return_value = [
                {'symbol': '000001.SZ', 'companyName': 'Ping An Bank Co., Ltd.'}
            ]
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            # Run screening
            result = await screening_service.run_screening(test_stocks)

            # Verify screening results
            assert result['total_stocks_processed'] == 3
            assert result['signals_found'] == 1
            assert result['results_saved'] == 1
            assert len(result['results']) == 1
            assert result['results'][0]['symbol'] == '000001.SZ'

            # Verify repository interactions
            mock_repo.save_screening_results.assert_called_once()
            mock_repo.cleanup_old_results.assert_called_once_with(days_to_keep=30)

            # Test retrieving latest results
            latest_results = screening_service.get_latest_screening_results()
            assert len(latest_results) == 1
            assert latest_results[0]['symbol'] == '000001.SZ'

    @pytest.mark.asyncio
    async def test_scheduler_integration(self, mock_signal_result_sell: SignalResult) -> None:
        """Test scheduler integration with screening service."""
        scheduler = ScreeningScheduler()

        # Mock the screening service's run_screening method
        mock_screening_result = {
            'scan_timestamp': datetime.now().isoformat(),
            'total_stocks_processed': 10,
            'signals_found': 2,
            'results_saved': 2,
            'errors_encountered': 0,
            'processing_time_seconds': 1.5,
            'results': [
                {'symbol': '000001.SZ', 'companyName': 'Ping An Bank Co., Ltd.'},
                {'symbol': '000002.SZ', 'companyName': 'China Vanke Co., Ltd.'}
            ]
        }

        with patch.object(scheduler.screening_service, 'run_screening', return_value=mock_screening_result):
            # Test immediate screening job
            result = await scheduler.run_immediate_screening()

            # Verify job result
            assert result['status'] == 'completed'
            assert result['total_stocks_processed'] == 10
            assert result['signals_found'] == 2
            assert 'job_start_time' in result
            assert 'job_end_time' in result

            # Verify job was recorded
            assert scheduler.last_job_result is not None
            assert scheduler.last_job_result['status'] == 'completed'

    @pytest.mark.asyncio
    async def test_scheduler_error_handling_and_retry(self) -> None:
        """Test scheduler error handling and retry logic."""
        scheduler = ScreeningScheduler()

        # Mock screening service to fail twice then succeed
        call_count = 0
        def mock_screening_failure(*args: Any, **kwargs: Any) -> dict[str, Any]:
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise RuntimeError("Temporary service failure")
            return {
                'scan_timestamp': datetime.now().isoformat(),
                'total_stocks_processed': 5,
                'signals_found': 1,
                'results_saved': 1,
                'errors_encountered': 0,
                'processing_time_seconds': 0.5,
                'results': []
            }

        with patch.object(scheduler.screening_service, 'run_screening', side_effect=mock_screening_failure), \
             patch('asyncio.sleep', return_value=None):  # Speed up retry delays

            # Test job with retry logic
            result = await scheduler._run_screening_job()

            # Should succeed after retries
            assert result['status'] == 'completed'
            assert result['attempt'] == 3  # Third attempt succeeded
            assert call_count == 3

    @pytest.mark.asyncio
    async def test_scheduler_max_retries_exhausted(self) -> None:
        """Test scheduler behavior when all retries are exhausted."""
        scheduler = ScreeningScheduler()

        with patch.object(scheduler.screening_service, 'run_screening', side_effect=RuntimeError("Persistent failure")), \
             patch('asyncio.sleep', return_value=None):  # Speed up retry delays

            # Test job that fails all retries
            with pytest.raises(RuntimeError, match="Persistent failure"):
                await scheduler._run_screening_job()

            # Verify failure was recorded
            assert scheduler.last_job_result is not None
            assert scheduler.last_job_result['status'] == 'failed'
            assert scheduler.last_job_result['total_attempts'] == 3
            assert 'error' in scheduler.last_job_result

    def test_scheduler_lifecycle(self) -> None:
        """Test scheduler start/stop lifecycle."""
        scheduler = ScreeningScheduler()

        # Initially not running
        assert not scheduler.is_running

        # Start scheduler
        scheduler.start_scheduler()
        assert scheduler.is_running

        # Schedule a job
        job = scheduler.schedule_daily_screening(hour=10, minute=0)
        assert job.id == 'daily_screening'

        # Check status
        status = scheduler.get_scheduler_status()
        assert status['scheduler_running'] is True
        assert len(status['active_jobs']) == 1
        assert status['active_jobs'][0]['id'] == 'daily_screening'

        # Remove job
        success = scheduler.remove_job('daily_screening')
        assert success is True

        # Verify job removed
        status = scheduler.get_scheduler_status()
        assert len(status['active_jobs']) == 0

        # Stop scheduler
        scheduler.stop_scheduler()
        assert not scheduler.is_running

    @pytest.mark.asyncio
    async def test_caching_across_multiple_runs(self, mock_signal_result_sell: SignalResult, mock_signal_result_hold: SignalResult) -> None:
        """Test that caching works correctly across multiple screening runs."""
        screening_service = ScreeningService(cache_ttl_hours=1)  # 1 hour cache
        test_stocks = ["000001.SZ", "000002.SZ"]

        # Mock signal service responses
        def mock_get_signal(stock_code: str) -> SignalResult:
             if stock_code == "000001.SZ":
                 return mock_signal_result_sell  # Simulate sell signal
             else:
                 return mock_signal_result_hold  # Simulate hold signal

        with patch.object(screening_service.signal_service, 'get_signal_for_stock', side_effect=mock_get_signal) as mock_signal, \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 1
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            # First run - should make API calls
            result1 = await screening_service.run_screening(test_stocks)
            first_run_calls = mock_signal.call_count
            assert first_run_calls == 2
            assert result1['signals_found'] == 1

            # Reset mock
            mock_signal.reset_mock()

            # Second run - should use cache
            result2 = await screening_service.run_screening(test_stocks)
            second_run_calls = mock_signal.call_count
            assert second_run_calls == 0  # No API calls due to cache
            assert result2['signals_found'] == 1  # Same results from cache

            # Verify cache stats
            cache_stats = screening_service.get_cache_stats()
            assert cache_stats['valid_entries'] == 2
            assert cache_stats['expired_entries'] == 0

    @pytest.mark.asyncio
    async def test_performance_with_large_stock_list(self, mock_signal_result_hold: SignalResult) -> None:
        """Test performance with large stock list (simulated CSI 300)."""
        screening_service = ScreeningService(max_concurrent=10, rate_limit_delay=0.001)

        # Create large stock list (100 stocks to simulate performance)
        large_stock_list = [f"{str(i).zfill(6)}.{'SZ' if i % 2 == 0 else 'SH'}" for i in range(1, 101)]

        with patch.object(screening_service.signal_service, 'get_signal_for_stock', return_value=mock_signal_result_hold), \
             patch('src.features.screener.screening_service.ScreenerRepository') as mock_repo_class:

            # Mock repository
            mock_repo = Mock()
            mock_repo.save_screening_results.return_value = 0
            mock_repo.cleanup_old_results.return_value = 0
            mock_repo_class.return_value.__enter__.return_value = mock_repo
            mock_repo_class.return_value.__exit__.return_value = None

            # Run screening and measure performance
            import time
            start_time = time.time()
            result = await screening_service.run_screening(large_stock_list)
            end_time = time.time()

            processing_time = end_time - start_time

            # Performance assertions
            assert result['total_stocks_processed'] == 100
            assert processing_time < 5  # Should complete within 5 seconds

            # Calculate throughput
            throughput = result['total_stocks_processed'] / processing_time
            assert throughput > 20  # At least 20 stocks per second


class TestScreenerAPIIntegration:
    """Integration tests for screener API endpoints."""

    @pytest.fixture(autouse=True)
    def clear_cache(self) -> None:
        """Clear the results cache before each test to ensure test isolation."""
        from src.features.screener.router import _results_cache
        _results_cache["data"] = None
        _results_cache["timestamp"] = None

    @pytest.fixture
    def api_app(self) -> FastAPI:
        """Create test FastAPI app with screener router."""
        test_app = FastAPI()
        test_app.include_router(router, prefix="/api")
        return test_app

    @pytest.fixture
    def api_client(self, api_app: FastAPI) -> TestClient:
        """Create test client for API tests."""
        return TestClient(api_app)

    @patch('src.features.screener.router.ScreenerRepository')
    def test_api_endpoint_integration_workflow(self, mock_repo_class: Mock, api_client: TestClient) -> None:
        """Test complete API endpoint workflow from request to response."""
        # Setup mock repository with realistic data
        mock_repo = Mock()
        mock_repo.__enter__ = Mock(return_value=mock_repo)
        mock_repo.__exit__ = Mock(return_value=None)

        test_results = [
            {"symbol": "000001.SZ", "companyName": "Ping An Bank Co., Ltd."},
            {"symbol": "000002.SZ", "companyName": "China Vanke Co., Ltd."},
            {"symbol": "000858.SZ", "companyName": "Wuliangye Yibin Co., Ltd."}
        ]
        mock_repo.get_latest_screening_results.return_value = test_results
        mock_repo_class.return_value = mock_repo

        # Test API request
        response = api_client.get("/api/screener/results")

        # Verify response structure
        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) == 3

        # Verify each result matches ScreenerItem interface
        for item in response_data:
            assert "symbol" in item
            assert "companyName" in item
            assert isinstance(item["symbol"], str)
            assert isinstance(item["companyName"], str)
            assert item["symbol"].endswith((".SZ", ".SH", ".HK"))

        # Verify repository was called correctly
        mock_repo.get_latest_screening_results.assert_called_once()

    @patch('src.features.screener.router.ScreenerRepository')
    def test_api_endpoint_with_empty_results(self, mock_repo_class: Mock, api_client: TestClient) -> None:
        """Test API endpoint behavior with empty screening results."""
        # Setup mock repository with empty results
        mock_repo = Mock()
        mock_repo.__enter__ = Mock(return_value=mock_repo)
        mock_repo.__exit__ = Mock(return_value=None)
        mock_repo.get_latest_screening_results.return_value = []
        mock_repo_class.return_value = mock_repo

        # Test API request
        response = api_client.get("/api/screener/results")

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) == 0

    @patch('src.features.screener.router.ScreenerRepository')
    def test_api_endpoint_database_error_handling(self, mock_repo_class: Mock, api_client: TestClient) -> None:
        """Test API endpoint error handling with database issues."""
        # Setup mock repository to raise database error
        mock_repo = Mock()
        mock_repo.__enter__ = Mock(return_value=mock_repo)
        mock_repo.__exit__ = Mock(return_value=None)
        mock_repo.get_latest_screening_results.side_effect = Exception("Database connection lost")
        mock_repo_class.return_value = mock_repo

        # Test API request
        response = api_client.get("/api/screener/results")

        # Verify error response
        assert response.status_code == 500
        error_data = response.json()
        assert "detail" in error_data
        assert "Failed to retrieve screening results" in error_data["detail"]
        assert "Database connection lost" in error_data["detail"]

    @patch('src.features.screener.router.ScreenerRepository')
    def test_api_endpoint_caching_integration(self, mock_repo_class: Mock, api_client: TestClient) -> None:
        """Test that API endpoint caching works in integration scenario."""
        from src.features.screener.router import _results_cache

        # Clear cache
        _results_cache["data"] = None
        _results_cache["timestamp"] = None

        # Setup mock repository
        mock_repo = Mock()
        mock_repo.__enter__ = Mock(return_value=mock_repo)
        mock_repo.__exit__ = Mock(return_value=None)

        test_results = [{"symbol": "000001.SZ", "companyName": "Test Company"}]
        mock_repo.get_latest_screening_results.return_value = test_results
        mock_repo_class.return_value = mock_repo

        # First request - should hit repository
        response1 = api_client.get("/api/screener/results")
        assert response1.status_code == 200
        assert response1.json() == test_results

        # Verify repository was called
        assert mock_repo.get_latest_screening_results.call_count == 1

        # Second request - should hit cache
        response2 = api_client.get("/api/screener/results")
        assert response2.status_code == 200
        assert response2.json() == test_results

        # Verify repository was not called again
        assert mock_repo.get_latest_screening_results.call_count == 1

        # Verify cache contains data
        assert _results_cache["data"] == test_results
        assert _results_cache["timestamp"] is not None

    @patch('src.features.screener.router.get_scheduler')
    @patch('src.features.screener.router.ScreenerRepository')
    def test_api_cache_invalidation_integration(self, mock_repo_class: Mock, mock_get_scheduler: Mock, api_client: TestClient) -> None:
        """Test that cache invalidation works when screening is triggered."""
        from src.features.screener.router import _results_cache

        # Setup cache with initial data
        initial_data = [{"symbol": "000001.SZ", "companyName": "Initial Company"}]
        _results_cache["data"] = initial_data
        _results_cache["timestamp"] = datetime.now()

        # Setup mock scheduler
        mock_scheduler = Mock()
        # Mock the async method properly
        async def mock_run_immediate_screening() -> dict[str, Any]:
            return {
                "status": "completed",
                "signals_found": 2,
                "total_stocks_processed": 100
            }
        mock_scheduler.run_immediate_screening = mock_run_immediate_screening
        mock_get_scheduler.return_value = mock_scheduler

        # Trigger screening run to invalidate cache
        screening_response = api_client.post("/api/screener/run")
        assert screening_response.status_code == 200

        # Verify cache was invalidated
        assert _results_cache["data"] is None
        assert _results_cache["timestamp"] is None

        # Setup fresh data for next request
        fresh_data = [{"symbol": "000002.SZ", "companyName": "Fresh Company"}]
        mock_repo = Mock()
        mock_repo.__enter__ = Mock(return_value=mock_repo)
        mock_repo.__exit__ = Mock(return_value=None)
        mock_repo.get_latest_screening_results.return_value = fresh_data
        mock_repo_class.return_value = mock_repo

        # Next results request should fetch fresh data
        results_response = api_client.get("/api/screener/results")
        assert results_response.status_code == 200
        assert results_response.json() == fresh_data
        assert results_response.json() != initial_data
