# Comprehensive Trading Agent Web Application Product Requirements Document (PRD)

### 1. Goals and Background Context

#### Goals

The primary goals of this project are to:
* Develop and launch a Minimum Viable Product (MVP) within 3-4 months to validate the core trading strategy.
* Provide users with a reliable, automated tool that reduces the time spent on manual stock analysis.
* Increase trader confidence by generating high-quality signals for identifying price tops.
* Help users discover trading opportunities through an automated screening tool.
* Establish a scalable technical foundation for future feature enhancements.

#### Background Context

Traders in the Chinese stock market face a significant challenge in accurately timing their sell orders. Relying on individual technical indicators often leads to false signals, resulting in financial losses or missed opportunities. Existing platforms lack the capability to automatically combine sophisticated indicators like "Magic Nine Turns" with MACD top divergence analysis, forcing traders into time-consuming and subjective manual interpretation.

This application will address this gap by providing a specialized tool that automates this unique, combined strategy. It will synthesize complex data into a single, actionable signal, empowering traders to make more informed, data-driven decisions with greater speed and confidence. The MVP is designed to test the core hypothesis that this automated signal provides a tangible analytical edge.

#### Change Log

| Date            | Version | Description                                | Author     |
| :-------------- | :------ | :----------------------------------------- | :--------- |
| August 17, 2025 | 1.0     | Initial PRD draft based on Project Brief. | John (PM) |

---

### 2. Requirements

#### Functional Requirements
1.  **FR1**: The system shall ingest Chinese stock market data for analysis using the `akshare` library.
2.  **FR2**: The system shall calculate the "Magic Nine Turns" technical indicator based on the ingested stock data.
3.  **FR3**: The system shall calculate the MACD indicator and include an algorithm to detect top divergence patterns.
4.  **FR4**: The system shall generate a unified trading signal by combining the results of the "Magic Nine Turns" and MACD divergence indicators.
5.  **FR5**: A user interface shall be provided for users to input a single Chinese stock code.
6.  **FR6**: The system shall display the unified trading signal and a visual chart with the relevant indicators overlaid for the requested stock.
7.  **FR7**: The system shall perform an automated daily scan of a predefined list of stocks (e.g., CSI 300).
8.  **FR8**: The system shall display a simple list of stocks that meet the criteria of the combined trading strategy from the automated scan.

#### Non-Functional Requirements
1.  **NFR1**: The application shall be a web-based platform.
2.  **NFR2**: The application's design shall be responsive, ensuring usability on both desktop and mobile browsers.
3.  **NFR3**: The application shall use near real-time data for its analysis to ensure signals are timely.
4.  **NFR4**: All charts presented to the user shall be interactive (e.g., allowing zoom, pan).
5.  **NFR5**: All sensitive credentials, such as the Tushare API token, must be securely stored and managed on the backend.

---

### 3. User Interface Design Goals

#### Overall UX Vision
A clean, data-dense, and highly responsive interface that prioritizes clarity and speed. The design should empower traders to quickly assess signals and make informed decisions without being overwhelmed by visual clutter or unnecessary steps.

#### Key Interaction Paradigms
* **Direct Input:** A prominent input field for stock codes, possibly with autocomplete suggestions, will be the primary entry point for analysis.
* **Interactive Charting:** Users will be able to pan, zoom, and toggle indicators on the charts to explore the data.
* **Signal Prominence:** The final generated trading signal (e.g., "Sell Signal Detected") will be the most visually prominent element on the analysis page.
* **Minimal Navigation:** The MVP will consist of two primary views (Analysis and Screener), with clear and simple navigation between them.

#### Core Screens and Views
* **Stock Analysis View:** A view where a user inputs a stock code and is presented with the generated signal and the detailed interactive chart.
* **Screener Results View:** A view that displays the daily list of stocks flagged by the automated screening process.

#### Accessibility
* **WCAG 2.1 AA:** The application should be designed to meet WCAG AA standards, ensuring it is usable by people with disabilities. This includes considerations for color contrast (especially on charts) and keyboard navigation.

#### Branding
* To be defined. The initial design should be clean, professional, and functional, using a simple and serious color palette suitable for a financial application. Signal colors (e.g., red for sell signals) should be used clearly and consistently.

#### Target Device and Platforms
* **Web Responsive:** The application will be a responsive web app, functional and usable across both modern desktop and mobile browsers.

---

### 4. Technical Assumptions

#### Repository Structure: Monorepo
* **Decision:** A **Monorepo** structure is recommended.
* **Rationale:** This will simplify development for the MVP by keeping the frontend and backend code in a single repository, making it easier to share code (like data types) and manage dependencies.

#### Service Architecture
* **Decision:** A **Monolith** architecture for the backend.
* **Rationale:** A single Python service is sufficient to handle the defined MVP scope and simplifies deployment and development compared to a microservices or serverless approach at this early stage.

#### Testing Requirements
* **Decision:** A minimum of **Unit and Integration tests** are required.
* **Rationale:** Unit tests will ensure the accuracy of the indicator calculations. Integration tests are critical to verify the data pipeline from `akshare` through the strategy engine to the API response.

#### Additional Technical Assumptions and Requests
* **Backend:** The backend will be built using **Python** (e.g., FastAPI or Flask) to leverage the required `akshare` and `Tushare` data libraries.
* **Database:** The MVP will use **SQLite** to minimize setup complexity, with a planned migration to **PostgreSQL** post-MVP.
* **Frontend:** The specific frontend framework (**React, Vue, etc.**) is still to be determined. The application will require a robust charting library (e.g., ECharts, Chart.js).

---

### 5. Epic List

* **Epic 1: Foundation & Core Signal Generation**
    * **Goal:** Establish the core project infrastructure and implement the backend strategy engine to generate a trading signal for a single stock, accessible via an API endpoint.
* **Epic 2: Analysis Interface & Visualization**
    * **Goal:** Develop the user-facing stock analysis interface, allowing users to input a stock code and visualize the generated trading signal on an interactive chart.
* **Epic 3: Automated Stock Screening**
    * **Goal:** Implement the automated stock screening tool to scan the market daily and present a list of potential trading opportunities to the user.

---

### Epic 1: Foundation & Core Signal Generation

This epic lays the groundwork for the entire application. It establishes the project structure and backend services, and crucially, it implements and validates the core data pipeline and proprietary trading signal logic. By the end of this epic, we will have a functional, testable API that can deliver the core value proposition, even without a user interface.

#### Story 1.1: Project Scaffolding
**As a** Developer, **I want** a new monorepo project initialized with backend and frontend placeholders, **so that** I have a consistent and clean structure for development.
**Acceptance Criteria:**
1.  A new Git repository is created.
2.  A monorepo structure is set up with `/apps/backend` and `/apps/frontend` directories.
3.  The backend app is initialized with a basic Python (e.g., FastAPI) "hello world" setup.
4.  The frontend app is initialized with a placeholder using a framework starter kit (e.g., `create-react-app`).
5.  A root-level script (e.g., `npm install`) successfully installs all dependencies for both apps.

#### Story 1.2: Data Ingestion Service
**As a** System, **I want** to fetch historical daily stock data for a specific stock code from `akshare`, **so that** the data is available for the strategy analysis engine.
**Acceptance Criteria:**
1.  A backend API endpoint (e.g., `GET /api/data/{stock_code}`) is created.
2.  When called, the endpoint successfully fetches historical daily data (e.g., open, high, low, close, volume) for the given stock code from the `akshare` library.
3.  The data is correctly parsed and returned in a JSON format.
4.  The endpoint handles errors gracefully if the stock code is invalid or `akshare` is unavailable.
5.  Unit tests verify the data fetching and parsing logic.

#### Story 1.3: Core Strategy Calculation
**As a** System, **I want** to apply the "Magic Nine Turns" and "MACD with top divergence" calculations to a given set of stock data, **so that** the raw indicator results are generated for signal processing.
**Acceptance Criteria:**
1.  A backend service/module is created that takes a time-series of stock data as input.
2.  The service correctly calculates the "Magic Nine Turns" sequence according to its technical definition.
3.  The service correctly calculates MACD values and programmatically identifies top divergence events.
4.  The service outputs the calculated results for both indicators in a structured format.
5.  Unit tests for both indicator calculations exist and pass using known inputs and expected outputs.

#### Story 1.4: Unified Signal API Endpoint
**As a** System, **I want** a single API endpoint that orchestrates the data fetching and strategy calculations to return a unified trading signal, **so that** the frontend has a simple, clean interface to consume.
**Acceptance Criteria:**
1.  A new backend API endpoint (e.g., `GET /api/signal/{stock_code}`) is created.
2.  The endpoint internally calls the data ingestion service and the strategy calculation service.
3.  The endpoint returns a simple JSON response indicating the final, combined signal (e.g., `{ "signal": "SELL_CANDIDATE", "details": {...} }`).
4.  The JSON response also includes the necessary data points for the frontend to render a chart (e.g., prices, indicator values).
5.  Integration tests confirm the end-to-end flow from request to a valid signal response.

---

### Epic 2: Analysis Interface & Visualization

This epic brings the core backend logic to life by creating the primary user-facing feature. It focuses on building a clean and effective user interface where a trader can input a stock code and immediately see the unified signal and a detailed, interactive chart. The goal is to provide a seamless and intuitive experience for visualizing the results of our proprietary strategy.

#### Story 2.1: Basic UI Layout
**As a** User, **I want** a basic application layout with navigation between the "Analysis" and "Screener" views, **so that** I can easily switch between the main tools.
**Acceptance Criteria:**
1.  A main application shell is created using the chosen frontend framework.
2.  A simple header or navigation bar is present containing links for "Analysis" and "Screener".
3.  Clicking the navigation links successfully switches between two distinct, initially empty, page components.
4.  The overall layout is responsively structured for both desktop and mobile viewports.

#### Story 2.2: Stock Input and API Connection
**As a** User, **I want** to enter a stock code into an input field and trigger the analysis, **so that** I can get a trading signal for a specific stock.
**Acceptance Criteria:**
1.  The "Analysis" view contains a text input field for a stock code and a submit button.
2.  Clicking the submit button triggers a call to the `GET /api/signal/{stock_code}` endpoint created in Epic 1.
3.  The frontend correctly receives and processes the JSON response from the API.
4.  A visual loading indicator is displayed while the API call is in progress.
5.  Clear error messages are displayed to the user if the API call fails or the stock code is invalid.

#### Story 2.3: Signal Display
**As a** User, **I want** to see the final trading signal displayed clearly and prominently, **so that** I can understand the analysis result at a glance.
**Acceptance Criteria:**
1.  After a successful API response, the unified signal (e.g., "SELL_CANDIDATE") is displayed in a prominent position on the "Analysis" view.
2.  The display style (e.g., color) changes based on the nature of the signal to provide an immediate visual cue.
3.  Any additional summary details from the API response are displayed in a clean, readable format.

#### Story 2.4: Interactive Chart Visualization
**As a** User, **I want** to see an interactive chart of the stock's price and indicators, **so that** I can visually verify and explore the data behind the signal.
**Acceptance Criteria:**
1.  A third-party charting library is successfully integrated into the "Analysis" view.
2.  The chart renders the stock's historical price data received from the API.
3.  The "Magic Nine Turns" sequence and the MACD indicator with divergence markers are correctly overlaid on the price chart.
4.  The chart is interactive, supporting, at a minimum, zooming and panning.
5.  The chart is responsive and renders correctly on both desktop and mobile screens.

---

### Epic 3: Automated Stock Screening

This epic delivers the second major feature of the application, transforming it from a single-stock analysis tool into a market discovery engine. It focuses on building the backend process to automatically scan a wide range of stocks and the frontend view to display the results. This provides immense value to users by proactively bringing trading opportunities to their attention.

#### Story 3.1: Backend Screening Service
**As a** System, **I want** to periodically run the unified signal strategy against a predefined list of stock codes, **so that** I can identify all stocks that currently meet the criteria for a potential trade.
**Acceptance Criteria:**
1.  A new backend service is created that takes a list of stock codes (e.g., the CSI 300) as input.
2.  The service iterates through the list, applying the core signal generation logic (from Epic 1) to each stock.
3.  The service collects and stores the list of all stock codes that produced a positive signal.
4.  The process is optimized to handle a large number of stocks efficiently.
5.  A mechanism is in place to trigger this service on a recurring schedule (e.g., once per day).

#### Story 3.2: Screener Results API Endpoint
**As a** System, **I want** an API endpoint that provides the latest results from the stock screener, **so that** the frontend application can display these results to the user.
**Acceptance Criteria:**
1.  A new backend API endpoint (e.g., `GET /api/screener/results`) is created.
2.  The endpoint retrieves and returns a JSON array of the most recent list of stocks identified by the screening service.
3.  The response includes the necessary data for each stock to be displayed in a list (e.g., stock code, company name).
4.  The endpoint serves a cached or pre-computed result to ensure a fast response time.

#### Story 3.3: Screener Results UI
**As a** User, **I want** to see a clear list of stocks that have been flagged by the screener, **so that** I can discover and investigate potential trading opportunities.
**Acceptance Criteria:**
1.  The "Screener" view calls the `GET /api/screener/results` endpoint when loaded.
2.  The returned list of stocks is displayed in a simple, readable table or list format.
3.  Each stock code in the list is a clickable link.
4.  Clicking a stock code navigates the user to the "Analysis" view and automatically populates the input with that stock code for a detailed view.
5.  A timestamp is displayed on the page indicating when the results were last updated.

---
### 6. Checklist Results Report

#### PRD & EPIC VALIDATION SUMMARY

**Executive Summary**
The Product Requirements Document (PRD) is comprehensive, well-structured, and shows a high degree of readiness for the next phase. The MVP scope is clear, and the epics and stories are logically sequenced to mitigate risk and deliver incremental value. There are no critical deficiencies blocking progress. The document is **Ready for Architect**.

**Category Statuses**

| Category                        | Status    | Critical Issues                                                  |
| :------------------------------ | :-------- | :--------------------------------------------------------------- |
| 1. Problem Definition & Context | ✅ PASS   | None                                                             |
| 2. MVP Scope Definition         | ✅ PASS   | None                                                             |
| 3. User Experience Requirements | ⚠️ PARTIAL| High-level, as expected. Requires UX Expert to create detailed spec. |
| 4. Functional Requirements      | ✅ PASS   | None                                                             |
| 5. Non-Functional Requirements  | ✅ PASS   | None                                                             |
| 6. Epic & Story Structure       | ✅ PASS   | None                                                             |
| 7. Technical Guidance           | ✅ PASS   | Clear constraints provided for Architect.                        |
| 8. Cross-Functional Requirements| ⚠️ PARTIAL| High-level, as expected. Will be detailed during architecture.   |
| 9. Clarity & Communication      | ✅ PASS   | None                                                             |

**Final Decision**
* ✅ **READY FOR ARCHITECT**: The PRD and epics are comprehensive, properly structured, and ready for architectural design.

---

### 7. Next Steps

#### UX Expert Prompt
"Based on the attached PRD, please create a comprehensive UI/UX Specification. Pay close attention to the User Interface Design Goals in Section 3 and the user persona defined in the Project Brief."

#### Architect Prompt
"Based on the attached PRD, please create a comprehensive Fullstack Architecture Document. Pay close attention to the Technical Assumptions in Section 4, which serve as the primary constraints for the design."