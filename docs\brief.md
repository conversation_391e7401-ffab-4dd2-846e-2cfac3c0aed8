# Project Brief: Comprehensive Trading Agent Web Application

### 1. Executive Summary

The proposed project is a specialized web application designed to enhance trading analysis for the **Chinese stock market**. The core of the application is a unique trading strategy that combines the **"Magic Nine Turns"** (神奇九转) technical indicator with **MACD top divergence analysis**. By integrating data from reliable sources like **akshare** and **Tushare**, the platform will provide users with more accurate buy/sell signals, interactive charting tools, and an automated stock screening feature to identify potential trading opportunities based on this combined strategy.

### 2. Problem Statement

Traders and investors in the dynamic Chinese stock market often struggle with the **timing of their sell orders**, primarily due to the unreliability of single technical indicators which can produce false signals. Relying solely on indicators like MACD can lead to premature selling or failing to exit a position before a significant downturn.

The core problem is the **lack of a readily available, automated tool** that combines multiple, sophisticated indicators to produce a higher-confidence signal for identifying price tops. Existing trading platforms may offer standard indicators, but they typically require manual analysis and do not integrate specialized, sequential indicators like "Magic Nine Turns" with divergence analysis in a systematic way. This gap forces traders to either perform complex, time-consuming manual analysis or rely on incomplete data, increasing the risk of financial losses and missed opportunities.

### 3. Proposed Solution

Our proposed solution is a web-based stock analysis and screening platform specifically tailored for the Chinese stock market. The application will automate the execution of a sophisticated trading strategy that **combines the "Magic Nine Turns" indicator with MACD top divergence analysis**.

The key differentiator is the platform's ability to **synthesize these two indicators into a single, actionable buy/sell signal**, removing the need for manual, multi-chart analysis. It will feature a stock analysis interface for on-demand signal generation for specific stocks and a powerful screening tool that proactively scans the market to find stocks currently meeting the strategy's criteria.

This solution will succeed by providing traders with a specialized, high-confidence analytical edge that is not offered by generic trading platforms. By automating a complex and time-consuming analysis process, the application will empower users to make faster, more informed, and potentially more profitable trading decisions.

### 4. Target Users

#### Primary User Segment: The Technically-Minded Retail Trader

* **Profile:** These are active, self-directed retail investors who trade stocks on the Chinese market. They are tech-savvy and possess an intermediate to advanced understanding of technical analysis. They actively manage their own portfolios and are constantly seeking tools that provide a statistical or analytical edge.
* **Current Behaviors:** They spend considerable time each day analyzing stock charts, switching between different platforms to track various indicators. They manually look for confirmations between indicators like MACD and others, a process that is often time-consuming and prone to subjective interpretation.
* **Needs and Pain Points:** Their primary need is for a more reliable, data-driven signal to confirm their trading theses, especially for identifying price tops. Their main pain point is the "analysis paralysis" and uncertainty that comes from conflicting indicators and the manual effort required to track complex strategies across multiple stocks.
* **Goals:** Their ultimate goal is to increase their trading profitability and consistency by making more accurate and timely sell decisions. They want to reduce the emotional and subjective elements of trading by relying on a systematic, automated signal.

### 5. Goals & Success Metrics

#### Business Objectives
* Develop and launch a Minimum Viable Product (MVP) of the trading analysis application within the next 3-4 months.
* Validate the core "Magic Nine Turns + MACD" strategy by achieving a positive signal accuracy rating from a target beta group of at least 25 users.
* Establish a foundational, scalable architecture that can support future feature enhancements and a growing user base.

#### User Success Metrics
* Users report a significant reduction in the time required to perform their daily stock analysis.
* Users express increased confidence in their trading decisions, particularly in identifying price tops.
* The screening tool successfully identifies trading opportunities that users would have otherwise missed.

#### Key Performance Indicators (KPIs)
* **Daily Active Users (DAU):** Track the number of users engaging with the platform daily during the beta phase.
* **Signal Accuracy Score:** A qualitative score (1-10) from beta users rating the perceived accuracy and usefulness of the generated signals. Target: > 7.5/10.
* **User Retention Rate:** Percentage of beta users who use the application at least once a week. Target: 60% weekly retention during the beta period.
* **Screener Usage:** Number of times the stock screening tool is run per day.

### 6. MVP Scope

#### Core Features (Must Have)
* **Single Data Source Integration:** The application will connect to and process data from the **akshare** library as the primary source for Chinese stock market data.
* **Core Strategy Engine:** Backend logic will be implemented to calculate both the "Magic Nine Turns" and "MACD top divergence" indicators and combine them to generate a unified trading signal.
* **Basic Stock Analysis Interface:** A simple, single-page interface where a user can input a valid Chinese stock code and view the resulting signal (e.g., "Top Signal Detected," "Hold," "No Signal") and a basic chart visualizing the indicators.
* **Simple Stock Screener:** An automated daily scan of a predefined, fixed list of major Chinese stocks (e.g., the CSI 300 index). The output will be a simple, non-filterable list of stocks that meet the strategy's criteria.

#### Out of Scope for MVP
* User accounts, login, and personalization features.
* Integration with the secondary Tushare API.
* Advanced sorting, filtering, or customization of the screener results.
* Real-time, streaming data updates (MVP will use near real-time or end-of-day data).
* Portfolio tracking, back-testing, or paper trading capabilities.
* Customizable parameters for the technical indicators.

#### MVP Success Criteria
The MVP will be considered successful when it can consistently ingest data, apply the core strategy without errors, and generate valid signals that the initial beta user group finds useful and sufficiently accurate (achieving the target KPI of > 7.5/10 satisfaction).

### 7. Post-MVP Vision

#### Phase 2 Features
Following a successful MVP, the next phase of development would focus on expanding the core feature set. Priorities would include introducing **user accounts** for personalization and watchlists, enhancing the screener with **advanced filtering and sorting**, integrating **Tushare as a secondary data source** for improved reliability, and developing a **back-testing module** for strategy validation.

#### Long-term Vision
The long-term vision is to establish this application as the premier analytical tool for traders specializing in the Chinese stock market. This includes expanding the library to include **multiple, combinable trading strategies**, offering **premium tiers** with real-time data and advanced analytics, and potentially integrating with brokerage APIs to facilitate **in-platform trading**.

#### Expansion Opportunities
Beyond the core product, several expansion opportunities exist. The underlying strategy engine could be adapted for **other international stock markets** or even different **asset classes** like futures or commodities. Another potential avenue is offering the generated trading signals via a **paid API** for algorithmic traders and other financial platforms.

### 8. Technical Considerations

#### Platform Requirements
* **Target Platforms:** Web application with a responsive design suitable for both desktop and mobile browsers.
* **Performance Requirements:** The system must support near real-time data updates to ensure the timeliness of trading signals.
* **Browser Support:** The application should support the latest versions of major evergreen browsers (Chrome, Firefox, Safari, Edge).

#### Technology Preferences
* **Frontend:** To be finalized (options include React, Vue, or vanilla HTML/JS).
* **Backend:** A **Python backend** (e.g., using FastAPI or Flask) is the recommended choice due to the specified data libraries.
* **Database:** For the MVP, **SQLite is the preferred choice to minimize implementation complexity**. A migration to a more robust production database like **PostgreSQL** will be planned for post-MVP phases.
* **Hosting/Infrastructure:** To be determined.

#### Architecture Considerations
* **Service Architecture:** A monolithic service (a single Python backend application) is likely sufficient for the MVP and Phase 2.
* **Integration Requirements:** The architecture must be designed around the `akshare` and `Tushare` Python libraries.
* **Security:** The Tushare API token must be stored securely on the backend and never exposed to the client.

### 9. Constraints & Assumptions

#### Constraints
* **Budget:** To be determined (TBD).
* **Timeline:** The initial goal is to deliver an MVP within 3-4 months.
* **Resources:** TBD, assumed to be a small development team for the MVP.
* **Technical:** The project is fundamentally constrained by the data availability, accuracy, and API limitations of the `akshare` and `Tushare` libraries.

#### Key Assumptions
* **Data Validity:** We assume the data provided by `akshare` is accurate and timely enough to be effective for the trading strategy.
* **Strategy Efficacy:** We assume the "Magic Nine Turns" + MACD top divergence strategy is statistically valid and can generate valuable, actionable trading signals. The primary purpose of the MVP is to test this assumption.
* **User Sophistication:** We assume the target users are sufficiently knowledgeable in technical analysis to understand the provided signals and the inherent risks of trading.
* **Regulatory Compliance:** We assume that providing this type of automated stock analysis tool is compliant with all relevant financial regulations for the Chinese market.

### 10. Risks & Open Questions

#### Key Risks
* **Data Source Unreliability:** The primary risk is that the `akshare` library could become unavailable, change its API without notice, or provide inaccurate data. **Impact:** This would critically disable the core functionality of the application.
* **Strategy Ineffectiveness:** The combined "Magic Nine Turns + MACD" strategy may not perform as well as anticipated in live market conditions. **Impact:** This would invalidate the application's core value proposition.
* **Performance Bottlenecks:** The stock screening process across hundreds of stocks could be slow or resource-intensive. **Impact:** A poor user experience and potentially high operational costs.

#### Open Questions
* What is the final decision on the frontend framework (e.g., React, Vue)?
* What is the most cost-effective hosting and deployment strategy for a Python backend and a web frontend?
* What is the long-term monetization strategy (e.g., subscription, freemium)?
* What are the precise technical requirements and costs for achieving near real-time data post-MVP?

#### Areas Needing Further Research
* A detailed technical specification for the "Magic Nine Turns" indicator to ensure an accurate implementation.
* A comparative analysis of different algorithms for programmatically detecting MACD divergence.
* A preliminary review of any regulations in the Chinese market regarding automated financial analysis tools.

### 11. Next Steps

#### Immediate Actions
1.  Review and formally approve this Project Brief.
2.  Finalize the choice of frontend framework (e.g., React, Vue, etc.).
3.  Conduct the technical research identified (specifics of "Magic Nine Turns" and MACD divergence algorithms).
4.  Conduct a preliminary review of relevant financial regulations for the Chinese market.
5.  Handoff this brief to the Product Manager to begin creating the detailed Product Requirements Document (PRD).

#### PM Handoff
This Project Brief provides the full context for the Comprehensive Trading Agent Web Application. The Product Manager should now start the next phase, reviewing this brief thoroughly to work with the user to create the PRD section by section, asking for any necessary clarification and suggesting improvements.