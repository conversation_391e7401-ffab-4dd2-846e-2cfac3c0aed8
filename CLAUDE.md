# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

A fullstack Jamstack trading agent web application with React frontend and Python FastAPI backend. Built as a monorepo using npm workspaces and Turborepo for orchestration.

**Key Technology Stack:**
- Frontend: React 18+ with TypeScript, Vite, Tailwind CSS, Zustand state management
- Backend: Python 3.11+ with FastAPI, SQLAlchemy, SQLite
- Testing: Vitest (frontend), <PERSON><PERSON><PERSON> (backend), Playwright (E2E)
- Data Source: akshare library for Chinese stock market data

## Development Commands

### Initial Setup
```bash
npm install                    # Install all workspace dependencies
npm run install-deps          # Install both npm and Python dependencies
```

### Development Servers
```bash
npm run dev                    # Start both frontend (5173) and backend (8000) in parallel
cd apps/web && npm run dev     # Frontend only on localhost:5173
cd apps/api && npm run dev     # Backend only on localhost:8000
```

### Building
```bash
npm run build                  # Build all packages
cd apps/web && npm run build   # Frontend build only (outputs to dist/)
```

### Testing
```bash
npm test                       # Run all tests (unit + integration)
npm run test:e2e              # Run Playwright E2E tests
npm run test:e2e:ui           # Run E2E tests with UI
cd apps/web && npm run test    # Frontend unit tests with Vitest
cd apps/api && npm run test    # Backend tests with Pytest
```

### Code Quality
```bash
npm run lint                   # Lint all packages
npm run type-check            # TypeScript type checking
cd apps/api && npm run lint    # Python linting with Ruff
cd apps/api && npm run type-check  # Python type checking with MyPy
```

### Cleanup
```bash
npm run clean                  # Clean all build artifacts
cd apps/api && npm run clean   # Clean Python cache files
```

## Architecture Overview

### Monorepo Structure
- `apps/web/` - React frontend SPA
- `apps/api/` - Python FastAPI backend
- `packages/shared-types/` - Shared TypeScript type definitions
- `tests/e2e/` - Cross-application E2E tests

### Frontend Architecture (apps/web/)
- **Routing**: React Router with `/` (analysis) and `/screener` routes
- **State Management**: Zustand stores (`analysisStore.ts`, `screenerStore.ts`)
- **UI Components**: Radix UI primitives with custom components in `components/ui/`
- **Features**: Organized by domain (`features/Analysis/`, `features/Screener/`)
- **API Communication**: Centralized in `lib/api.ts` with proxy to backend at `/api/*`
- **Styling**: Tailwind CSS with utility-first approach
- **Path Aliases**: `@/` points to `src/` directory

### Backend Architecture (apps/api/)
- **Entry Point**: `src/main.py` with FastAPI app and CORS middleware
- **Feature Modules**: Domain-organized in `src/features/` (signal, screener, data)
- **API Routes**: Prefixed with `/api` and organized by feature routers
- **Data Source**: akshare library for Chinese stock market data
- **Database**: SQLite with SQLAlchemy ORM
- **Health Checks**: `/` and `/health` endpoints

### Shared Types
- Common interfaces defined in `packages/shared-types/src/index.ts`
- Core types: `SignalResult`, `StockData`, `DailyPrice`, `ScreenerItem`
- Signal types: `'SELL_CANDIDATE' | 'HOLD' | 'NO_SIGNAL'`

### Development Workflow
- **Proxy Setup**: Frontend dev server proxies `/api/*` to backend on port 8000
- **Hot Reload**: Both frontend (Vite) and backend (uvicorn --reload) support hot reload
- **Cross-Origin**: CORS configured for localhost:5173 and 127.0.0.1:5173
- **Parallel Development**: Turborepo manages parallel execution across workspaces

### Testing Strategy
- **Unit Tests**: Vitest for frontend components, Pytest for backend functions
- **Integration Tests**: Backend API testing with httpx client
- **E2E Tests**: Playwright with auto-start of both servers
- **Test Environment**: jsdom for frontend, pytest-asyncio for backend async testing

### Build and Deployment
- **Frontend**: Vite builds to `dist/` with sourcemaps
- **Backend**: Standard Python package with uvicorn ASGI server
- **Target Platforms**: Vercel (frontend), Render (backend) as mentioned in architecture docs
- **Dependencies**: Frontend builds require backend types from shared-types package

## Important Conventions

### File Organization
- Use feature-based organization over technical grouping
- Place tests adjacent to source files (`__tests__/` directories)
- Shared utilities in `lib/` directories within each app
- Component co-location: components, tests, and related files together

### Import Patterns
- Use `@/` alias for frontend absolute imports
- Import shared types from `@trading-agent/shared-types`
- Prefer named imports over default imports for utilities
- Backend uses relative imports within feature modules

### API Design
- RESTful endpoints prefixed with `/api`
- Consistent error handling with `ApiError` and `ApiResponse<T>` types
- Health check endpoints for monitoring
- CORS configured for development origins

### State Management
- Zustand stores for client state (not server state)
- Store files co-located with features that primarily use them
- Separate concerns: UI state vs. data fetching state
- Error handling integrated into store patterns


# This configuration instructs Claude Code to use `uv run python` for executing Python scripts,
# which ensures it uses the Python environment managed by `uv` (virtual environment & dependency manager).

# Avoid running raw `python` commands; always prefix with `uv run`

# Example script command running a Python file
run_script: uv run python my_script.py

# Example inline Python execution command
run_inline: uv run python -c 'print("Hello from uv environment")'

# Example to run a Python module using `uv run`
run_module: uv run python -m my_module

# For other Python commands, prepend `uv run` to ensure the correct Python environment
# Example: uv run python setup.py install