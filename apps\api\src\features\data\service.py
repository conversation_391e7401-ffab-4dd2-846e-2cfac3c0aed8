"""Data ingestion service for fetching stock data from akshare."""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional

import akshare as ak
import pandas as pd

from ...models.stock_data import DailyPrice, StockData

logger = logging.getLogger(__name__)


class DataIngestionConfig:
    """Configuration for data ingestion service."""

    DEFAULT_LOOKBACK_DAYS = 90
    AKSHARE_DATE_FORMAT = '%Y%m%d'
    API_DATE_FORMAT = '%Y-%m-%d'

    # akshare column mapping (by index to avoid Chinese character encoding issues)
    AKSHARE_COLUMNS = {
        'date': 0,
        'symbol': 1,
        'open': 2,
        'close': 3,
        'high': 4,
        'low': 5,
        'volume': 6
    }


class DataIngestionService:
    """Service for fetching and parsing stock data from akshare library."""

    def __init__(self):
        """Initialize the data ingestion service."""
        pass

    def fetch_stock_data(
        self,
        stock_code: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> StockData:
        """
        Fetch historical daily stock data for a given stock code.

        Args:
            stock_code: The stock symbol/code (e.g., "000001")
            start_date: Start date in YYYY-MM-DD format (defaults to 90 days ago)
            end_date: End date in YYYY-MM-DD format (defaults to today)

        Returns:
            StockData: Parsed stock data with daily prices

        Raises:
            ValueError: If stock_code is invalid or data cannot be fetched
            RuntimeError: If akshare service is unavailable
        """
        try:
            # Set default date range if not provided
            if not end_date:
                end_date = datetime.now().strftime(DataIngestionConfig.AKSHARE_DATE_FORMAT)
            else:
                # Convert YYYY-MM-DD to YYYYMMDD for akshare
                end_date = datetime.strptime(end_date, DataIngestionConfig.API_DATE_FORMAT).strftime(DataIngestionConfig.AKSHARE_DATE_FORMAT)

            if not start_date:
                # Default to configured lookback period
                start_dt = datetime.now() - timedelta(days=DataIngestionConfig.DEFAULT_LOOKBACK_DAYS)
                start_date = start_dt.strftime(DataIngestionConfig.AKSHARE_DATE_FORMAT)
            else:
                # Convert YYYY-MM-DD to YYYYMMDD for akshare
                start_date = datetime.strptime(start_date, DataIngestionConfig.API_DATE_FORMAT).strftime(DataIngestionConfig.AKSHARE_DATE_FORMAT)

            logger.info(f"Fetching stock data for {stock_code} from {start_date} to {end_date}")

            # Fetch data from akshare
            raw_data = ak.stock_zh_a_hist(
                symbol=stock_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust=""
            )

            if raw_data is None or raw_data.empty:
                raise ValueError(f"No data found for stock code: {stock_code}")

            # Parse and validate the data
            daily_prices = self._parse_akshare_data(raw_data)

            return StockData(
                symbol=stock_code,
                daily_prices=daily_prices
            )

        except ValueError as e:
            logger.error(f"Invalid stock code or data: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to fetch data from akshare: {e}")
            raise RuntimeError(f"Data service unavailable: {str(e)}")

    def _parse_akshare_data(self, raw_data: pd.DataFrame) -> list[DailyPrice]:
        """
        Parse akshare raw data into DailyPrice objects.

        Args:
            raw_data: Raw DataFrame from akshare

        Returns:
            List of DailyPrice objects sorted by date

        Raises:
            ValueError: If data parsing fails
        """
        try:
            daily_prices = []

            # Use configuration for column mapping
            col_map = DataIngestionConfig.AKSHARE_COLUMNS

            for _, row in raw_data.iterrows():
                # Extract values by column index using configuration
                date_val = row.iloc[col_map['date']]
                open_val = float(row.iloc[col_map['open']])
                close_val = float(row.iloc[col_map['close']])
                high_val = float(row.iloc[col_map['high']])
                low_val = float(row.iloc[col_map['low']])
                volume_val = int(row.iloc[col_map['volume']])

                # Convert date to API format (YYYY-MM-DD)
                if isinstance(date_val, str):
                    # If already string, assume it's in correct format
                    date_str = date_val
                else:
                    # If datetime object, format it using configuration
                    date_str = date_val.strftime(DataIngestionConfig.API_DATE_FORMAT)

                daily_price = DailyPrice(
                    date=date_str,
                    open=open_val,
                    high=high_val,
                    low=low_val,
                    close=close_val,
                    volume=volume_val
                )
                daily_prices.append(daily_price)

            # Sort by date (ascending)
            daily_prices.sort(key=lambda x: x.date)

            logger.info(f"Successfully parsed {len(daily_prices)} daily price records")

            return daily_prices

        except Exception as e:
            logger.error(f"Failed to parse akshare data: {e}")
            raise ValueError(f"Data parsing failed: {str(e)}")
